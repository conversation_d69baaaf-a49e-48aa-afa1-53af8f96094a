package worker

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/worker/cron"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcron"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring/uuid"
	"survey/service"
)

var cronWorker = cron.NewCron(xcron.WithSeconds())

func Get() *cron.Cron {
	return cronWorker
}

func Startup() error {
	// 每1分钟执行一次
	_, err := cronWorker.AddFunc("0 */1 * * * *", handleExportJob)
	if err != nil {
		xlog.Error("crontabs Startup handleExportJob err", xlog.Err(err))
	}
	return nil
}

// nodeJS SurveyExportTaskService.handleCronJob
func handleExportJob() {
	ctx := context.Background()
	version6, _ := uuid.NewVersion6()
	ctx = xlog.NewContext(ctx, xlog.L().With(xlog.RequestID(version6.String())))
	err := service.HandleExportJob(ctx)
	if err != nil {
		xlog.Error("handleExportJob service.HandleExportJob has err", xlog.Err(err))
		return
	}
}
