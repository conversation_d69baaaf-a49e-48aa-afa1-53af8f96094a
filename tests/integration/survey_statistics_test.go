package integration

import (
	"encoding/json"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"net/http"
	"survey/proto"
	"testing"
)

func TestStatisticsUpdate(t *testing.T) {
	Convey("test TestStatisticsUpdate", t, func(c C) {
		p := proto.StatisticsUpdateRequest{
			Id:       1277,
			ClientId: 1106,
			Name:     "【官方】国服CBT2满意度问卷II_update_name",
			//Name:     "【官方】国服CBT2满意度问卷II_update",
			//Settings: "{\"baseRuleConfig\":{\"loginType\":\"0\",\"timeLimitConfig\":{},\"isGoOnAnswer\":true,\"answerTimesConfig\":{\"limitType\":1,\"times\":1},\"languageList\":[\"zh-cn\"],\"deliverList\":[\"大陆\"]},\"giftConfig\":{\"isGiveOutByCms\":true,\"giveOutType\":\"2\"},\"answerLimitConfig\":{},\"materialsConfig\":{\"autoLatestMaterial\":true}}",
			//Stime:    "",
			//Etime:    "",
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/rule/update").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)

		requestObjectExpect(r, true)
	})
}

func TestGetInputMethodList(t *testing.T) {
	Convey("test TestGetInputMethodList", t, func(c C) {
		p := proto.SurveyInputMethodListRequest{
			Page:     1,
			PageSize: 10,
			SurveyId: 662,
			RequestConfig: &proto.RequestConfig{
				QuestionUniqueKey: "1ZQDaLwoQZ",
				ConfigProps: &proto.ConfigProps{
					IsQuestion:       true,
					QuestionType:     "",
					ValueType:        "",
					UniqueKey:        "",
					QuestionId:       0,
					StatisticsMethod: "",
					IsAddress:        false,
				},
				OptionValue: "BwFI4yOd-x",
			},
			ClientId:        1106,
			FilterNullValue: 1,
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/statistics/detail/input-method-list").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)

		requestObjectExpect(r, false)
	})
}
