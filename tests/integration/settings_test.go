package integration

import (
	"encoding/json"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"net/http"
	proto2 "survey/proto"
	"testing"
)

func TestGetValidRedeemConfigList(t *testing.T) {
	Convey("test TestGetValidRedeemConfigList", t, func(c C) {
		req := &proto2.ValidRedeemConfigRequest{
			Page:     1,
			PageSize: 1,
			ClientId: 2008,
		}
		queryObj, err := structToMap(req)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			GET("/v1/survey/settings/get-valid-redeem-config-list").
			WithQueryObject(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		So(err, ShouldBeNil)
		requestObjectExpect(r, false)
	})
}

func TestGetPreAwardTemplateList(t *testing.T) {
	Convey("test TestGetPreAwardTemplateList", t, func(c C) {
		req := &proto2.PreAwardTemplateRequest{
			Page:     1,
			PageSize: 1,
			ClientId: 2008,
		}
		queryObj, err := structToMap(req)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			GET("/v1/survey/settings/get-pre-award-template-list").
			WithQueryObject(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		So(err, ShouldBeNil)
		requestObjectExpect(r, false)
	})
}
