package integration

import (
	"context"
	"encoding/json"
	"net/http"
	"survey/constants"
	"survey/proto"
	"survey/service"
	"testing"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestStatisticsDetail(t *testing.T) {
	// 概览
	Convey("test statistics detail", t, func(c C) {
		p := proto.SurveyRequest{
			//SurveyId: 662,  // 关联问题demo
			//ClientId: 1106, // 关联问题demo
			//SurveyId: 1297, //Test 数据源
			//SurveyId: 1403,
			//ClientId: 2008,
			SurveyId: 1284, // dev
			ClientId: 1008, // dev
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/statistics/detail").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)

		requestObjectExpect(r)
		data := r.Value("data").Object()
		data.Value("created_time").NotNull()
		data.Value("updated_time").NotNull()
	})
}

func TestSurveyExportTask(t *testing.T) {
	// 导出任务
	Convey("TestSurveyExportTask", t, func(c C) {
		ctx := context.Background()
		err := service.HandleExportJob(ctx)
		if err != nil {
			t.Logf("TestSurveyExportTask err:%+v", err)
		} else {
			t.Log("success")
		}
	})
}

func TestSurveyViewCreate(t *testing.T) {
	// 筛选器视图 - 创建
	Convey("test SurveyViewCreate", t, func(c C) {
		content := `{"content_view":{"operator":"&&","conditions":[{"operator":"==","lhs":"r.survey_record_id","rhs":"1"},{"operator":">","lhs":"_field_duration_","rhs":"1"}]}}`
		p := proto.SurveyViewCreateReq{
			SurveyId: 1,
			Name:     "筛选器7",
			Content:  content,
			Kind:     constants.Survey_View_Kind_List,
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/view/create").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)

		requestObjectExpect(r)
		data := r.Value("data").Object()
		data.Value("id").IsNumber()
	})
}

func TestSurveyViewList(t *testing.T) {
	// 筛选器 - 列表
	Convey("test TestSurveyViewList", t, func(c C) {
		r := expect.Build(c).
			GET("/v1/survey/view/list").
			WithQuery("survey_id", 1).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
		So(err, ShouldBeNil)
	})
}

func TestSurveyViewUpdate(t *testing.T) {
	// 筛选器 - 更新
	Convey("test TestSurveyViewUpdate", t, func(c C) {
		content := `{"operator":"&&","conditions":[{"operator":"==","lhs":"d.question","rhs":"'OKyW8jT_yz_22'"},{"operator":"wildcard","lhs":"d.text","rhs":"'*abc*'"}]}`
		p := proto.SurveyView{
			Id:      1,
			Name:    "筛选器_编辑test",
			Content: content,
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/view/update").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyViewDelete(t *testing.T) {
	// 筛选器 - 删除
	Convey("test TestSurveyViewDelete", t, func(c C) {
		p := proto.SurveyViewDeleteReq{
			Ids: []int64{1},
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/view/delete").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyExportQueueCreate(t *testing.T) {
	// 导出任务 - 创建
	Convey("test TestSurveyExportQueueCreate", t, func(c C) {
		p := proto.SurveyExportTask{
			ClientId:   1106,
			SurveyId:   762,
			FileType:   0,
			DataType:   1,
			IsValid:    2,
			DataSource: 1,
			DataNum:    5,
			//ViewContent: "{\"operator\":\"&&\",\"conditions\":[{\"operator\":\"==\",\"lhs\":\"d.question\",\"rhs\":\"'OKyW8jT_yz'\"},{\"operator\":\"wildcard\",\"lhs\":\"d.text\",\"rhs\":\"'*abc*'\"}]}",
		}
		// 用户分群
		p = proto.SurveyExportTask{
			ClientId: 1087,
			SurveyId: 1679,
			FileType: 0,
			DataType: 1,
			IsValid:  2,
			Extra:    "{\"user_cluster\":[{\"cluster_info\":{\"cluster_name\":\"jp_user_cluser_test02\",\"entity_name\":\"vroleid\",\"version\":\"\"},\"relation\":1}]}",
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/statistics/user-answer-record/survey-export/create").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyRecordListV2(t *testing.T) {
	// 问卷 - 获取回收列表v2
	Convey("test TestSurveyRecordListV2", t, func(c C) {
		p := proto.SurveyRecordListV2Request{
			SurveyId: 1284,
			ClientId: 1008,
			//ViewContent: "{\"operator\":\"&&\",\"conditions\":[{\"operator\":\"==\",\"lhs\":\"r.survey_record_id\",\"rhs\":\"1\"},{\"operator\":\">\",\"lhs\":\"_field_duration_\",\"rhs\":\"1\"},{\"operator\":\"==\",\"lhs\":\"r.ctime\",\"rhs\":\"'2024-08-20 10:31:22'\"},{\"operator\":\"==\",\"lhs\":\"r.openid\",\"rhs\":\"'202669669'\"},{\"operator\":\"==\",\"lhs\":\"r.roleid\",\"rhs\":\"'9013276242'\"},{\"operator\":\"==\",\"lhs\":\"d.question\",\"rhs\":\"'OKyW8jT_yz'\"},{\"operator\":\"wildcard\",\"lhs\":\"d.text\",\"rhs\":\"'*abc*'\"}]}",
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/statistics/user-answer-record/list-v2").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyRecordList(t *testing.T) {
	// 问卷 - 获取回收列表v2
	Convey("test TestSurveyRecordListV2", t, func(c C) {
		p := proto.SurveyRecordListV2Request{
			SurveyId: 1284,
			ClientId: 1008,
			//ViewContent: "{\"operator\":\"&&\",\"conditions\":[{\"operator\":\"==\",\"lhs\":\"r.survey_record_id\",\"rhs\":\"1\"},{\"operator\":\">\",\"lhs\":\"_field_duration_\",\"rhs\":\"1\"},{\"operator\":\"==\",\"lhs\":\"r.ctime\",\"rhs\":\"'2024-08-20 10:31:22'\"},{\"operator\":\"==\",\"lhs\":\"r.openid\",\"rhs\":\"'202669669'\"},{\"operator\":\"==\",\"lhs\":\"r.roleid\",\"rhs\":\"'9013276242'\"},{\"operator\":\"==\",\"lhs\":\"d.question\",\"rhs\":\"'OKyW8jT_yz'\"},{\"operator\":\"wildcard\",\"lhs\":\"d.text\",\"rhs\":\"'*abc*'\"}]}",
			Page:     1,
			PageSize: 2,
		}
		b, err := json.Marshal(&p)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/statistics/user-answer-record/list").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyList(t *testing.T) {
	// 问卷 - 列表
	Convey("test TestSurveyList", t, func(c C) {

		req := &proto.SurveyListRequest{
			Page:     1,
			PageSize: 10,
			ClientId: 2008,
		}
		queryObj, err := structToMap(req)
		r := expect.Build(c).
			GET("/v1/survey/list").
			WithQueryObject(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupCreate(t *testing.T) {
	// 问卷组 - 创建
	Convey("test TestSurveyGroupCreate", t, func(c C) {
		settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb","default":true},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有默认值
		//settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有survey_id
		//settings := `{"group_survey":{"zh-cn":{"survey_id":0,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有survey_id
		//settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":""},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		req := &proto.SurveyGroupCreateReq{
			ClientId:  2008,
			Name:      "问卷组名字",
			LimitType: 3,
			Type:      1,
			Settings:  settings,
		}

		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/create").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupUpdate(t *testing.T) {
	// 问卷组 - 编辑
	Convey("test TestSurveyGroupUpdate", t, func(c C) {
		settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb","default":true},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有默认值
		//settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有survey_id
		//settings := `{"group_survey":{"zh-cn":{"survey_id":0,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		req := &proto.SurveyGroupUpdateReq{
			ClientId:  2008,
			Id:        4,
			Name:      "问卷组名字-新2",
			LimitType: 2,
			Type:      2,
			Settings:  settings,
		}

		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/update").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupDetail(t *testing.T) {
	// 问卷组 - 详情
	Convey("test TestSurveyGroupUpdate", t, func(c C) {
		req := &proto.SurveyGroupDetailReq{
			ClientId: 2008,
			Id:       2,
		}
		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/detail").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, false)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupSubUpdate(t *testing.T) {
	// 问卷组 - 部分字段更新
	Convey("test TestSurveyGroupSubUpdate", t, func(c C) {
		// 发布
		//req := &proto.SurveyGroupSubUpdateReq{
		//	ClientId: 2008,
		//	Id:       2,
		//	Type:     proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Publish,
		//	Value:    "1",
		//}
		//settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb","default":true},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有默认值
		//settings := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb","default":true},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		// 校验没有survey_id
		settings := `{"group_survey":{"zh-cn":{"survey_id":0,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
		req := &proto.SurveyGroupSubUpdateReq{
			ClientId: 2008,
			Id:       1,
			Type:     proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Settings,
			Value:    settings,
		}
		// 删除
		//req := &proto.SurveyGroupSubUpdateReq{
		//	ClientId: 2008,
		//	Id:       2,
		//	Type:     proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Delete,
		//	Value:    "1",
		//}
		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/sub_update").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupList(t *testing.T) {
	// 问卷组 - 列表
	Convey("test TestSurveyGroupList", t, func(c C) {
		req := &proto.SurveyGroupListReq{
			ClientId: 2008,
			//Id:       2,
		}
		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/list").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyOverwriteSend(t *testing.T) {
	Convey("test TestSurveyOverwriteSend", t, func(c C) {
		req := &proto.SurveyOverwriteSendReq{
			ClientId: 2008,
			SurveyId: 1348,
		}
		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/overwrite/send").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyGroupOverwriteSend(t *testing.T) {
	Convey("test TestSurveyGroupOverwriteSend", t, func(c C) {
		req := &proto.SurveyGroupOverwriteSendReq{
			ClientId:      2008,
			SurveyGroupId: 23,
		}
		queryObj, err := json.Marshal(req)
		r := expect.Build(c).
			POST("/v1/survey/group/overwrite/send").
			WithBytes(queryObj).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r, true)
		So(err, ShouldBeNil)
	})
}

func TestSurveyExportUserClusterSubmit(t *testing.T) {
	Convey("test SurveyExportUserClusterSubmit", t, func(c C) {
		// 用户分群
		req := proto.SurveyExportUserClusterSubmitReq{
			ClientId: 1087,
			UserStrategy: &proto.UserStrategy{
				ClusterName: "jp_user_cluser_test02",
				EntityName:  "vroleid",
				Version:     "",
			},
		}
		b, err := json.Marshal(&req)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/export/user-cluster-submit").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyExportHeaders(t *testing.T) {
	Convey("test SurveyExportHeaders", t, func(c C) {
		req := proto.SurveyExportHeadersReq{
			ClientId: 1087,
			SurveyId: 1678,
		}
		b, err := json.Marshal(&req)
		So(err, ShouldBeNil)
		r := expect.Build(c).
			POST("/v1/survey/export/headers").
			WithBytes(b).WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%+v", bs)
		requestObjectExpect(r)
	})
}

func TestSurveyInvalidList(t *testing.T) {
	Convey("test TestSurveyViewList", t, func(c C) {
		r := expect.Build(c).
			GET("/v1/survey/statistics/user-answer-record/invalid-list").
			WithQuery("survey_id", 1681).
			WithQuery("client_id", 2008).
			WithCookie("gos_auth_key", Cookie).
			Expect().
			Status(http.StatusOK).
			JSON().Object()

		bs, err := json.Marshal(r.Raw())
		t.Logf("data:%s", bs)
		requestObjectExpect(r)
		So(err, ShouldBeNil)
	})
}
