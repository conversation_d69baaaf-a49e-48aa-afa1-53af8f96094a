package integration

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/testing/xhttpexpect"
	"net/http"
	"os"
	"survey/broker"
	"survey/config"
	"survey/database"
	_ "survey/model"
	"survey/server"
	"survey/service"
	"sync"
	"testing"
	"time"
)

var expect = xconvey.NewExpector(server.Get().GetGinEngine())

const Cookie = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXIiOiJodHRwczovL3MxLWltZmlsZS5mZWlzaHVjZG4uY29tL3N0YXRpYy1yZXNvdXJjZS92MS92Ml9jODNjYmEyMy0wNDUzLTQzYTUtOGFiMi0zNGJmZmZhNTNiNmd-P2ltYWdlX3NpemU9bm9vcFx1MDAyNmN1dF90eXBlPVx1MDAyNnF1YWxpdHk9XHUwMDI2Zm9ybWF0PXBuZ1x1MDAyNnN0aWNrZXJfZm9ybWF0PS53ZWJwIiwiZXhwIjo0MzIxNTY4OTA1LCJuaWNrbmFtZSI6IuWHjOmbsiIsInVzZXJJZCI6IjE2OTIxNTg3NzU1MDg4NTI4ODYiLCJ1c2VybmFtZSI6Imxpbmd5dW4ifQ.Fnv2sUFA0Qd_czIXqJ1N8dsYTX2eYCX_ofD1tBQvCE8"

func TestMain(m *testing.M) {
	// _, filename, _, _ := runtime.Caller(0)
	// conf := path.Join(path.Dir(filename), "docker-compose.yaml")
	// compose.Setup(conf)
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		broker.Startup,
		service.Startup,
		server.Startup,
	).Server(server.Get())
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()
	waitReady(app)
	fmt.Println("TestMain start")
	exitCode := m.Run()
	app.Stop()
	wg.Wait()
	fmt.Println("TestMain done")
	// compose.Down()
	os.Exit(exitCode)
}
func waitReady(app *sparrow.Application) {
	app.WaitReady(context.Background())
	for i := 0; i < 30; i++ {
		resp, err := http.Get("http://" + config.Get().Host + "/v1/survey/health")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			return
		}
		time.Sleep(time.Millisecond * 100)
	}
	panic("waiting too long")
}
func requestObjectExpect(obj *xhttpexpect.Object, withoutData ...bool) {
	var keys []interface{}
	if len(withoutData) != 0 && withoutData[0] {
		keys = []interface{}{"code", "info", "request_id"}
	} else {
		keys = []interface{}{"code", "info", "request_id", "data"}
	}
	obj.Keys().ContainsOnly(keys...)
	obj.Value("code").IsEqual(0)
	obj.Value("info").IsEqual("OK")
	obj.Value("request_id").NotNull()
}

func structToMap(s interface{}) (map[string]interface{}, error) {
	bs, err := sonic.Marshal(s)
	if err != nil {
		return nil, fmt.Errorf("structToMap sonic.Marshal err :%+v", err)
	}
	var data map[string]interface{}
	err = sonic.Unmarshal(bs, &data)
	if err != nil {
		return nil, fmt.Errorf("structToMap sonic.Unmarshal err :%+v", err)
	}
	return data, nil
}
