package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"survey/database"
	"survey/model"
	proto2 "survey/proto"
	"survey/service"
	"survey/types"
	"survey/util/base"
	"sync"
	"testing"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
)

func TestName(t *testing.T) {
	result := types.SurveyQuestionStatsWithExtra{
		//ExtraData:   extraData,
		Count:       1,
		SelectCount: int(2),
		//Detail:      statDetail,
		UniqueKey: "uniqueKey",
	}

	//result.ExtraData = types.ExtraData{}

	bs, _ := json.Marshal(result)
	t.Logf("-=-=-=-=-=-%s", bs)
}

func TestUnmarshalSchema(t *testing.T) {
	//schema := `{"version":"1.0.0","componentsMap":[{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"QuestionTitle","main":"","destructuring":true,"subName":"","componentName":"QuestionTitle"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"QuestionDesc","main":"","destructuring":true,"subName":"","componentName":"QuestionDesc"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"QuestionCheckbox","main":"","destructuring":true,"subName":"","componentName":"QuestionCheckbox"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"QuestionRadiosMatrix","main":"","destructuring":true,"subName":"","componentName":"QuestionRadiosMatrix"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"QuestionWrapper","main":"","destructuring":true,"subName":"","componentName":"QuestionWrapper"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"SurveyWrapper","main":"","destructuring":true,"subName":"","componentName":"SurveyWrapper"},{"devMode":"lowCode","componentName":"Page"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"LegalProvisions","main":"","destructuring":true,"subName":"","componentName":"LegalProvisions"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"NonSurveyWrapper","main":"","destructuring":true,"subName":"","componentName":"NonSurveyWrapper"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"SurveyCompleted","main":"","destructuring":true,"subName":"","componentName":"SurveyCompleted"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"SurveyStopped","main":"","destructuring":true,"subName":"","componentName":"SurveyStopped"},{"package":"question-survey-materials","version":"0.1.128-alpha.6","exportName":"SurveyNotFound","main":"","destructuring":true,"subName":"","componentName":"SurveyNotFound"}],"componentsTree":[{"componentName":"Page","id":"node_ocl5jke2bq1","docId":"docl5jke2bq1","props":{},"meta":{"title":"问卷","router":"/"},"fileName":"index","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","level":0,"children":[{"componentName":"SurveyWrapper","id":"node_ocl5jke2bq2","docId":"docl5jke2bq","props":{"spacing":40,"wrapperMainTitle":"未命名问卷","wrapperSubTitle":"问卷为了给您提供更好的服务，希望您能抽出几分钟时间，将您的感受和建议告诉我们，我们非常重视每位用户的宝贵意见，期待您的参与！现在我们就马上开始吧","showQuestionSerialNumber":true,"skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","answerQuestionProcessCanBack":false,"surveySettings":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","showQuestionSerialNumber":true,"answerQuestionProcessCanBack":false},"questionWrapperActiveKey":2,"engineIsInit":true,"settingsProps":{"baseRuleConfig":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true,"timeLimitConfig":{"isTimeLimit":true,"stimeConfig":{"isSet":true,"time":"2022-12-03 17:14:43","promptMsg":""},"etimeConfig":{"isSet":false,"time":null,"promptMsg":""},"stime":"2023-02-01 14:34:19","etime":"2023-02-05 14:34:24"}},"skinConfig":{"isCustomSkinUrl":"1","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","bgColor":"#d53535"},"materialsConfig":{"materialVersion":"0.1.128-alpha.6","autoLatestMaterial":true},"giftConfig":{"isGiveOutByCms":true,"giveOutType":"0","redeemConfig":null,"preAwardConfig":null},"engineIsInit":true}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_ocl5jke2bq1"},"index":0,"children":[{"componentName":"QuestionWrapper","id":"node_oclcr3ataf4g","docId":"docl6bxujdy","props":{"spacing":40},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_ocl5jke2bq2","index":0},"index":0,"children":[{"componentName":"QuestionTitle","id":"node_oclcr3ataf4h","docId":"docl9s45g0r","props":{"question_component_config":{"desc":"1111"},"configProps":{"isQuestion":false,"uniqueKey":"YRvBBVJXBC"},"extraProps":{},"question_logical_config":{}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_oclcr3ataf4g","index":0},"index":0},{"componentName":"QuestionDesc","id":"node_oclcr3ataf4i","docId":"docl9s45g0r","props":{"question_component_config":{"desc":"问卷为了给您提供更好的服务，希望您能抽出几分钟时间，将您的感受和建议告诉我们，我们非常重视每位用户的宝贵意见，期待您的参与！现在我们就马上开始吧"},"configProps":{"isQuestion":false,"uniqueKey":"gojJMwWj46"},"extraProps":{},"question_logical_config":{}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_oclcr3ataf4g","index":0},"index":1},{"componentName":"QuestionCheckbox","id":"node_oclcr3ataf4j","docId":"doclcfwdqig","props":{"question_base_config":{"question_title":"1111","question_desc":"请输入题目描述","question_tip":""},"question_select_config":{"select_item_layout_horizontal":false,"select_item_random_sort":false,"select_options":[{"value":"LRtiGGoB","label":"111"},{"value":"YTLvh5_u","label":"222"},{"value":"oKHG9Yud","image":"","label":"333"},{"value":"wr3dKpXa","label":"444"},{"value":"QAgiUn5h","label":"555"}],"fill_free_config":{"show":true,"label":"ddd","value":"r3VIfUbD"}},"question_check_config":{"required":false},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"Ko-b9Rl8gS"},"extraProps":{},"question_component_config":{"mutex":[]},"question_logical_config":{"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_oclcr3ataf4g","index":0},"index":2},{"componentName":"QuestionRadiosMatrix","id":"node_oclcykmpb13a","docId":"doclcykmpb1","props":{"question_base_config":{"question_title":"请输入题目标题","question_desc":"请输入题目描述","question_tip":""},"question_select_config":{"question_title":"","row_title_select_options":[{"value":"xrVBWOhkbi","label":"11"},{"value":"iS0BfzR0jU","label":"22"}],"select_options":[{"value":"rg7ngFFW","label":"aa"},{"value":"cWUwP61W","label":"bb"},{"value":"6d6n9Hpc","label":"cc"}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"isMatrix":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"vJxb95FVDX"},"extraProps":{}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_oclcr3ataf4g","index":0},"index":3},{"componentName":"QuestionCheckbox","id":"node_oclcr3ataf4m","docId":"doclcr3ataf","props":{"question_base_config":{"question_title":"4444","question_desc":"请输入题目描述","question_tip":""},"question_select_config":{"select_item_layout_horizontal":false,"select_item_random_sort":false,"select_options":[{"value":"CBTbwXto","label":"1"},{"value":"o8hOiC8w","label":"2"},{"value":"ZLsredj8","label":"3"},{"value":"sYaORD3O","label":"4"},{"value":"TSNVErzF","label":"5"}],"fill_free_config":{"show":false,"value":"eqKPg-o6","label":""}},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"fFeGcvFSFw"},"extraProps":{},"question_logical_config":{"display_logic":{"currentQuestionId":"fFeGcvFSFw","rules":[],"relation":"1"},"options_reference":{"currentQuestionId":"fFeGcvFSFw","relationQuestionId":"Ko-b9Rl8gS","relationSelectOptionsConfig":[{"value":"BzRiefdX","label":"11","newValue":"3nA7RALX84"},{"value":"PA5K2_Uf","image":"","label":"22","newValue":"LfktMJTWzz"},{"value":"oKHG9Yud","image":"","label":"33","newValue":"uFHaAS1DX4"}]}},"question_component_config":{"mutex":[]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","parent":{"id":"node_oclcr3ataf4g","index":0},"index":4}]}]}]},{"componentName":"Page","id":"node_ocl5jke2bq4","docId":"docl5jke2bq4","props":{},"meta":{"title":"法务法规页","router":"/legal"},"fileName":"legal","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"NonSurveyWrapper","id":"node_ocl9mki4sm1","docId":"docl9mki4sm","props":{"settingsProps":{"baseRuleConfig":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true,"timeLimitConfig":{"isTimeLimit":true,"stimeConfig":{"isSet":true,"time":"2022-12-03 17:14:43","promptMsg":""},"etimeConfig":{"isSet":false,"time":null,"promptMsg":""},"stime":"2023-02-01 14:34:19","etime":"2023-02-05 14:34:24"}},"skinConfig":{"isCustomSkinUrl":"1","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","bgColor":"#d53535"},"materialsConfig":{"materialVersion":"0.1.128-alpha.6","autoLatestMaterial":true},"giftConfig":{"isGiveOutByCms":true,"giveOutType":"0","redeemConfig":null,"preAwardConfig":null},"engineIsInit":true},"question_component_config":{"show_skin":true}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"QuestionTitle","id":"node_oclbnc3x3j3s","docId":"doclbnc3x3j","props":{"question_component_config":{"desc":"未命名问卷"},"configProps":{"isQuestion":false},"extraProps":{}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""},{"componentName":"LegalProvisions","id":"node_oclbnc3x3j1v","docId":"doclbnc3x3j","props":{"question_component_config":{"richtext":"富文本内容","external":false,"suvery_router":"/"},"configProps":{"isQuestion":false,"valueType":"string"}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"Page","id":"node_ocl5jke2bq2","docId":"docl5jke2bq2","props":{},"meta":{"title":"完成答题","router":"/completed"},"fileName":"completed","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"NonSurveyWrapper","id":"node_ocl5jke2bq9","docId":"ocl5jke2bq9","props":{"settingsProps":{"baseRuleConfig":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true,"timeLimitConfig":{"isTimeLimit":true,"stimeConfig":{"isSet":true,"time":"2022-12-03 17:14:43","promptMsg":""},"etimeConfig":{"isSet":false,"time":null,"promptMsg":""},"stime":"2023-02-01 14:34:19","etime":"2023-02-05 14:34:24"}},"skinConfig":{"isCustomSkinUrl":"1","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","bgColor":"#d53535"},"materialsConfig":{"materialVersion":"0.1.128-alpha.6","autoLatestMaterial":true},"giftConfig":{"isGiveOutByCms":true,"giveOutType":"0","redeemConfig":null,"preAwardConfig":null},"engineIsInit":true}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyCompleted","id":"node_oclbnc3x5e1d","docId":"doclbnc3x5e","props":{"question_component_config":{"message":"请输入描述说明","image":"https://cmscdn.papegames.com/cmssurvey/static/55724be1cc3e908514256cc0e1a27eb3.png","messages":{"no_award":"请输入","email_award":"请输入","award_cdkey":"请输入","preview_type":"no_award"}}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"Page","id":"node_ocl5jke2bq3","docId":"docl5jke2bq3","props":{},"meta":{"title":"停止答题","router":"/stopped"},"fileName":"stopped","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"NonSurveyWrapper","id":"node_ocl5jke2bq6","docId":"ocl5jke2bq6","props":{"settingsProps":{"baseRuleConfig":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true,"timeLimitConfig":{"isTimeLimit":true,"stimeConfig":{"isSet":true,"time":"2022-12-03 17:14:43","promptMsg":""},"etimeConfig":{"isSet":false,"time":null,"promptMsg":""},"stime":"2023-02-01 14:34:19","etime":"2023-02-05 14:34:24"}},"skinConfig":{"isCustomSkinUrl":"1","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","bgColor":"#d53535"},"materialsConfig":{"materialVersion":"0.1.128-alpha.6","autoLatestMaterial":true},"giftConfig":{"isGiveOutByCms":true,"giveOutType":"0","redeemConfig":null,"preAwardConfig":null},"engineIsInit":true}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyStopped","id":"node_oclbnc3x6t2n","docId":"doclbnc3x6t","props":{"question_component_config":{"messages":{"not_start":"请输入","closed":"请输入","pause":"请输入","ip_limited":"请输入","repeat":"请输入","limited":"请输入","preview_type":"not_start"},"image":"https://cmscdn.papegames.com/cmssurvey/static/98211dc28fd3a686e5ad760d5cb03306.png"}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"Page","id":"node_ocMi8_K3dA-","docId":"docMi8_K3dA-","props":{},"hidden":false,"title":"404","isLocked":false,"condition":true,"conditionGroup":"","fileName":"not_found","meta":{"title":"404","router":"/not_found"},"children":[{"componentName":"NonSurveyWrapper","id":"node_ocEHrUSpAcO","docId":"docEHrUSpAcO","props":{"settingsProps":{"baseRuleConfig":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true,"timeLimitConfig":{"isTimeLimit":true,"stimeConfig":{"isSet":true,"time":"2022-12-03 17:14:43","promptMsg":""},"etimeConfig":{"isSet":false,"time":null,"promptMsg":""},"stime":"2023-02-01 14:34:19","etime":"2023-02-05 14:34:24"}},"skinConfig":{"isCustomSkinUrl":"1","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png","bgColor":"#d53535"},"materialsConfig":{"materialVersion":"0.1.128-alpha.6","autoLatestMaterial":true},"giftConfig":{"isGiveOutByCms":true,"giveOutType":"0","redeemConfig":null,"preAwardConfig":null},"engineIsInit":true}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyNotFound","id":"node_oclbnhi7a915","docId":"doclbnhi7a9","props":{"question_component_config":{"image":"https://cmscdn.papegames.com/cmssurvey/static/3f2b5ceb8157f153f0447febedb4eeb1.png","message":"您要查看的网页暂时不可访问，请刷新一下或稍后进行尝试～"}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]}],"i18n":{},"config":{"historyMode":"hash","targetRootID":"J_Container","layout":{"componentName":null,"props":{"logo":"...","name":"问卷调查"}}},"meta":{"name":"问卷调查","project_name":"问卷调查","description":"问卷调查","spma":"spa23d","creator":"moling"}}`
	schema := `{"version":"1.0.0","componentsMap":[{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"QuestionTitle","main":"","destructuring":true,"subName":"","componentName":"QuestionTitle"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"QuestionRadio","main":"","destructuring":true,"subName":"","componentName":"QuestionRadio"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"QuestionCheckbox","main":"","destructuring":true,"subName":"","componentName":"QuestionCheckbox"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"QuestionInput","main":"","destructuring":true,"subName":"","componentName":"QuestionInput"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"QuestionWrapper","main":"","destructuring":true,"subName":"","componentName":"QuestionWrapper"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"SurveyWrapper","main":"","destructuring":true,"subName":"","componentName":"SurveyWrapper"},{"devMode":"lowCode","componentName":"Page"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"LegalProvisions","main":"","destructuring":true,"subName":"","componentName":"LegalProvisions"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"SurveyCompleted","main":"","destructuring":true,"subName":"","componentName":"SurveyCompleted"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"SurveyStopped","main":"","destructuring":true,"subName":"","componentName":"SurveyStopped"},{"package":"cms-survey-materials","version":"0.2.7-alpha.3","exportName":"SurveyNotFound","main":"","destructuring":true,"subName":"","componentName":"SurveyNotFound"}],"componentsTree":[{"componentName":"Page","id":"node_ocl5jke2bq1","docId":"docl5jke2bq1","props":{},"meta":{"title":"问卷","router":"/"},"fileName":"index","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyWrapper","id":"node_oclik6qswq1","docId":"doclik6qswq","props":{"wrapperMainTitle":"问卷标题","wrapperSubTitle":"为了给您提供更好的服务，希望您能抽出几分钟时间，将您的感受和建议告诉我们，我们非常重视每位用户的宝贵意见，期待您的参与！现在我们就马上开始吧!副标题","surveyBaseRule":{"showQuestionSerialNumber":true,"answerQuestionProcessCanBack":true},"skinConfig":{"showSkin":true,"skin":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png"},"bgColor":""},"settingsProps":{"baseRuleConfig":{"loginType":"0","timeLimitConfig":{"isTimeLimit":false},"isEndPreview":false,"isGoOnAnswer":true,"answerTimesConfig":{"limitType":1,"times":1},"languageList":["zh-tw"],"deliverList":["港澳台"]},"giftConfig":{"isGiveOutByCms":false,"giveOutType":"","preAwardConfig":null,"redeemConfig":null},"answerLimitConfig":{"limitType":""},"zoneIds":[5,15],"materialsConfig":{"autoLatestMaterial":true,"materialVersion":"0.2.7-alpha.3"},"footerConfig":{"url":"https://assets.papegames.com/umd/deepspace-footer-other/index.min.js","name":"deepspace-footer-other"},"sourceConfig":{"citysUrl":"","agreements":[]}},"clientid":"1067"},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"children":[{"componentName":"QuestionWrapper","id":"node_oclik6qswq14","docId":"doclik6qswq","props":{"spacing":40},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{"index":0},"children":[{"componentName":"QuestionTitle","id":"node_oclik6qswq2n","docId":"doclik6qswq","props":{"question_component_config":{"desc":{"languKey":"main_title","value":"【戀與深空】遊戲體驗問卷調查"}},"configProps":{"isQuestion":false,"uniqueKey":"QtZXfEFT9r"},"extraProps":{},"question_logical_config":{}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233a","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您的性別？</p>","languKey":"PJ7EOSwtZH"},"question_desc":{"languKey":"NyZke6_J7J","value":""},"question_tip":{"languKey":"OMQi_0O0JX","value":""}},"question_select_config":{"select_options":[{"value":"V5mMdNQ7","label":{"value":"女生","languKey":"QyFHp8WRgn"},"image_position":0,"blank_fill":"","image":{"languKey":"jZzo2tDn9y","value":""}},{"value":"0OkBqc6o","label":{"value":"男生","languKey":"jrGeS_j9WR"},"image_position":0,"blank_fill":"","image":{"languKey":"JxIbVX5QFD","value":""}},{"value":"LeEfnLNO","label":{"value":"其他","languKey":"P0IWwcYU5L"},"image_position":0,"blank_fill":"","image":{"languKey":"wsx9kw7Pfl","value":""}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"lbWvFU8fxN"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":"","display_logic":{},"options_reference":{}}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":2,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233e","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您的年齡?</p>","languKey":"gjog_dTlCi"},"question_desc":{"languKey":"HtDfP3PrM1","value":""},"question_tip":{"languKey":"TqVmLId8q6","value":""}},"question_select_config":{"select_options":[{"value":"cv6m0Nef","label":{"value":"18歲以下","languKey":"CPPMuroRGE"},"image_position":0,"blank_fill":"","image":{"languKey":"DE39Xli2jS","value":""}},{"value":"bNw1ZGnP","label":{"value":"19-25歲","languKey":"iD1KPVomes"},"image_position":0,"blank_fill":"","image":{"languKey":"4xvrOp9Ywz","value":""}},{"value":"6AueHfR7","label":{"value":"26-30歲","languKey":"kg9PxxioEu"},"image_position":0,"blank_fill":"","image":{"languKey":"ekFqwhpKG7"}},{"value":"GmdC5NRy","label":{"value":"31-35歲","languKey":"g-fsj0Ph8h"},"image_position":0,"blank_fill":"","image":{"languKey":"NVA77rB2I7"}},{"value":"xM4_bIJq","label":{"value":"36-40歲","languKey":"JYViF0HsBf"},"image_position":0,"blank_fill":"","image":{"languKey":"62eK6xBUkw"}},{"value":"5gmDO3na","label":{"value":"41-50歲","languKey":"ojCpkm20WO"},"image_position":0,"blank_fill":"","image":{"languKey":"a8at20B2Rs"}},{"value":"3GYqppWQ","label":{"value":"51歲以上","languKey":"x_6_1gcnVw"},"image_position":0,"blank_fill":"","image":{"languKey":"WN4S43QTUP"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"qN_f8qRjEu"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":3,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233i","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您所在地區?</p>","languKey":"bPFPg68dqP"},"question_desc":{"languKey":"ZldSDBCSxP"},"question_tip":{"languKey":"VzTjxg0Aen"}},"question_select_config":{"select_options":[{"value":"8ghifnGr","label":{"value":"北北基 (基隆市、台北市、新北市)","languKey":"_wVe8Lhdua"},"image_position":0,"blank_fill":"","image":{"languKey":"UjAg-Bp6Aq"}},{"value":"8rHSYjO2","label":{"value":"桃竹苗(桃園市、新竹縣/市、苗栗縣)","languKey":"YWSa53PAGi"},"image_position":0,"blank_fill":"","image":{"languKey":"DuTujMbs3b"}},{"value":"agllDkAt","label":{"value":"中彰投(台中市、彰化縣、南投縣)","languKey":"kyRvIlIoa0"},"image_position":0,"blank_fill":"","image":{"languKey":"sg7jjLtOY5"}},{"value":"Tl7p8tme","label":{"value":"雲嘉南(雲林縣、嘉義縣/市、台南市)","languKey":"4lt3qdb_9V"},"image_position":0,"blank_fill":"","image":{"languKey":"q04LjWp1cC"}},{"value":"Z7UFtfs0","label":{"value":"高屏澎(高雄市、屏東縣、澎湖縣)","languKey":"PdR4u_EPwa"},"image_position":0,"blank_fill":"","image":{"languKey":"r4jCT0E332"}},{"value":"rg-i6e0r","label":{"value":"宜花東(宜蘭縣、花蓮縣、台東縣)","languKey":"3mtOUhuZ-u"},"image_position":0,"blank_fill":"","image":{"languKey":"O4InAQtxss"}},{"value":"JLYi5y_5","label":{"value":"金門縣、連江縣","languKey":"18IYT6MzVc"},"image_position":0,"blank_fill":"","image":{"languKey":"Q2lr7v8sHJ"}},{"value":"bhLFIdgA","label":{"value":"香港","languKey":"2HB_R-xKOX"},"image_position":0,"blank_fill":"","image":{"languKey":"SIIvrGw3gk"}},{"value":"Jd9XdE0O","label":{"value":"澳門","languKey":"Rs6mBGMoOE"},"image_position":0,"blank_fill":"","image":{"languKey":"LtH1dhQCBB"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"XU4TBYARS9"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":4,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233k","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您目前的職業</p>","languKey":"dgQnkg1tAk"},"question_desc":{"languKey":"J9hs7djh-V"},"question_tip":{"languKey":"xpwGzQEiRd"}},"question_select_config":{"select_options":[{"value":"T3969ZX2","label":{"value":"在校學生","languKey":"mic3eWhWx2"},"image_position":0,"blank_fill":"","image":{"languKey":"OA40kGVyOd"}},{"value":"EDUWO8yQ","label":{"value":"軍公教","languKey":"THNnCyZd8e"},"image_position":0,"blank_fill":"","image":{"languKey":"OtoTq5RGGF"}},{"value":"bsVJMENR","label":{"value":"普通職員","languKey":"LMGMyVMAW_"},"image_position":0,"blank_fill":"","image":{"languKey":"Ix1GhI1YZx"}},{"value":"P0dnko0q","label":{"value":"技術人員及助理專業人員","languKey":"Gtj6X97Y3_"},"image_position":0,"blank_fill":"","image":{"languKey":"cUbGQbAS7a"}},{"value":"IK1lSpY3","label":{"value":"服務業","languKey":"GosM-4fEPY"},"image_position":0,"blank_fill":"","image":{"languKey":"gOr1X_9H_7"}},{"value":"8vudvFpF","label":{"value":"個體經營者/承包商","languKey":"NCiKx_ir07"},"image_position":0,"blank_fill":"","image":{"languKey":"HBopmd1Asq"}},{"value":"0YLVkXPG","label":{"value":"自由職業者","languKey":"g7ycQ2-zPJ"},"image_position":0,"blank_fill":"","image":{"languKey":"VIB4iFWFyj"}},{"value":"xmdgBVEZ","label":{"value":"農林漁牧業","languKey":"ABZtqGbMjy"},"image_position":0,"blank_fill":"","image":{"languKey":"Kt4CNoicIt"}},{"value":"29izG632","label":{"value":"企業管理者","languKey":"qeodxa6X43"},"image_position":0,"blank_fill":"","image":{"languKey":"8O6R3_juA7"}},{"value":"wfT42Yd7","label":{"value":"退休","languKey":"aPfUJ41xSw"},"image_position":0,"blank_fill":"","image":{"languKey":"Czd2xyjY25"}},{"value":"b2xwDmWq","label":{"value":"家管","languKey":"FtyC5zN1Op"},"image_position":0,"blank_fill":"","image":{"languKey":"aWcGwsu6Ih"}},{"value":"KriBw6q-","label":{"value":"待業中","languKey":"Jn0T4WkAnS"},"image_position":0,"blank_fill":"","image":{"languKey":"wjlOzgUNZ3"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"jPnxP07f4L"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":5,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233m","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您的平均月收入</p>","languKey":"-LUn_skrhz"},"question_desc":{"languKey":"w8V5ApxW6n"},"question_tip":{"languKey":"YFvbPasJ0h"}},"question_select_config":{"select_options":[{"value":"jjsqnXWR","label":{"value":"NT$ 2,500-5,000元","languKey":"q_arC5zuml"},"image_position":0,"blank_fill":"","image":{"languKey":"7zy1pMsfaa"}},{"value":"_XjQ_3fH","label":{"value":"NT$ 5,001-10,000元","languKey":"1D7fVZfB-H"},"image_position":0,"blank_fill":"","image":{"languKey":"bFp8R4Udf8"}},{"value":"2PqWDxpI","label":{"value":"NT$ 10,001-15,000元","languKey":"M2ALPHm6sP"},"image_position":0,"blank_fill":"","image":{"languKey":"PG7fI9kBJ0"}},{"value":"KnoahUIU","label":{"value":"NT$ 15,001-25,000元","languKey":"G1343zZv-n"},"image_position":0,"blank_fill":"","image":{"languKey":"vIr0CX3XBo"}},{"value":"Sni7t-yw","label":{"value":"NT$ 25,001-40,000元","languKey":"4N4iDdizSW"},"image_position":0,"blank_fill":"","image":{"languKey":"zZ_foM1-BX"}},{"value":"p8KebqNj","label":{"value":"NT$ 40,001-50,000元","languKey":"t1aEVjdqAu"},"image_position":0,"blank_fill":"","image":{"languKey":"o2Nc6QOHDq"}},{"value":"Fdfad92b","label":{"value":"NT$ 50,001-100,000元","languKey":"oXz03Q7Ae-"},"image_position":0,"blank_fill":"","image":{"languKey":"a6epL0Iqtt"}},{"value":"RnVHbuy4","label":{"value":"NT$ 100,001元以上","languKey":"UJ2fQeUA09"},"image_position":0,"blank_fill":"","image":{"languKey":"YcQ4H3YSun"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"Cgzv0rvBhj"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":6,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg233o","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您是從哪裡得知『戀與深空』</p>","languKey":"8aJoBi928M"},"question_desc":{"value":"","languKey":"3pFo_Lt9CX"},"question_tip":{"value":"","languKey":"keMsdYiZID"}},"question_select_config":{"select_options":[{"value":"5KNTSwR4","label":{"value":"App Store / Google Play 商店","languKey":"GuHZgEJSnw"},"image_position":0,"blank_fill":"","image":{"languKey":"KzA1vF14RL","value":""}},{"value":"g3yUIly7","label":{"value":"線上廣告(FB、IG、Dcard、巴哈姆特、YT等線上媒體)","languKey":"iPU8t0PFBa"},"image_position":0,"blank_fill":"","image":{"languKey":"Zoyi-rtP7z","value":""}},{"value":"8EqLZcmQ","label":{"value":"自媒體(實況主、KOL、KOC、頻道)","languKey":"_Fq2fYg2KA"},"image_position":0,"blank_fill":"","image":{"languKey":"o66nmkAO__","value":""}},{"value":"N1Slotkg","label":{"value":"實體商店","languKey":"fI93G5UyFD"},"image_position":0,"blank_fill":"","image":{"languKey":"Q-9lVbdxGr","value":""}},{"value":"GuYSx1lR","label":{"value":"實體刊物(報紙、雜誌)","languKey":"85G_7XXTPi"},"image_position":0,"blank_fill":"","image":{"languKey":"XHad3Z6sof","value":""}},{"value":"lIWXIOyx","label":{"value":"電子書、報、郵件、Email、簡訊","languKey":"lel095ORrC"},"image_position":0,"blank_fill":"","image":{"languKey":"WFs4jGG-lr","value":""}},{"value":"ua3979-Q","label":{"value":"網路搜尋(Google、Bing)","languKey":"BeCSgRP7oa"},"image_position":0,"blank_fill":"","image":{"languKey":"rLBbj0uxBs","value":""}},{"value":"lASk6xXv","label":{"value":"網路社群媒體文章/轉載/推薦 (PTT、YT、IG、FB、Dcard等)","languKey":"F_p5RJzq_h"},"image_position":0,"blank_fill":"","image":{"languKey":"NIJQvlNIIi","value":""}},{"value":"h7DFzdKV","label":{"value":"朋友推薦","languKey":"yl6rdymMie"},"image_position":0,"blank_fill":"","image":{"languKey":"mfkT51D9V_","value":""}},{"value":"hpYd4SUs","label":{"value":"實體通路","languKey":"3GH7K9wbKX"},"image_position":0,"blank_fill":"","image":{"languKey":"nC_5wz760s","value":""}},{"value":"Wyp1wdri","label":{"value":"其他","languKey":"2l_09JUmLV"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"hlDZV2TR4X","value":""},"default_checked":false}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"SE3L0ylSN1"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":7,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg233t","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您平常最主要的線下娛樂行為有哪些？</p>","languKey":"zBG91K1m_S"},"question_desc":{"languKey":"Xn44FL_yt_","value":""},"question_tip":{"languKey":"PUvuGdsAnW","value":""}},"question_select_config":{"max_selected":3,"select_options":[{"value":"_sbm7nk5","label":{"value":"參加文化展覽（如漫展、美術展等）","languKey":"JnKWNPxMEM"},"image_position":0,"blank_fill":"","image":{"languKey":"KNRa0vkije","value":""}},{"value":"H1dCahv_","label":{"value":"參加文娛活動（如去Livehouse、聽演唱會、看戲劇/舞台劇/歌劇等）","languKey":"SK5Uk3rAy7"},"image_position":0,"blank_fill":"","image":{"languKey":"uMRM0P6NEo","value":""}},{"value":"oUmLbqBy","label":{"value":"沉浸式體驗娛樂（如密室逃脫、劇本殺、VR、參演戲劇/舞台劇/歌劇等）","languKey":"kLxsFFI1Jb"},"image_position":0,"blank_fill":"","image":{"languKey":"E0CDtbAUaw","value":""}},{"value":"R0XNhllb","label":{"value":"文化、創作體驗（DIY、寫書法、陶土等手工藝製作）","languKey":"IfFhrIha8p"},"image_position":0,"blank_fill":"","image":{"languKey":"EJ17HCjCgC","value":""}},{"value":"AtwaCArj","label":{"value":"多人聚會活動（如桌遊店、電子遊樂場、綜合式娛樂場所等）","languKey":"hbXufSv4kx"},"image_position":0,"blank_fill":"","image":{"languKey":"qVLnZaZpce","value":""}},{"value":"DNahS7UW","label":{"value":"遊樂園（主題樂園、遊樂場）","languKey":"diDoVdnhWb"},"image_position":0,"blank_fill":"","image":{"languKey":"QFGlMx5mJQ","value":""}},{"value":"hzQEI7Tl","label":{"value":"體育運動（如健身、馬拉松、滑雪、登山等）","languKey":"SZ59egSa9g"},"image_position":0,"blank_fill":"","image":{"languKey":"nu_hyGkqp3","value":""}},{"value":"Q6VyrweH","label":{"value":"旅行出遊（如旅行、露營等）","languKey":"8mKVIIsxa5"},"image_position":0,"blank_fill":"","image":{"languKey":"nSUJ2J_R-t","value":""}},{"value":"GSwoRdUs","label":{"value":"線下購物、逛街等","languKey":"vO8BoJtpfI"},"image_position":0,"blank_fill":"","image":{"languKey":"O2gz5C4rlV","value":""}},{"value":"ehi5T3F0","label":{"value":"與好友聚餐、美食探店打卡等","languKey":"JcMbihTVLn"},"image_position":0,"blank_fill":"","image":{"languKey":"4FRphqhtKh","value":""}},{"value":"rSNgnwpo","label":{"value":"其他，請填寫","languKey":"-NTLsre5fa"},"image_position":0,"blank_fill":{"allow":true},"image":{"languKey":"pkiafEcR6o","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"pUlqQQ42gl"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":8,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg233v","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您平常最主要的線上娛樂行為有哪些？</p>","languKey":"q-B8ikBvHQ"},"question_desc":{"value":"","languKey":"JCBP0TTiu-"},"question_tip":{"value":"","languKey":"jNEZQRXkzv"}},"question_select_config":{"max_selected":3,"select_options":[{"value":"As-UVt_v","label":{"value":"看動漫（動畫、漫畫）","languKey":"YmS1fkSNJ6"},"image_position":0,"blank_fill":"","image":{"languKey":"yxX90tvQgt","value":""}},{"value":"k5rd3Kvw","label":{"value":"看網路影片（如綜藝、短影片、一般影片等）","languKey":"pW63wXvS7A"},"image_position":0,"blank_fill":"","image":{"languKey":"2aMXaPCyfT","value":""}},{"value":"1midamDk","label":{"value":"看電影、電視劇、網路劇等","languKey":"4gh5WMVJ0j"},"image_position":0,"blank_fill":"","image":{"languKey":"Cis_FmOqaI","value":""}},{"value":"70WBXIpz","label":{"value":"看網路小說","languKey":"wST_9P1ERB"},"image_position":0,"blank_fill":"","image":{"languKey":"09Ol0x9n9Q","value":""}},{"value":"-iLrWmqQ","label":{"value":"看體育比賽","languKey":"ZvPokWX2Em"},"image_position":0,"blank_fill":"","image":{"languKey":"VsqqHjKCJu","value":""}},{"value":"metyWXnA","label":{"value":"看直播（娛樂主播、遊戲主播）","languKey":"GIrQhf0PZz"},"image_position":0,"blank_fill":"","image":{"languKey":"hF0O6ap11I","value":""}},{"value":"mnBJxivF","label":{"value":"看遊戲電競比賽","languKey":"ee-D89JB-v"},"image_position":0,"blank_fill":"","image":{"languKey":"4PxwtVSC-u","value":""}},{"value":"3loCLhAk","label":{"value":"聽音樂/電台節目","languKey":"HWga7PKgh0"},"image_position":0,"blank_fill":"","image":{"languKey":"UOWaaxAysy","value":""}},{"value":"5XfXdNMa","label":{"value":"玩遊戲（PC線上遊戲、手遊、主機/單機遊戲等）","languKey":"j3YZLeECY_"},"image_position":0,"blank_fill":"","image":{"languKey":"bQ0daS3lCd","value":""}},{"value":"gI7eCLk2","label":{"value":"關注、瀏覽、或是產出同人作品逛網路社群（如論壇、討論區等）","languKey":"rBG3bzCyd7"},"image_position":0,"blank_fill":"","image":{"languKey":"3_yPAMkDCo","value":""}},{"value":"UVktIDk-","label":{"value":"社交/聊天（使用Line、Instagram、Whatsapp等）","languKey":"P88HeivTXW"},"image_position":0,"blank_fill":"","image":{"languKey":"5g3aPw3wVZ","value":""}},{"value":"RW1V_Rag","label":{"value":"瀏覽新聞/資訊（新聞網站或APP等）","languKey":"cD_Ja4LbEX"},"image_position":0,"blank_fill":"","image":{"languKey":"nyZavLwMXE","value":""}},{"value":"S9AdVRj2","label":{"value":"網路購物","languKey":"Vo06OM-LIA"},"image_position":0,"blank_fill":"","image":{"languKey":"PaiQyxGlQd","value":""}},{"value":"UyZ9z-et","label":{"value":"其他，請填寫","languKey":"m3RSERbnnS"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"s_5zI4lyX5","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"glwPZDyJa2"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":9,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg233x","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您最常使用的社群媒體?</p>","languKey":"Wjb_qYw-oe"},"question_desc":{"value":"","languKey":"m7N1JDMs3x"},"question_tip":{"value":"","languKey":"tcBJHogeYm"}},"question_select_config":{"max_selected":2,"select_options":[{"value":"IfEwgoeg","label":{"value":"Facebook","languKey":"CcqP_cQkCy"},"image_position":0,"blank_fill":"","image":{"languKey":"j7XdQywzu4","value":""}},{"value":"dnjwaFz6","label":{"value":"Instagram","languKey":"-MVPH4qkdK"},"image_position":0,"blank_fill":"","image":{"languKey":"JYqJxjFrJP","value":""}},{"value":"N6BmlfDN","label":{"value":"Threads","languKey":"nakF9opBaS"},"image_position":0,"blank_fill":"","image":{"languKey":"cTMg7SHpka","value":""}},{"value":"_YIIt3vN","label":{"value":"Dcard","languKey":"nG86CJ7xbK"},"image_position":0,"blank_fill":"","image":{"languKey":"LUmjecMpp-","value":""}},{"value":"D2jrXQyV","label":{"value":"Discord","languKey":"n0ppPiEuNG"},"image_position":0,"blank_fill":"","image":{"languKey":"ICDdnsO-zx","value":""}},{"value":"T8r0hsaD","label":{"value":"TikTok","languKey":"H2yyLqpTQF"},"image_position":0,"blank_fill":"","image":{"languKey":"wSMREdCPwC","value":""}},{"value":"g5rBJ0n3","label":{"value":"Youtube","languKey":"6D9-IW-7kY"},"image_position":0,"blank_fill":"","image":{"languKey":"zSPawR7Ntw","value":""}},{"value":"yw1x-dAk","label":{"value":"小紅書","languKey":"oGj3XAChAf"},"image_position":0,"blank_fill":"","image":{"languKey":"tLjh3JxkUX","value":""}},{"value":"VI7dbYB6","label":{"value":"PTT","languKey":"9yafGEr4u0"},"image_position":0,"blank_fill":"","image":{"languKey":"eFB0kcH7oX","value":""}},{"value":"1hcTWwbr","label":{"value":"X (原Twitter)","languKey":"eMUTTQqiSS"},"image_position":0,"blank_fill":"","image":{"languKey":"5ggBgJjG8t","value":""}},{"value":"xn43XybA","label":{"value":"其他，請填寫","languKey":"xAmRVAu3pN"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"V_3P-Xemp6","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"vwwBDlwzaF"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":10,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg233z","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>最常使用的影音平台?</p>","languKey":"4DQG8Vmb2q"},"question_desc":{"value":"","languKey":"UhP0dNl3b6"},"question_tip":{"value":"","languKey":"jf_bwjJ7wM"}},"question_select_config":{"max_selected":2,"select_options":[{"value":"sHe6xIoE","label":{"value":"YouTube","languKey":"1c7nXjng25"},"image_position":0,"blank_fill":"","image":{"languKey":"B3HzFpgBi3","value":""}},{"value":"EEb3LQXM","label":{"value":"愛奇藝","languKey":"e6v8YyXGST"},"image_position":0,"blank_fill":"","image":{"languKey":"a0XuYAf1ak","value":""}},{"value":"cAEHutHB","label":{"value":"Netflix","languKey":"J2f3SppGeX"},"image_position":0,"blank_fill":"","image":{"languKey":"uJHxwcFCh9","value":""}},{"value":"Lge1zx8Q","label":{"value":"Disney+","languKey":"cJmwp6885e"},"image_position":0,"blank_fill":"","image":{"languKey":"E1R1-F3i5h","value":""}},{"value":"rHBZY232","label":{"value":"HBO Max","languKey":"05qmeo4BiR"},"image_position":0,"blank_fill":"","image":{"languKey":"k7WiKq-wQi","value":""}},{"value":"oYWnER1j","label":{"value":"Line TV","languKey":"u4okOvkk00"},"image_position":0,"blank_fill":"","image":{"languKey":"SKT4FsRu9B","value":""}},{"value":"qOtOsnAt","label":{"value":"KKTV","languKey":"FVYv-9LoAX"},"image_position":0,"blank_fill":"","image":{"languKey":"ZRkgFuJdYW","value":""}},{"value":"Aqqe5IQp","label":{"value":"friDay影音","languKey":"CQeDw3sF8D"},"image_position":0,"blank_fill":"","image":{"languKey":"9THyilZ8zy","value":""}},{"value":"NL4Vyg06","label":{"value":"其他，請填寫","languKey":"zw6dPovCtK"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"p-2Rr8gJ2S","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"dC2p9zNHbI"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":11,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg23311","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您看的戲劇/動漫/小說是哪一種類型?</p>","languKey":"BXdnOsM39l"},"question_desc":{"value":"","languKey":"5ulTwsyOv8"},"question_tip":{"value":"","languKey":"f-4myPaRYt"}},"question_select_config":{"max_selected":2,"select_options":[{"value":"5QtRSzzE","label":{"value":"喜劇類 (開心、搞笑)","languKey":"XSiOGKYuhz"},"image_position":0,"blank_fill":"","image":{"languKey":"3vfeQFSXvY","value":""}},{"value":"BK_0jkSz","label":{"value":"劇情類 (重情節、張力大)","languKey":"MBW53nY-p0"},"image_position":0,"blank_fill":"","image":{"languKey":"qpyDPu9E2-","value":""}},{"value":"-cdTQNZ4","label":{"value":"愛情類 (談戀愛、三角戀)","languKey":"D5wPFjK3DN"},"image_position":0,"blank_fill":"","image":{"languKey":"hFy23IX7TF","value":""}},{"value":"a_JILwEi","label":{"value":"科幻類 (穿越、平行宇宙、未來科技)","languKey":"Lh5U0LtJ44"},"image_position":0,"blank_fill":"","image":{"languKey":"wxprb83RSg","value":""}},{"value":"TwQRi1hZ","label":{"value":"奇幻類 (魔法、神話)","languKey":"JMFSdsfhiW"},"image_position":0,"blank_fill":"","image":{"languKey":"sImoJzVcRu","value":""}},{"value":"oOIUZDNA","label":{"value":"靈異類 (鬼怪、幽靈、神魔)","languKey":"ViJDYnMmsM"},"image_position":0,"blank_fill":"","image":{"languKey":"E6YLs70cg-","value":""}},{"value":"FYUTlPVC","label":{"value":"親情類 (家人、婚姻)","languKey":"NjulvqV2vI"},"image_position":0,"blank_fill":"","image":{"languKey":"FbcBhJvxpp","value":""}},{"value":"PTbcj2q6","label":{"value":"犯罪類 (警察、犯人、法官、犯罪)","languKey":"EgGxWO-uQb"},"image_position":0,"blank_fill":"","image":{"languKey":"zHjwAJAjV1","value":""}},{"value":"SkvBqAbI","label":{"value":"實事改編 (真實故事改編)","languKey":"PwQlY26yQc"},"image_position":0,"blank_fill":"","image":{"languKey":"vnpavTL-S-","value":""}},{"value":"MsK20p_s","label":{"value":"其他，請填寫","languKey":"CjrEqcT1Qb"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"IRr8UpMr6R"}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"C03MuqCZHB"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":12,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg23313","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>以下手機類型遊戲，您玩過的有哪些？</p>","languKey":"NAjqGfmmVX"},"question_desc":{"value":"","languKey":"8rsT2zGa7c"},"question_tip":{"value":"","languKey":"MOWHIJTBKa"}},"question_select_config":{"max_selected":3,"select_options":[{"value":"bBJeaaZQ","label":{"value":"女性向類（如戀與製作人、光與夜之戀、未定事件簿、時空中的繪旅人等）","languKey":"JGwWUsZXNX"},"image_position":0,"blank_fill":"","image":{"languKey":"KsDNg64eb1","value":""}},{"value":"b8fQ9sS8","label":{"value":"換裝類（奇迹暖暖、閃耀暖暖等）","languKey":"C8KpRDamRM"},"image_position":0,"blank_fill":"","image":{"languKey":"xfe3kYLKwe","value":""}},{"value":"92JIzMIv","label":{"value":"MMORPG（如天堂M、仙境傳說：守護永恆的愛等）","languKey":"cc8upDTPZG"},"image_position":0,"blank_fill":"","image":{"languKey":"iaebZI78Zv","value":""}},{"value":"UK9P3icJ","label":{"value":"音樂類（如Cytus、LoveLive等）","languKey":"5ypVItZ95O"},"image_position":0,"blank_fill":"","image":{"languKey":"z6aMo03YP-","value":""}},{"value":"T2wR1vpg","label":{"value":"沙盒類（如Minecraft等）","languKey":"-9zRRimVE3"},"image_position":0,"blank_fill":"","image":{"languKey":"xkeEsriIzt","value":""}},{"value":"wwmRps_g","label":{"value":"回合制RPG（如陰陽師、崩壞：星穹鐵道）","languKey":"5yUm2q5_Jr"},"image_position":0,"blank_fill":"","image":{"languKey":"a-vrBgv7cz","value":""}},{"value":"XYWDX63R","label":{"value":"休閒益智（如極速領域、弓劍傳說等）","languKey":"r0tLdKM300"},"image_position":0,"blank_fill":"","image":{"languKey":"BT3znIU9A7","value":""}},{"value":"u21e_e2w","label":{"value":"射擊類（如荒野行動、PUBG M等）","languKey":"Uj9RUQImlp"},"image_position":0,"blank_fill":"","image":{"languKey":"txb8hTLeFR","value":""}},{"value":"08bRkhDW","label":{"value":"開放世界類（如原神、幻塔等）","languKey":"2Coy-j-2IV"},"image_position":0,"blank_fill":"","image":{"languKey":"lT8Dttdmtg","value":""}},{"value":"XjAa-fSI","label":{"value":"MOBA類（如傳說對決、英雄聯盟等）","languKey":"o9uOzIY-Yj"},"image_position":0,"blank_fill":"","image":{"languKey":"mu6EnQ0lPi","value":""}},{"value":"nmWMiLJh","label":{"value":"其他，請填寫","languKey":"GyI34y43_u"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"nGUgDJ-u43","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"dp5YRJZq3Y"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":13,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg23315","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您平時每天會花多少時間玩遊戲</p>","languKey":"LTVSkyFDIj"},"question_desc":{"value":"","languKey":"B0OTJjMwku"},"question_tip":{"value":"","languKey":"Dv0Gij_jOH"}},"question_select_config":{"select_options":[{"value":"tVvq52Jg","label":{"value":"30分鐘以內","languKey":"5JIcHLw7Yk"},"image_position":0,"blank_fill":"","image":{"languKey":"C9GRwpdVDb"}},{"value":"KKyrlQm_","label":{"value":"30分鐘-1小時","languKey":"OpmWOaW32H"},"image_position":0,"blank_fill":"","image":{"languKey":"o7meWdwSlw"}},{"value":"2XUV0gFt","label":{"value":"1小時-3小時","languKey":"BBvGPGzgx4"},"image_position":0,"blank_fill":"","image":{"languKey":"gb2NAou7F4"}},{"value":"3ojK5p6v","label":{"value":"3小時-5小時","languKey":"vwDKbJXmcN"},"image_position":0,"blank_fill":"","image":{"languKey":"WXix04HK_B"}},{"value":"s_vVLEEm","label":{"value":"5小時-12小時","languKey":"Do4a52NGUh"},"image_position":0,"blank_fill":"","image":{"languKey":"n9SDqYp3m0"}},{"value":"kCWxT_EI","label":{"value":"12小時以上","languKey":"e7cQMWbmPf"},"image_position":0,"blank_fill":"","image":{"languKey":"-T-l3DvrpS"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"7IYxmQDroe"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":14,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg23317","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您玩遊戲的時間會在哪一個區間?</p>","languKey":"CKFCRu3NLH"},"question_desc":{"value":"","languKey":"7ty4PN2s8_"},"question_tip":{"value":"","languKey":"ymEebOQXGG"}},"question_select_config":{"select_options":[{"value":"Yy-jbR0U","label":{"value":"早上 (08:00 - 12:00)","languKey":"PaY_totgRi"},"image_position":0,"blank_fill":"","image":{"languKey":"nAb7JQCNJ_"}},{"value":"Yc27yRUR","label":{"value":"中午 (12:00 - 14:00)","languKey":"TEie1u0c_L"},"image_position":0,"blank_fill":"","image":{"languKey":"FjxB6mUS1c"}},{"value":"Nz21iZ7_","label":{"value":"下午 (14:00 - 18:00)","languKey":"80MDnfFIVV"},"image_position":0,"blank_fill":"","image":{"languKey":"pm88B2JbYh"}},{"value":"NJ8qNalJ","label":{"value":"晚上 (18:00 - 24:00)","languKey":"RxlPEOLPf3"},"image_position":0,"blank_fill":"","image":{"languKey":"7cds7LyFR8"}},{"value":"VlbsE79n","label":{"value":"凌晨 (00:00 - 08:00)","languKey":"ID5sGV3Coq"},"image_position":0,"blank_fill":"","image":{"languKey":"H3iwZ-Dm6V"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"MznXYH6kTs"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":15,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg23319","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您平均一週在遊戲上消費金額是多少?</p>","languKey":"WGt-u2KukO"},"question_desc":{"value":"","languKey":"1E8Wc4wY-U"},"question_tip":{"value":"","languKey":"re0NIpg8EF"}},"question_select_config":{"select_options":[{"value":"0NslqaqV","label":{"value":"NT$ 500元以下","languKey":"5EoxmIXswz"},"image_position":0,"blank_fill":"","image":{"languKey":"6mj-VK3RSH"}},{"value":"7_3tA9TD","label":{"value":"NT$ 501-1,500元","languKey":"ecDkvUGhw4"},"image_position":0,"blank_fill":"","image":{"languKey":"QRvm5NsMgz"}},{"value":"69gK4V4G","label":{"value":"NT$ 1,501-2,500元","languKey":"Mkhw7Kelnk"},"image_position":0,"blank_fill":"","image":{"languKey":"A4hBoKMMvK"}},{"value":"L6Rcds9H","label":{"value":"NT$ 2,501-5,000元","languKey":"dO6JGUbPCK"},"image_position":0,"blank_fill":"","image":{"languKey":"Onu40N-lut"}},{"value":"1sWOhiEY","label":{"value":"NT$ 5,001-15,000元","languKey":"Yy6Nw2sYFk"},"image_position":0,"blank_fill":"","image":{"languKey":"lZQjjayvVU"}},{"value":"3Zzqyvfs","label":{"value":"NT$ 15,001-25,000元","languKey":"1W5s5s_JQQ"},"image_position":0,"blank_fill":"","image":{"languKey":"nanIDurLmU"}},{"value":"sPwWnn1S","label":{"value":"NT$ 25,001-50,000元","languKey":"ewG08xCyeB"},"image_position":0,"blank_fill":"","image":{"languKey":"UllsrOhSIv"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"PhOCWCOFPf"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":16,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg2331b","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您所使用的電信業者?</p>","languKey":"OX4v03hUl6"},"question_desc":{"value":"","languKey":"AbNP9FFXTo"},"question_tip":{"value":"","languKey":"yEV888pkmn"}},"question_select_config":{"select_options":[{"value":"d-raJ_QW","label":{"value":"中華電信","languKey":"0_PwFpuX2z"},"image_position":0,"blank_fill":"","image":{"languKey":"GExD7QngBQ","value":""}},{"value":"cSX0BU10","label":{"value":"台灣大哥大","languKey":"rWuJQUSY3j"},"image_position":0,"blank_fill":"","image":{"languKey":"su_rMJbAWi","value":""}},{"value":"gb7JWDky","label":{"value":"遠傳","languKey":"frvl5Vv6E-"},"image_position":0,"blank_fill":"","image":{"languKey":"V10nC5TsXt","value":""}},{"value":"cKAU1aqB","label":{"value":"香港，請填寫電信業者","languKey":"opmly9a--A"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"hFa3K0fthR","value":""}},{"value":"_UP7403G","label":{"value":"澳門，請填寫電信業者","languKey":"cpCeDwGahK"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"LtH1juRK6d","value":""}},{"value":"JwreY-zH","label":{"value":"其他，請填寫","languKey":"x5Y8KFSFUX"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"HsWAephTcr","value":""}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"YeWKxhzwl-"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":17,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg2331d","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您有參加《戀與深空》的官網預約活動嗎?</p>","languKey":"8J21CO_l90"},"question_desc":{"languKey":"ValIBmEecw"},"question_tip":{"languKey":"kyhCsr4u8t"}},"question_select_config":{"select_options":[{"value":"eqIdJQ4U","label":{"value":"有","languKey":"ppPKTy5Dex"},"image_position":0,"blank_fill":"","image":{"languKey":"Pd_PzgBlhI"}},{"value":"zE1e5lFw","label":{"value":"無","languKey":"HU9tw3-sYo"},"image_position":0,"blank_fill":"","image":{"languKey":"BBtKFK9DyW"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"gc43JJn3ML"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":18,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg2331f","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您有參加《戀與深空》的雙平台預約嗎?</p>","languKey":"xEj_vJxqyU"},"question_desc":{"languKey":"u7o7MCJPcH"},"question_tip":{"languKey":"5x4NyApEjA"}},"question_select_config":{"select_options":[{"value":"4uCBxJJl","label":{"value":"有","languKey":"mK-diwMPX2"},"image_position":0,"blank_fill":"","image":{"languKey":"skeRZwaQNq"}},{"value":"jPbmhmd2","label":{"value":"無","languKey":"1jxmyykska"},"image_position":0,"blank_fill":"","image":{"languKey":"GgpROQqNLm"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"Qi3YdpK8Lh"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":19,"parent":{"index":0}},{"componentName":"QuestionRadio","id":"node_oclr7fg2331h","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您有參加《戀與深空》的封閉測試嗎?</p>","languKey":"kqZYXw3Trq"},"question_desc":{"languKey":"m3rc_Eemy2"},"question_tip":{"languKey":"DlHUovDrTL"}},"question_select_config":{"select_options":[{"value":"SMwZsBgG","label":{"value":"有","languKey":"Hm2BhgIHpt"},"image_position":0,"blank_fill":"","image":{"languKey":"FrenQ6j19s"}},{"value":"SeYGDdil","label":{"value":"無","languKey":"WH2x3YY1aj"},"image_position":0,"blank_fill":"","image":{"languKey":"UvELsdz41D"}}]},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"single","valueType":"string","uniqueKey":"fyjqc8JI6H"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":20,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg2331m","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>吸引您玩《戀與深空》的主要原因是？</p>","languKey":"9HtDCYW7f2"},"question_desc":{"value":"","languKey":"12-kXRnfR5"},"question_tip":{"value":"","languKey":"Ir5xyMR6UX"}},"question_select_config":{"max_selected":2,"select_options":[{"value":"nUnfMNI4","label":{"value":"對於3D戀愛遊戲感興趣","languKey":"4UswUKxc6G"},"image_position":0,"blank_fill":"","image":{"languKey":"AnvqOzG60T","value":""}},{"value":"cYdLYipb","label":{"value":"本身就對女性向遊戲感興趣","languKey":"X8lD49Gi8I"},"image_position":0,"blank_fill":"","image":{"languKey":"I3lmjeKk0w","value":""}},{"value":"gifQRn0n","label":{"value":"對世界觀題材感興趣，喜歡體驗劇情","languKey":"iDghG9V3i-"},"image_position":0,"blank_fill":"","image":{"languKey":"Aa0Hs4xqmt","value":""}},{"value":"oPcVwGTG","label":{"value":"覺得風格、美術好看","languKey":"XoU--OHCMa"},"image_position":0,"blank_fill":"","image":{"languKey":"fEDeosU6Td","value":""}},{"value":"dd0BlKYm","label":{"value":"喜歡人物的外觀","languKey":"j9YoBAbXM2"},"image_position":0,"blank_fill":"","image":{"languKey":"FS26wtsIGX","value":""}},{"value":"C1GyFZ6m","label":{"value":"親朋好友推坑","languKey":"SXZ7pW_a7F"},"image_position":0,"blank_fill":"","image":{"languKey":"Ugft5NZ5P9","value":""}},{"value":"A2HfZCn3","label":{"value":"其他，請填寫","languKey":"c5zOvugyhc"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"YK7GHCrayf","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"6HqNpEybHO"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":21,"parent":{"index":0}},{"componentName":"QuestionCheckbox","id":"node_oclr7fg2331o","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>請問您對於女性向遊戲最在意的內容？</p>","languKey":"AdBmPEIYhO"},"question_desc":{"value":"","languKey":"um5DSHxXU4"},"question_tip":{"value":"","languKey":"94i36zNNjC"}},"question_select_config":{"max_selected":3,"select_options":[{"value":"R719sUvS","label":{"value":"劇情","languKey":"1ojQJydyOw"},"image_position":0,"blank_fill":"","image":{"languKey":"k5B-Sw3X2r","value":""}},{"value":"fhaYj7KP","label":{"value":"畫面","languKey":"HZeciej8EN"},"image_position":0,"blank_fill":"","image":{"languKey":"V3EQ2_Vxii","value":""}},{"value":"i1sKGYS1","label":{"value":"男主角","languKey":"6t5ki61i3d"},"image_position":0,"blank_fill":"","image":{"languKey":"2JJRdqgGO5","value":""}},{"value":"E5cX8SuU","label":{"value":"特效","languKey":"io4m-5Unzx"},"image_position":0,"blank_fill":"","image":{"languKey":"HWg-05E0AN","value":""}},{"value":"wMSNId-q","label":{"value":"聲音","languKey":"GQ8cjln6_F"},"image_position":0,"blank_fill":"","image":{"languKey":"LvWt4TFOLf","value":""}},{"value":"i463SbQL","label":{"value":"互動","languKey":"dW0d_3yhS4"},"image_position":0,"blank_fill":"","image":{"languKey":"jgdrvyeqGC","value":""}},{"value":"mBybshj1","label":{"value":"氛圍","languKey":"1oOmA7EjSA"},"image_position":0,"blank_fill":"","image":{"languKey":"U2ND0VoXtH","value":""}},{"value":"CkDNQnrN","label":{"value":"其他，請填寫","languKey":"GA6U2-Ju1H"},"image_position":0,"blank_fill":{"allow":true,"require":true},"image":{"languKey":"Uy1E4HZx2S","value":""}}]},"question_component_config":{},"question_check_config":{"required":true},"configProps":{"isQuestion":true,"hasFillFree":true,"questionType":"select","selectMode":"multi","valueType":"string","uniqueKey":"uQAFN32uuD"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":22,"parent":{"index":0}},{"componentName":"QuestionInput","id":"node_oclr7fg2331q","docId":"doclr7fg233","props":{"question_base_config":{"question_title":{"value":"<p>期望您提供建議以及想法，作為爾後我們持續努力的方向</p>","languKey":"MwJz5qO2JQ"},"placeholder":{"value":"请输入","languKey":"5C4zmmAFLf"},"question_desc":{"languKey":"4oHy8q6aOP"},"question_tip":{"languKey":"VqJNfGCbGo"}},"question_check_config":{"required":true},"question_select_config":{},"configProps":{"isQuestion":true,"questionType":"input","valueType":"string","uniqueKey":"R2yGkLzu3f"},"extraProps":{},"question_logical_config":{"visible_switch":{},"condition_desc":""}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":23,"parent":{"index":0}}]}],"parent":{}}]},{"componentName":"Page","id":"node_ocl5jke2bq4","docId":"docl5jke2bq4","props":{},"meta":{"title":"法务法规页","router":"/legal"},"fileName":"legal","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"LegalProvisions","id":"node_oclilhcxl91","docId":"doclr7fg266","props":{"baseConfig":{"showLegalPage":true,"richtext":{"languKey":"legal_info","value":"<p>親愛的深空獵人您好！感謝您參與《戀與深空》公開測試。 &nbsp;</p><p><br></p><p>本問卷的目的是為了讓我們可以更了解每一位深空獵人的遊玩體驗。 </p><p>您的所有意見將成為我們持續改善遊戲重要的指標，請盡可能詳細作答，感謝您的支持！ </p><p>為了回饋填寫問卷的每一位獵人，我們將會提供問卷填寫獎勵「極空許願劵*1」。</p><p><br></p><p> &nbsp;※本問卷收集到的所有資料僅會用於體驗分析和優化參考，不會共享給其他任何第三方廠商或應用於其他用途，請獵人們放心填寫。</p>"}},"skinConfig":{"showSkin":true,"skin":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png"},"bgColor":""},"configProps":{"isQuestion":false,"valueType":"string"},"settingsProps":{"baseRuleConfig":{"loginType":"0","timeLimitConfig":{"isTimeLimit":false},"isEndPreview":false,"isGoOnAnswer":true,"answerTimesConfig":{"limitType":1,"times":1},"languageList":["zh-tw"],"deliverList":["港澳台"]},"giftConfig":{"isGiveOutByCms":false,"giveOutType":"","preAwardConfig":null,"redeemConfig":null},"answerLimitConfig":{"limitType":""},"zoneIds":[5,15],"materialsConfig":{"autoLatestMaterial":true,"materialVersion":"0.2.7-alpha.3"},"footerConfig":{"url":"https://assets.papegames.com/umd/deepspace-footer-other/index.min.js","name":"deepspace-footer-other"},"sourceConfig":{"citysUrl":"","agreements":[]}},"clientid":"1067"},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{}}]},{"componentName":"Page","id":"node_ocl5jke2bq2","docId":"docl5jke2bq2","props":{},"meta":{"title":"完成答题","router":"/completed"},"fileName":"completed","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyCompleted","id":"node_oclikflwi01","docId":"doclikflwi0","props":{"baseConfig":{"message":"请输入描述说明","image":{"languKey":"survey_completed_image","value":"https://cmscdn.papegames.com/cmssurvey/static/55724be1cc3e908514256cc0e1a27eb3.png"},"messages":{"no_award":{"languKey":"no_award","value":"答题完成"},"email_award":{"languKey":"email_award","value":"答题完成，已发放邮件奖励"},"award_cdkey":{"languKey":"award_cdkey","value":"答题完成，已发放兑换码奖励"},"preview_type":"no_award"}},"skinConfig":{"showSkin":false,"skin":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png"},"bgColor":""},"settingsProps":{"baseRuleConfig":{"loginType":"0","timeLimitConfig":{"isTimeLimit":false},"isEndPreview":false,"isGoOnAnswer":true,"answerTimesConfig":{"limitType":1,"times":1},"languageList":["zh-tw"],"deliverList":["港澳台"]},"giftConfig":{"isGiveOutByCms":false,"giveOutType":"","preAwardConfig":null,"redeemConfig":null},"answerLimitConfig":{"limitType":""},"zoneIds":[5,15],"materialsConfig":{"autoLatestMaterial":true,"materialVersion":"0.2.7-alpha.3"},"footerConfig":{"url":"https://assets.papegames.com/umd/deepspace-footer-other/index.min.js","name":"deepspace-footer-other"},"sourceConfig":{"citysUrl":"","agreements":[]}},"clientid":"1067"},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{}}]},{"componentName":"Page","id":"node_ocl5jke2bq3","docId":"docl5jke2bq3","props":{},"meta":{"title":"停止答题","router":"/stopped"},"fileName":"stopped","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SurveyStopped","id":"node_oclikgcutb1","docId":"doclr7fg2ab","props":{"baseConfig":{"messages":{"not_start":{"languKey":"not_start","value":"问卷未开始"},"closed":{"languKey":"closed","value":"问卷已结束"},"pause":{"languKey":"pause","value":"问卷已暂停"},"ip_limited":{"languKey":"ip_limited","value":"您不可作答"},"repeat":{"languKey":"repeat","value":"您已作答"},"limited":{"languKey":"limited","value":"您不可作答"},"preview_type":"not_start"},"image":{"languKey":"survey_stopped_image","value":"https://cmscdn.papegames.com/cmssurvey/static/98211dc28fd3a686e5ad760d5cb03306.png"}},"skinConfig":{"showSkin":false,"skin":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png"},"bgColor":""},"settingsProps":{"baseRuleConfig":{"loginType":"0","timeLimitConfig":{"isTimeLimit":false},"isEndPreview":false,"isGoOnAnswer":true,"answerTimesConfig":{"limitType":1,"times":1},"languageList":["zh-tw"],"deliverList":["港澳台"]},"giftConfig":{"isGiveOutByCms":false,"giveOutType":"","preAwardConfig":null,"redeemConfig":null},"answerLimitConfig":{"limitType":""},"zoneIds":[5,15],"materialsConfig":{"autoLatestMaterial":true,"materialVersion":"0.2.7-alpha.3"},"footerConfig":{"url":"https://assets.papegames.com/umd/deepspace-footer-other/index.min.js","name":"deepspace-footer-other"},"sourceConfig":{"citysUrl":"","agreements":[]}},"clientid":"1067"},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{}}]},{"componentName":"Page","id":"node_ocMi8_K3dA-","docId":"docMi8_K3dA-","props":{},"hidden":false,"title":"缺省页","isLocked":false,"condition":true,"conditionGroup":"","fileName":"not_found","meta":{"title":"缺省页","router":"/not_found"},"children":[{"componentName":"SurveyNotFound","id":"node_oclikgk7fs1","docId":"doclr7fg2cp","props":{"baseConfig":{"image":{"languKey":"base_config_image","value":"https://cmscdn.papegames.com/cmssurvey/static/3f2b5ceb8157f153f0447febedb4eeb1.png"},"message":{"languKey":"base_config_message","value":"您要查看的网页暂时不可访问，请刷新一下或稍后进行尝试～"}},"skinConfig":{"showSkin":false,"skin":{"isCustomSkinUrl":"0","skinUrl":"https://cmscdn.papegames.com/cmssurvey/static/10397353c513380d4220c1769e25ed33.png"},"bgColor":""},"settingsProps":{"baseRuleConfig":{"loginType":"0","timeLimitConfig":{"isTimeLimit":false},"isEndPreview":false,"isGoOnAnswer":true,"answerTimesConfig":{"limitType":1,"times":1},"languageList":["zh-tw"],"deliverList":["港澳台"]},"giftConfig":{"isGiveOutByCms":false,"giveOutType":"","preAwardConfig":null,"redeemConfig":null},"answerLimitConfig":{"limitType":""},"zoneIds":[5,15],"materialsConfig":{"autoLatestMaterial":true,"materialVersion":"0.2.7-alpha.3"},"footerConfig":{"url":"https://assets.papegames.com/umd/deepspace-footer-other/index.min.js","name":"deepspace-footer-other"},"sourceConfig":{"citysUrl":"","agreements":[]}},"clientid":"1067"},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","index":0,"parent":{}}]}],"i18n":{},"config":{"historyMode":"hash","targetRootID":"J_Container","layout":{"componentName":null,"props":{"logo":"...","name":"问卷调查"}}},"meta":{"name":"问卷调查","project_name":"问卷调查","description":"问卷调查","spma":"spa23d","creator":"moling"},"clientid":"1067"}`
	var schemaObj = new(types.Schema)
	err := sonic.UnmarshalString(schema, &schemaObj)
	if err != nil {
		t.Fatalf("TestUnmarshalSchema err:%+v", err)
	} else {
		t.Log("success")
	}
}

func TestGetSurveyIdByHashCode(t *testing.T) {
	//var targetCode = "1vb0ic45ryl"// 668
	//var targetCode = "0pe3z0aqif5"// 560
	//var targetCode = "8itbsayv6d3" // 554
	var targetCode = "q9fklb8dy4p" // 1275
	//targetCode = "4pvv7djhro5"     // 1357 - test， 可导出
	targetCode = "r47mt7dciap"
	for i := 0; i < 5000; i++ {
		hashCode, _ := base.SurveyIdToHashCode(int64(i))
		if targetCode == hashCode {
			t.Logf("surveyID:%+v", i)
			break
		}
	}
}

func TestName111(t *testing.T) {
	t.Log(base.HashCodeToSurveyId("bb7mt7dciap"))
}

func TestSurveyIDToHashCode(t *testing.T) {
	var surveyList = []int64{
		1349,
	}

	for _, i2 := range surveyList {
		code, _ := base.SurveyIdToHashCode(i2)
		t.Log(i2, code)
	}
}

func TestExportRedisLock(t *testing.T) {
	redisKey := fmt.Sprintf("AWE_SURVEY_EXPORT_TASK_%d", 1)

	rdbLock := database.GetRdb().SetNX(context.TODO(), redisKey, 1, time.Minute*10)
	if !rdbLock.Val() {
		t.Log("fail")
	} else {
		t.Log("success")
	}
}
func TestGenProtoSurveyGroup(t *testing.T) {
	var data = &proto2.CmsSurveyGroupInfo{
		Id:        1,
		Clientid:  "1008",
		Name:      "问卷组1",
		IsPublish: 0,
		Type:      1,
		Settings:  "",
		HashCode:  "xxxx",
		IsDelete:  0,
		Ctime:     "2024-10-18 15:28:53",
		Mtime:     "2024-10-18 15:28:57",
		Creator:   "v-jiangpeng",
		Editor:    "v-jiangpeng",
	}
	bs, _ := json.Marshal(data)

	t.Log(string(bs))

	res := &proto2.SurveyGroupListRes{
		List:  []*proto2.CmsSurveyGroupInfo{data},
		Total: 1,
	}

	bs1, _ := json.Marshal(res)
	t.Log(string(bs1))
}

func TestCreateSurveyGroupRecordTable(t *testing.T) {
	err := model.CreateSurveyGroupRecordTable("2222")
	t.Logf("err:%+v", err)
}

func TestMultiGo(t *testing.T) {
	var g = sync.WaitGroup{}

	var ids = []int64{1, 2, 3, 4, 5, 6, 7, 8, 9}
	for _, id := range ids {
		go func(surveyRecordId int64) {
			g.Add(1)
			defer g.Done()
			if r := recover(); r != nil {
				t.Logf("xx:%+v", r)
			}
			fmt.Printf("id:%+v\n", surveyRecordId)
		}(id)
	}
	g.Wait()
	t.Log("Finished")
}

func TestCheckSurveyGroupFormat(t *testing.T) {
	settings := `{"group_survey":{"zh-cn":{"survey_id":1,"hash_code":"gs#xxxx"},"zh-tw":{"survey_id":2,"hash_code":"gs#xxxx"},"jp":{"survey_id":3,"hash_code":"gs#xxxx"}},"extra":{}}`
	err := service.CheckSurveyGroupFormat(settings)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	t.Log("finished")
}

func TestGetCountryLang(t *testing.T) {
	var (
		baseUrl = "https://awe-kpm-dev.diezhi.net"
		//baseUrl = "https://awe-kpm-test.diezhi.net/"
		//countryUrl = "/v1/kpm/global_country_code/list"
		areUrl = "/v1/kpm/area/list"
	)

	type Request struct {
		//Filter struct {
		//} `json:"filter,omitempty"`
		ClientId int    `json:"client_id,omitempty"`
		Area     string `json:"area,omitempty"`
		Currency string `json:"currency,omitempty"`
		Lang     string `json:"lang,omitempty"`
		PageNo   int    `json:"page_no,omitempty"`
		PageSize int    `json:"page_size,omitempty"`
	}

	req := &Request{
		ClientId: 2008,
		PageNo:   1,
		PageSize: 100,
	}

	bs, _ := json.Marshal(req)

	var res any
	response, err := xresty.New().R().SetCookie(&http.Cookie{
		Name:  "gos_auth_key",
		Value: Cookie,
	}).SetBody(bs).SetResult(&res).Post(baseUrl + areUrl)

	t.Logf("res:%+v err:%+v response:%+v", res, err, response.String())
}

func TestUnmarshal(t *testing.T) {
	str := `{"group_survey":{"zh-cn":{"survey_id":1302,"hash_code":"h377a6jhrcb"},"zh-tw":{"survey_id":1301,"hash_code":"dl11z5d28qn"},"jp":{"survey_id":1301,"hash_code":"dl11z5d28qn"}},"extra":{}}`
	var dd = new(types.SurveyGroupSetting)
	err := sonic.UnmarshalString(str, &dd)
	if err != nil {
		t.Logf("err:%+v", err)
	}
}

func TestGenSurveyExportHeadersRes(t *testing.T) {
	res := &proto2.SurveyExportHeadersRes{
		List: make([]*proto2.SurveyExportHeadersRes_Question, 0),
	}

	res.List = append(res.List, &proto2.SurveyExportHeadersRes_Question{
		QuestionUniqueKey: "aaaaaaaaaaa",
		QuestionTitle:     "请评价下列游戏",
		StatisticsMethod:  "matrix",
		QuestionType:      "select",
		SelectMode:        "single",
		QuestionId:        1,
		QuestionRowTitles: []*proto2.SurveyExportHeadersRes_QuestionRowTitle{
			{
				RowUniqueKey: "bbbbb",
				RoleTitle:    "闪耀暖暖",
			},
			{
				RowUniqueKey: "ccccc",
				RoleTitle:    "恋与深空",
			},
		},
		SelectOptions: []*proto2.SurveyExportHeadersRes_SelectOptions{
			{
				Value: "ddddddd1",
				Label: "一般",
			},
			{
				Value: "ddddddd2",
				Label: "满意",
			},
			{
				Value: "ddddddd3",
				Label: "喜欢",
			},
			{
				Value: "ddddddd34",
				Label: "没玩过",
			},
		},
	})
	bs, err := json.Marshal(res)
	if err != nil {
		t.Fatalf("%+v", err)
	}
	t.Log(string(bs))
}
