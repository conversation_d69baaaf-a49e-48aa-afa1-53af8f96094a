package constants

import "survey/types"

var DefaultDeliverList = []types.Deliver{
	{
		DefaultLanguage:     "zh-cn",
		Region:              "大陆",
		OfficialWebsiteHost: "papegames.com",
		SurveyHost:          "papegames.com",
	},
}

var DeliverConfigList = []types.DeliverList{
	{
		Clients: []string{"2008"},
		Name:    "闪暖-不停服测试1",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "www.papegames.cn",
				SurveyHost:          "papegames.cn",
			},
			{
				DefaultLanguage:     "zh-tw",
				Region:              "港澳台",
				OfficialWebsiteHost: "papegames.com",
				SurveyHost:          "papegames.com",
			},
			{
				DefaultLanguage:     "en-us",
				Region:              "欧美",
				OfficialWebsiteHost: "papegames.com",
				SurveyHost:          "papegames.com",
			},
			{
				DefaultLanguage:     "ja-jp",
				Region:              "日本",
				OfficialWebsiteHost: "papegames.com",
				SurveyHost:          "papegames.com",
			},
			{
				DefaultLanguage:     "ko-kr",
				Region:              "韩国",
				OfficialWebsiteHost: "papegames.com",
				SurveyHost:          "papegames.com",
			},
		},
	},
	{
		Clients: []string{"1008"},
		Name:    "闪暖-国服",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "www.papegames.cn",
				SurveyHost:          "papegames.cn",
			},
		},
	},
	{
		Clients: []string{"1019"},
		Name:    "闪暖-台服",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-tw",
				Region:              "港澳台",
				OfficialWebsiteHost: "www.nikki4.com.tw",
				SurveyHost:          "nikki4.com.tw",
			},
		},
	},
	{
		Clients: []string{"1037"},
		Name:    "闪暖-欧美",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "en-us",
				Region:              "欧美",
				OfficialWebsiteHost: "nikki4.playpapergames.com",
				SurveyHost:          "paperdb.com",
			},
			{
				DefaultLanguage:     "en-us",
				Region:              "东南亚",
				OfficialWebsiteHost: "nikki4.playpapergames.com",
				SurveyHost:          "paperdb.com",
			},
		},
	},
	{
		Clients: []string{"1035"},
		Name:    "闪暖-日本",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "ja-jp",
				Region:              "日本",
				OfficialWebsiteHost: "www.shiningnikki.jp",
				SurveyHost:          "paperdb.com",
			},
		},
	},
	{
		Clients: []string{"1095"},
		Name:    "闪暖-越南",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "vi-vn",
				Region:              "越南",
				OfficialWebsiteHost: "",
				SurveyHost:          "paperdb.com",
			},
		},
	},
	{
		Clients: []string{"1001"},
		Name:    "恋与制作人",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "evol.papegames.cn",
				SurveyHost:          "papegames.com",
			},
		},
	},
	{
		Clients: []string{"1011"},
		Name:    "恋与制作人台湾",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-tw",
				Region:              "台湾",
				OfficialWebsiteHost: "evol.papegames.cn",
				SurveyHost:          "nikkisoft.com",
			},
		},
	},
	{
		Clients: []string{"1017"},
		Name:    "恋与制作人日本",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "ja-jp",
				Region:              "日本",
				OfficialWebsiteHost: "evolxlove.papegames.co.jp",
				SurveyHost:          "nikkisoft.com",
			},
		},
	},
	{
		Clients: []string{"1030"},
		Name:    "无期迷途-国服",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "wqmt.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
		},
	},
	{
		Clients: []string{"1061"},
		Name:    "无期迷途-海外",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-tw",
				Region:              "港澳台",
				OfficialWebsiteHost: "ptn.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
			{
				DefaultLanguage:     "en-us",
				Region:              "欧美",
				OfficialWebsiteHost: "ptn.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
			{
				DefaultLanguage:     "ja-jp",
				Region:              "日本",
				OfficialWebsiteHost: "ptn.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
			{
				DefaultLanguage:     "ko-kr",
				Region:              "韩国",
				OfficialWebsiteHost: "ptn.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
			{
				DefaultLanguage:     "en-us",
				Region:              "东南亚",
				OfficialWebsiteHost: "ptn.aisnogames.com",
				SurveyHost:          "shziyi.com",
			},
		},
	},
	{
		Clients: []string{"1033"},
		Name:    "恋与深空-研发",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "deepspace.papegames.com",
				SurveyHost:          "papegames.com",
			},
		},
	},
	{
		Clients: []string{"1068"},
		Name:    "恋与深空-国服",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-cn",
				Region:              "大陆",
				OfficialWebsiteHost: "deepspace.papegames.com",
				SurveyHost:          "papegames.com",
			},
		},
	},
	{
		Clients: []string{"1067", "1067"},
		Name:    "恋与深空-海外",
		DeliverList: []types.Deliver{
			{
				DefaultLanguage:     "zh-tw",
				Region:              "港澳台",
				OfficialWebsiteHost: "loveanddeepspace.infoldgames.com",
				SurveyHost:          "infoldgames.com",
			},
			{
				DefaultLanguage:     "en-us",
				Region:              "欧美",
				OfficialWebsiteHost: "loveanddeepspace.infoldgames.com",
				SurveyHost:          "infoldgames.com",
			},
			{
				DefaultLanguage:     "ja-jp",
				Region:              "日本",
				OfficialWebsiteHost: "loveanddeepspace.infoldgames.com",
				SurveyHost:          "infoldgames.com",
			},
			{
				DefaultLanguage:     "ko-kr",
				Region:              "韩国",
				OfficialWebsiteHost: "loveanddeepspace.infoldgames.com",
				SurveyHost:          "infoldgames.com",
			},
		},
	},
}
