package config

import (
	"sync/atomic"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type Config struct {
	Host                        string         `xconf:"host"`
	Register                    bool           `xconf:"register"`
	OssCfg                      OssCfg         `xconf:"ossCfg"`
	Env                         string         `xconf:"env"`
	GosCfg                      GosCfg         `xconf:"gosCfg"`
	CmsMajorUrl                 string         `xconf:"cmsMajorUrl"`
	CmsMajorZoneListUri         string         `xconf:"cmsMajorZoneListUri"`
	NodeEnv                     string         `xconf:"NODE_ENV"` // todo NodeEnv 与 Env 是否可以合并
	NodeCfg                     NodeCfg        `xconf:"nodeCfg"`
	SurveyImpUrl                string         `xconf:"surveyImpUrl"`
	AuthUrl                     string         `xconf:"authUrl"`
	CitysUrl                    string         `xconf:"citysUrl"`
	SurveyAuth                  SurveyAuth     `xconf:"surveyAuth"`
	AuthIgnore                  []string       `xconf:"authIgnore"`
	StatisticUseMysqlData       bool           `xconf:"statisticUseMysqlData"`
	StatisticUseMysqlDataWithMV bool           `xconf:"statisticUseMysqlData"` // 使用物化
	ExportCompressPath          string         `xconf:"exportCompressPath"`    // 导出任务，文件生成、压缩路径
	DetailApiUseNode            bool           `xconf:"detailApiUseNode"`
	SurveyRecordExportQueue     string         `xconf:"surveyRecordExportQueue"`
	MethodApiUseNode            bool           `xconf:"methodApiUseNode"`
	BI                          BI             `xconf:"bi"`
	ExportTaskConf              ExportTaskConf `xconf:"exportTaskConf"`
	Overwrite                   Overwrite      `xconf:"overwrite"`
	UserQuery                   UserQuery      `xconf:"userQuery"`
	SurveyFrontHost             string         `xconf:"surveyFrontHost"`
}

type SurveyAuth struct {
	// 不鉴权白名单 url-path
	IgnoreAuthenticationWhitelist []string `xconf:"ignoreAuthenticationWhitelist"`
}

type SchedulerX struct {
	Endpoint  string `xconf:"endpoint"`
	Namespace string `xconf:"namespace"`
	GroupId   string `xconf:"groupId"`
	AppKey    string `xconf:"appKey"`
}

type BI struct {
	User        string `xconf:"user"`
	ClientId    string `xconf:"client_id"`
	BiSource    string `xconf:"bi_source"`
	Url         string `xconf:"url"`
	RecordTable string `xconf:"record_table"`
	DetailTable string `xconf:"detail_table"`
}

type OssCfg struct {
	AccessKeyId     string `xconf:"accessKeyId"`
	AccessKeySecret string `xconf:"accessKeySecret"`
	Bucket          string `xconf:"bucket"`
	Endpoint        string `xconf:"endpoint"`
	RoleArn         string `xconf:"roleArn"`
	Cdn             string `xconf:"cdn"`
	BasePath        string `xconf:"basePath"`
}

type GosCfg struct {
	CookieName string `xconf:"cookieName"`
	Secret     string `xconf:"secret"`
	Host       string `xconf:"host"`
	UrlMap     struct {
		PermUrl         string `xcownf:"permUrl"`
		OperationLogUrl string `xconf:"operationLogUrl"`
	} `xconf:"urlMap"`
}

type NodeCfg struct {
	Host                         string `xconf:"host"`
	StatisticsDetailUrl          string `xconf:"statisticsDetailUrl"`
	StatisticsInputMethodListUrl string `xconf:"statisticsInputMethodListUrl"`
}

type ExportTaskConf struct {
	QueryRecordNum int  `xconf:"queryRecordNum"`
	FileDataLimit  int  `xconf:"fileDataLimit"`
	RandomLimit    int  `xconf:"randomLimit"`
	NoPrintCkSQL   bool `xconf:"noPrintCkSQL"`
}

type Overwrite struct {
	ClientId     []int64 `xconf:"clientId"`
	SyncUrl      string  `xconf:"syncUrl"`
	SyncGroupUrl string  `xconf:"syncGroupUrl"`
}

type UserQuery struct {
	Host             string `xconf:"host"`
	BiEnv            string `xconf:"bi_env"` //dev,test 都配置test，BI无dev
	ClusterThreadNum int32  `xconf:"cluster_thread_num"`
	ClusterEntityNum int32  `xconf:"cluster_entity_num"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		Host:     "",
		Register: false,
		ExportTaskConf: ExportTaskConf{
			QueryRecordNum: 40000,
			FileDataLimit:  160000,
			RandomLimit:    50000,
		},
		UserQuery: UserQuery{
			ClusterThreadNum: 5,
			ClusterEntityNum: 200,
		},
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(c)
	return nil
}
