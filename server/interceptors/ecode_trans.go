package interceptors

import (
	"errors"

	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
)

// EcodeTrans 错误码转换，抛出错误详情
func EcodeTrans() xgin.Interceptor {
	return func(next xgin.Handler) xgin.Handler {
		return func(c *gin.Context) (interface{}, ecode.Code) {
			r, err := next(c)
			if errors.Is(err, ecode.BadRequest) {
				var status *ecode.Status
				ok := errors.As(err, &status)
				if ok {
					errMsg := "Parameter error: " + status.Err.Error()
					return r, ecode.Error(status, errMsg)
				}
			}
			return r, err
		}
	}
}
