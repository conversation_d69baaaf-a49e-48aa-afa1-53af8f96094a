package server

import (
	"gitlab.papegames.com/awe/awe_bff/shared/pkg/hooks/xcors"
	"survey/config"
	"survey/control"
	"survey/proto"
	"survey/server/interceptors"
	"survey/util/userpkg"

	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/log"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
)

var ginServer = xgin.NewServer()

func Get() *xgin.Server { return ginServer }

func Startup() error {
	hooks.Append(xcors.New())
	hooks.Append(log.New())
	ginServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().Host),
		server.ServiceRegistrar(config.Get().Register),
	)
	eng := ginServer.GetGinEngine()
	xgin.RegisterInterceptor(eng, userpkg.CheckLogin(config.Get().AuthIgnore), interceptors.EcodeTrans())
	proto.RegisterSurveyServiceGinServer(ginServer, control.Get())
	return nil
}
