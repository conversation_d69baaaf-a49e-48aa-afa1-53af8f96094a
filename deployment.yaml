apiVersion: v1
data:
  conf.yaml: |
    sparrow:
      configure:
        provider: "apollo"
        watch: true
        path: "application"
        config:
          endpoints:
            - "http://*************:8080"
          appID: "awe-survey"
          cluster: "default"
          namespace: "application"
          secret: "e7ec8d7ee690420bb914a768eabc4e4a"
kind: ConfigMap
metadata:
  name: awe-survey
  namespace: platsdk
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: awe-survey
  name: awe-survey
  namespace: platsdk
spec:
  ports:
    - name: http
      port: 8090
      protocol: TCP
      targetPort: http
  selector:
    app: awe-survey
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: awe-survey
  name: awe-survey
  namespace: platsdk
spec:
  replicas: 1
  selector:
    matchLabels:
      app: awe-survey
  template:
    metadata:
      annotations:
        k8s.aliyun.com/eci-eviction-enable: 'true'
        k8s.aliyun.com/eci-extra-ephemeral-storage: 100G
        prometheus.io/path: /metrics
        prometheus.io/port: '4006'
        prometheus.io/scrape: 'true'
        sidecar.istio.io/inject: 'false'
      labels:
        app: awe-survey
    spec:
      containers:
        - env:
            - name: APP_NAME
              value: awe-survey
            - name: APP_ENV
              value: dev
            - name: ROLE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['role']
            - name: TZ
              value: Asia/Shanghai
          image: dev-registry-vpc.cn-hangzhou.cr.aliyuncs.com/platform/awe-survey:VERSION
          imagePullPolicy: Always
          livenessProbe:
            initialDelaySeconds: 45
            periodSeconds: 12
            tcpSocket:
              port: 8090
            timeoutSeconds: 5
          name: awe-survey
          ports:
            - containerPort: 8090
              name: http
              protocol: TCP
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            tcpSocket:
              port: 8090
            timeoutSeconds: 5
          securityContext: { }
          volumeMounts:
            - mountPath: /data/logs
              name: k8s-log
            - mountPath: /app/conf.yaml
              name: awe-survey
              subPath: conf.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: lingyun
      restartPolicy: Always
      securityContext: { }
      serviceAccountName: default
      terminationGracePeriodSeconds: 45
      volumes:
        - emptyDir: { }
          name: k8s-log
        - configMap:
            name: awe-survey
          name: awe-survey
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: awe-survey
  namespace: netops
spec:
  gateways:
    - common-inbound-gateway
  hosts:
    - survey-manage.papegames.it
    - survey-manage-dev.diezhi.net
    - awe-survey-dev.diezhi.net
  http:
    - route:
        - destination:
            host: awe-survey.platsdk.svc.cluster.local
            port:
              number: 8090
          weight: 100
