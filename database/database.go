package database

import (
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xredis"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var sql *xgorm.DB

var rdb *xredis.Client
var clickhouse driver.Conn

func GetClickhouse() driver.Conn { return clickhouse }
func Get() *xgorm.DB             { return sql }

func GetRdb() *xredis.Client { return rdb }

func Startup() error {
	var err error

	sql, err = xgorm.StdConfig().WithInterceptor(
		xgorm.TraceInterceptor(),
		xgorm.DebugInterceptor(),
	).Build()
	if err != nil {
		xlog.Error("database.Startup with error",
			xlog.Err(err))
		return err
	}

	rdb, err = xredis.StdConfig().Build()
	if err != nil {
		xlog.Error("cache.Startup failed", xlog.Err(err))
		return err
	}
	rdb.AddHook(xredis.TracingHook())
	clickhouse, err = StdConfig().Build()
	if err != nil {
		xlog.Error("clickhouse.Startup failed", xlog.Err(err))
		return err
	}
	return nil
}
