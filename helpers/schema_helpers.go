package helpers

import "survey/types"

func FlattenComponentsTree(root *types.ComponentsTree) []types.ComponentsTree {
	if root == nil {
		return nil
	}

	var flattened []types.ComponentsTree
	var queue []types.ComponentsTree

	// 使用指针来初始化队列
	queue = append(queue, *root)

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:] // 移除队列的第一个元素

		// 检查当前节点是否是问题组件
		if current.Props.ConfigProps.IsQuestion {
			flattened = append(flattened, current)
		}

		// 将所有子节点添加到队列中
		for _, child := range current.Children {
			queue = append(queue, child)
		}
	}

	return flattened
}
