package helpers

import (
	"survey/types"
	"testing"
)

func TestFlattenComponentsTree(t *testing.T) {
	// 构建样例树
	tree := &types.ComponentsTree{
		ComponentName: "Root",
		Children: []types.ComponentsTree{
			{
				ComponentName: "Child1",
				Children: []types.ComponentsTree{
					{ComponentName: "Grandchild1"},
					{ComponentName: "Grandchild2"},
				},
			},
			{
				ComponentName: "Child2",
				Children: []types.ComponentsTree{
					{ComponentName: "Grandchild3"},
				},
			},
		},
	}

	// 预期输出，匹配宽度优先搜索的结果
	expected := []types.ComponentsTree{
		{ComponentName: "Root"},
		{ComponentName: "Child1"},
		{ComponentName: "Child2"},
		{ComponentName: "Grandchild1"},
		{ComponentName: "Grandchild2"},
		{ComponentName: "Grandchild3"},
	}

	// 测试FlattenComponentsTree函数
	result := FlattenComponentsTree(tree)
	if len(result) != len(expected) {
		t.Errorf("Expected result length %d, got %d", len(expected), len(result))
	}

	// 检查组件名称是否符合预期
	for i, component := range result {
		if component.ComponentName != expected[i].ComponentName {
			t.Errorf("Expected component name %s at index %d, got %s", expected[i].ComponentName, i, component.ComponentName)
		}
	}
}
