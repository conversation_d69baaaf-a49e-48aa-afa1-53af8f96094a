package helpers

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"strconv"
)

type RecordDetail map[string][]OptionItem

type OptionItem struct {
	Option string `json:"option"`
	Text   string `json:"text"`
}

//func RecordListData(m map[string]string) (string, error) {
//	recordDetail := make(RecordDetail)
//	for key, value := range m {
//		recordDetail[key] = []OptionItem{{Option: value}}
//	}
//
//	jsonData, err := json.Marshal(recordDetail)
//	if err != nil {
//		return "", err
//	}
//	return string(jsonData), nil
//}

func CreateSpecialLengthArray(len int) []string {
	if len <= 0 {
		return []string{}
	}

	// Initialize the slice with empty strings.
	array := make([]string, len)
	for i := range array {
		array[i] = ""
	}
	return array
}

func GetComponentTitleByComponentName(name string) string {
	var nameMaps = map[string]string{
		"SurveyWrapper":            "问卷容器",
		"NonSurveyWrapper":         "容器",
		"SurveyCompleted":          "完成答题",
		"SurveyNotFound":           "缺省页",
		"SurveyStopped":            "停止答题",
		"LegalProvisions":          "法务条款",
		"QuestionTitle":            "问卷标题",
		"QuestionDesc":             "问卷副标题",
		"QuestionRemark":           "备注",
		"QuestionWrapper":          "添加分页",
		"QuestionRadio":            "列表单选",
		"QuestionSelect":           "下拉单选",
		"QuestionDatePicker":       "时间选择",
		"QuestionProvinceCityZone": "省市区选择",
		"QuestionCheckbox":         "列表多选",
		"QuestionOrderCheckbox":    "多选排序",
		"QuestionInput":            "单行文本",
		"QuestionTextarea":         "多行文本",
		"QuestionNumber":           "数字",
		"QuestionEmail":            "邮箱",
		"QuestionPhone":            "手机号",
		"QuestionIDCard":           "身份证号",
		"QuestionRate":             "星级打分",
		"QuestionGauge":            "量表打分",
		"QuestionRadiosMatrix":     "矩阵单选",
		"QuestionCheckboxsMatrix":  "矩阵多选",
		"QuestionCheckboxMatrix":   "矩阵多选",
		"QuestionGaugeMatrix":      "矩阵量表",
	}
	return nameMaps[name]
}

func GetProportion(count, counts uint64, prec int) string {
	if count <= 0 || count/counts > 0 {
		return xcast.ToString(count)
	}

	var res = float64(count) / float64(counts)

	return strconv.FormatFloat(res, 'f', prec, 64)
}

// Reduce is a function that mimics the behavior of Array.prototype.reduce in JavaScript.
func Reduce(slice []interface{}, callback func(accumulator, currentValue interface{}, currentIndex int) interface{}, initialValue interface{}) interface{} {
	accumulator := initialValue

	if initialValue == nil && len(slice) > 0 {
		accumulator = slice[0]
		slice = slice[1:]
	}

	for i, v := range slice {
		accumulator = callback(accumulator, v, i)
	}

	return accumulator
}

// ReverseSlice 是一个函数，用于反转任何类型的切片。
func ReverseSlice[T any](s []T) []T {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
	return s
}
