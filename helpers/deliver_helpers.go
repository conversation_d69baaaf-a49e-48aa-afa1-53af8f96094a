package helpers

import (
	"gorm.io/gorm/utils"
	"survey/config"
	"survey/constants"
	"survey/proto"
	"survey/types"
	"survey/util"
)

// GenSurveyWebPath 基于客户端租户 id 和区域生成 web path
func GenSurveyWebPath(hashCode string, deliverList []string, clientID string) []*proto.WebPath {
	if hashCode == "" {
		return nil
	}

	list := constants.DefaultDeliverList
	for _, item := range constants.DeliverConfigList {
		if util.Contains(item.Clients, clientID) {
			filtered := filterDeliverList(item.DeliverList, deliverList)
			if len(filtered) > 0 {
				list = filtered
				break
			}
		}
	}

	env := config.Get().Env
	var webPaths []*proto.WebPath
	for _, item := range list {
		webPathHead := "survey-test"
		webPathMiddle := "papegames.com"
		if env == "pre" {
			webPathHead = choosePrefix(item.Region, "survey-pre", "survey-oversea-pre")
			webPathMiddle = item.SurveyHost
		} else if env == "prod" {
			webPathHead = "survey"
			webPathMiddle = item.SurveyHost
		}
		webPaths = append(webPaths, &proto.WebPath{
			Region:  item.Region,
			WebPath: "https://" + webPathHead + "." + webPathMiddle + "/" + hashCode,
		})
	}
	return webPaths
}

// filterDeliverList 根据区域过滤交付列表
func filterDeliverList(list []types.Deliver, regions []string) []types.Deliver {
	var filtered []types.Deliver
	for _, item := range list {
		if utils.Contains(regions, item.Region) {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

// choosePrefix 基于 web 路径的前缀选择区域
func choosePrefix(region, defaultPrefix, overseaPrefix string) string {
	if region == "大陆" {
		return defaultPrefix
	}
	return overseaPrefix
}
