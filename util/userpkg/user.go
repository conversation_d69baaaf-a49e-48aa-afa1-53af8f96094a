package userpkg

import (
	"context"
	"encoding/base64"
	"strings"
	"survey/config"
	"survey/proto"
	"survey/util/errors"

	"github.com/bytedance/sonic"
	"github.com/golang-jwt/jwt/v5"

	"github.com/gin-gonic/gin"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type AdminUser struct {
	Nickname string `json:"nickname"`
	Username string `json:"username"`
	UserId   string `json:"user_id"`
	Avatar   string `json:"avatar"`
	Email    string `json:"email"`
}

const CtxUserKey = "ctx_user_info_key"

// 鉴权
func CheckLogin(authIgnore []string) xgin.Interceptor {
	return func(next xgin.Handler) xgin.Handler {
		return func(ginCtx *gin.Context) (interface{}, ecode.Code) {

			// 鉴权白名单
			ignorePaths := append(authIgnore, "/v1/survey/health")
			if ignorePath(ginCtx, ignorePaths) {
				return next(ginCtx)
			}

			var nonAuthenticationWhitelist = config.Get().SurveyAuth.IgnoreAuthenticationWhitelist
			for _, path := range nonAuthenticationWhitelist {
				if ginCtx.Request.URL.Path == path {
					xlog.Info("url is not need check login", xlog.String("url", ginCtx.Request.URL.Path))
					return next(ginCtx)
				}
			}

			cookieName := config.Get().GosCfg.CookieName
			secret := []byte(config.Get().GosCfg.Secret) // Convert secret to []byte
			cookieVal, err := ginCtx.Request.Cookie(cookieName)

			if err != nil {
				xlog.Error("get cookie error", xlog.Err(err))
				return nil, ecode.Unauthenticated
			}

			tokenString := cookieVal.Value

			token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
				if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
					xlog.Error("Unexpected signing method: %v", xlog.Any("token.Header", token.Header["alg"]))

					return nil, errors.ErrCookieParseCode
				}
				return secret, nil
			})

			if err != nil || !token.Valid {
				xlog.Error("cookie is valid", xlog.Any("tokenString", tokenString), xlog.Any("token", token), xlog.Any("err", err))
				return nil, ecode.Unauthenticated
			}

			adminUser := AdminUser{
				Nickname: token.Claims.(jwt.MapClaims)["nickname"].(string),
				Username: token.Claims.(jwt.MapClaims)["username"].(string),
				UserId:   token.Claims.(jwt.MapClaims)["userId"].(string),
				Avatar:   token.Claims.(jwt.MapClaims)["avatar"].(string),
			}

			ginCtx.Set(CtxUserKey, adminUser)

			err = ginCtx.Request.ParseForm()
			if err != nil {
				xlog.FromContext(xgin.Context(ginCtx)).Error("hooks.decrypt: parse form failed", xlog.Err(err))
				return nil, ecode.BadRequest
			}

			return next(ginCtx)
		}
	}
}

func GetUserInfoFromCtx(ctx context.Context) (user *AdminUser, err error) {
	adminUser := AdminUser{}
	ginCtx := xgin.FromContext(ctx)
	u, ok := ginCtx.Get(CtxUserKey)
	if ok {
		adminUser = u.(AdminUser)
		return &adminUser, nil
	}
	return nil, errors.WrapStr(errors.ErrGetUserInfoFailed, "get user info from ctx error")
}

func base64Decode(token string) (string, error) {
	decodeBytes, err := base64.RawStdEncoding.DecodeString(token)
	if err != nil {
		return "", err
	}
	return string(decodeBytes), err

}

func tokenDecode(token string) (*proto.JwtUserInfoV1_Data, error) {
	tkData := strings.Split(token, ".")[1]
	decodeStr, err := base64Decode(tkData)
	if err != nil {
		return nil, err
	}
	var userInfo proto.JwtUserInfoV1
	sonic.UnmarshalString(decodeStr, &userInfo)
	return userInfo.Data, nil
}
func ignorePath(c *gin.Context, paths []string) bool {
	if len(paths) == 0 {
		return false
	}
	for _, p := range paths {
		if strings.HasPrefix(c.Request.URL.Path, p) {
			return true
		}
	}
	return false
}
