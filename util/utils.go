package util

import (
	"archive/zip"
	"fmt"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Contains checks if a string is in a slice of strings.
func Contains(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

func TimeIsZero(t *time.Time) bool {
	return t == nil || t.<PERSON>()
}

func CompressZip(dir, filePath string) error {
	zipFile, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer zipFile.Close()
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()
	return filepath.WalkDir(dir, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		//xlog.Info("path", xlog.String("x", d.Name()))
		if d.Is<PERSON>() {
			return nil
		}
		srcFile, err := os.Open(path)
		if err != nil {
			return err
		}
		defer srcFile.Close()
		f, err := zipWriter.Create(filepath.Base(path))
		if err != nil {
			return err
		}
		_, err = io.Copy(f, srcFile)
		if err != nil {
			return err
		}
		return nil
	})
}

// RemoveRichTextTags 移除Html标签
// @param html string html字符串
// @param keepImgHtml bool 是否保留img标签
func RemoveRichTextTags(html string, keepImgHtml bool) string {
	htmlTool := NewRegexpHtmlTool()

	if !keepImgHtml {
		html, _ = htmlTool.RemoveHTMLTags(html)
		return html
	}

	// 过滤除了img以外的HTML标签
	html, replaces := htmlTool.EncodeImgHtml(html)
	html, _ = htmlTool.RemoveHTMLTags(html)
	html = htmlTool.DecodeImgHtml(html, replaces)

	return html
}

func CsvSpecialValHandle(val string) string {
	if val == "" {
		return ""
	}
	return fmt.Sprintf("%v", val)
	//return fmt.Sprintf("\t%v", val)
}

func ReplaceStrings(str string, replaceArr map[string]string) string {
	for old, newStr := range replaceArr {
		str = strings.ReplaceAll(str, old, newStr)
	}
	return str
}

func MillSecondToFormat(mSecond int64) string {
	return time.UnixMilli(mSecond).Format(time.DateTime)
}

func RFC3339ToDatetime(datetime string) (string, error) {
	t, err := time.Parse(time.RFC3339, datetime)
	if err != nil {
		return "", err
	}
	newTimeStr := t.Format(time.DateTime)
	return newTimeStr, nil
}

func GenRandomsFromMax(max int, total int) map[int]bool {
	rand.Seed(time.Now().UnixNano())
	var usedNumbers = make(map[int]bool)
	for len(usedNumbers) < total {
		randomNumber := rand.Intn(max)
		randomNumber += 1
		if usedNumbers[randomNumber] {
			continue
		}
		usedNumbers[randomNumber] = true
	}
	return usedNumbers
}
