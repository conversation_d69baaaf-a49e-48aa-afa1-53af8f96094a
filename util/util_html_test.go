package util

import (
	"testing"
)

func TestRemoveRichTextTags(t *testing.T) {
	var testText = `
	<p><span style="color: rgb(0, 0, 0);"><strong>【单选题】请问您</strong></span><span style="color: rgb(255, 77, 79);"><strong>开始玩《闪耀暖暖》</strong></span><span style="color: rgb(0, 0, 0);"><strong>的时间是？</strong></span></p><img src="a.png"/>
`
	tags := RemoveRichTextTags(testText, true)
	t.Log(tags)
}

func TestName(t *testing.T) {
	var testText = `
	<p><span style="color: rgb(0, 0, 0);"><strong>【单选题】请问您</strong></span><span style="color: rgb(255, 77, 79);"><strong>开始玩《闪耀暖暖》</strong></span><span style="color: rgb(0, 0, 0);"><strong>的时间是？</strong></span></p><img src="a.png"/>
`
	htmlLib := NewRegexpHtmlTool()
	testText, replaces := htmlLib.EncodeImgHtml(testText)

	testText, _ = htmlLib.RemoveHTMLTags(testText)

	testText = htmlLib.DecodeImgHtml(testText, replaces)

	t.Log(testText)
}
