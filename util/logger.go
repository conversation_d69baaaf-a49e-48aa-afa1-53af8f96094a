package util

import (
	"context"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func Log(ctx context.Context, name string) *xlog.Logger {
	if name == "" {
		return xlog.FromContext(ctx)
	}
	return LogWith(xlog.FromContext(ctx), "[struct]", name)
}

func Logf(ctx context.Context, name string) *xlog.SugaredLogger {
	if name == "" {
		return Log(ctx, "").Sugar()
	}
	return Log(ctx, name).Sugar()
}

func LogWith(logger *xlog.Logger, key, val string) *xlog.Logger {
	if logger == nil {
		return xlog.L().With(
			xlog.String(key, val),
		)
	}
	return logger.With(
		xlog.String(key, val),
	)
}
