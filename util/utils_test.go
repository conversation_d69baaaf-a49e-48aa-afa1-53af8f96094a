package util

import (
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"survey/types"
	"testing"
	"time"
)

func TestCompressZip(t *testing.T) {

	var (
		now      = time.Now().Unix()
		prefix   = "/home/<USER>/go_projects/awe-survey/util/public"
		dir      = prefix + "/280_20240729"
		filePath = fmt.Sprintf(prefix+"/%v.zip", now)
	)
	err := CompressZip(dir, filePath)
	if err != nil {
		t.Fatalf("fail:%+v", err)
	}
}

func TestCsvSpecialValHandle(t *testing.T) {
	str := "-aqRD1lJUe"
	str = CsvSpecialValHandle(str)
	t.Log(str)
}

func TestReplaceStrings(t *testing.T) {
	str := "select * from {tb_record} r where survey_id={field_survey}"

	replaceArr := map[string]string{
		"{tb_record}":    "survey_record",
		"{field_survey}": "1",
	}

	replaceStrings := ReplaceStrings(str, replaceArr)
	t.Log(replaceStrings)
}

func TestMillSecondToFormat(t *testing.T) {
	//unixMilli := time.Now().UnixMilli()
	var unixMilli interface{} = 1724382960000
	a := xcast.ToInt64(unixMilli)
	t.Log(MillSecondToFormat(a))
}

func TestGenRandomsFromMax(t *testing.T) {
	randoms := GenRandomsFromMax(300000, 5000)
	t.Log(randoms)
}

func TestExportExtra(t *testing.T) {
	dd := &types.ExportQueueExtra{
		//UserCluster: []*types.UserCluster{
		//	{
		//		ClusterInfo: &proto2.UserStrategy{
		//			ClusterName: "jp_user_cluser_test02",
		//			EntityName:  "vroleid",
		//			Version:     "",
		//		},
		//		Relation: 1,
		//	},
		//},
		UserTag: []*types.UserTag{
			{
				TagInfo: &types.UserTagInfo{
					TagName:       "jp_tag_test01",
					TagEntityName: "vroleid",
				},
				Relation: 1,
				TagVal:   "标签值1",
			},
		},
	}
	bs, _ := sonic.Marshal(dd)
	_ = bs
	t.Logf("data:%s", bs)
}
