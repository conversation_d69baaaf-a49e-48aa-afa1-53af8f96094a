package base

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"survey/util/userpkg"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

type GormBasic struct {
	Username string           `json:"username"`
	NowTime  *xtype.Timestamp `json:"now_time"`
}

func GetUserAndNow(ctx context.Context) (*GormBasic, error) {
	userInfo, err := userpkg.GetUserInfoFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	return &GormBasic{
		Username: userInfo.Username,
		NowTime: &xtype.Timestamp{
			Seconds: time.Now().Unix(),
			Nanos:   0,
		}}, nil
}

// 定义全局变量的 map
var SURVEY_STATUS_ENUM = map[string]int{
	"UNKNOWN":    0,
	"NO_START":   1,
	"OVER":       2,
	"PROCESSING": 3,
	"PAUSE":      4,
	"CLOSED":     5,
	"DELETED":    6,
	"INIT":       99, // 初始化
}

var DICTIONARY_MATRIX = []string{
	"vje3zaqif5786yc4poxr0ubwntmcdk2h9l1s",
	"isy4pj5l97afqwh1ccumrdev02z6xn8o3bkt",
	"4x0vncoe368lku7tdr5sqa9h2cwfbp1ijzmy",
	"tkxweb2v98f1yarhm75p3s6ccuqn04ojdlzi",
	"6nabo24kcltjxyw137mdq0ev9sr8pfizhu5c",
	"xlr1zd28qnce59musyph7k43afciotjv0w6b",
	"k3f7ajhrcbmx08qictvn195dwo62ysulepz4",
	"0x7mtdciapfweq1bon4vch8sjk236lz95uyr",
	"61c4vr8fx9b5jqko7eutdpiy2nawchmlzs03",
	"1ia657m4ubs0rtdpcw8vhk3x2qznlc9jeofy",
	"axtbsyv6d3jecw8qim5p079uk4z2c1rnhflo",
	"92fkl8dy4pciex5q0za6hnwcjbotrm3v71su",
	"ehb0i45rylpzkfm9wjtv12sx8daucn7co63q",
	"28pv7jhro5m1c36q0fcadsxwy4eubzlti9nk",
	"9b8rd7l61z20afsv4mjck3hwecipyu5nxtqo",
	"6jkrtobpacy3h02fl4ec7u5nms1x9qzdiwv8",
}

func SurveyIdToHashCode(surveyId int64) (string, error) {
	matrix := int64(len(DICTIONARY_MATRIX))
	matrixIndex := surveyId % matrix
	dictionary := DICTIONARY_MATRIX[matrixIndex]
	dictionaryLen := int64(len(dictionary))

	beg := int64(0)
	mod := int64(0)
	var resultStr string
	for surveyId > 0 || beg < 10 {
		mod = surveyId % dictionaryLen
		index := (mod + beg) % dictionaryLen
		str := fmt.Sprintf("%c", dictionary[index])
		resultStr += str
		decimal := surveyId / dictionaryLen
		surveyId = int64(math.Floor(float64(decimal)))
		beg++
	}
	str := resultStr[0:5] + strconv.FormatInt(int64(matrixIndex), 16) + resultStr[5:10]
	return str, nil
}

func HashCodeToSurveyId(hashCode string) (int, error) {
	matrixIndex, _ := strconv.ParseInt(hashCode[5:6], 16, 64)
	dictionary := DICTIONARY_MATRIX[matrixIndex]
	dictionaryLen := len(dictionary)
	dictionary2 := strings.Split(DICTIONARY_MATRIX[matrixIndex], "")

	dictionaryMap := make(map[string]int)
	for index, str := range dictionary2 {
		dictionaryMap[str] = index
	}

	hash := hashCode[0:5] + hashCode[6:11]
	var realSiteArr []int
	hashStr := strings.Split(hash, "")
	for index, t := range hashStr {
		site := dictionaryMap[t]
		var realSite int
		if site-index >= 0 {
			realSite = site - index
		} else {
			realSite = site - index + dictionaryLen
		}
		realSiteArr = append(realSiteArr, realSite)
	}

	var nums int
	for i, j := 0, len(realSiteArr)-1; i < j; i, j = i+1, j-1 {
		// 交换slice[i]和slice[j]的值
		realSiteArr[i], realSiteArr[j] = realSiteArr[j], realSiteArr[i]
	}
	for index, _ := range realSiteArr {
		if index+1 >= len(realSiteArr) {
			break
		}
		nums = nums*dictionaryLen + realSiteArr[index+1]
	}
	return nums, nil
}

// DiffUtcTimeStr 计算两个UTC时间相差秒数
func DiffUtcTimeStr(utcTime1 string, utcTime2 string) (string, error) {
	// 将UTC时间字符串转为time.Time对象
	t1, err := time.Parse(time.DateTime, utcTime1)
	if err != nil {
		return "", err
	}

	t2, err := time.Parse(time.DateTime, utcTime2)
	if err != nil {
		return "", err
	}

	// 将time.Time对象转为Unix时间戳
	timestamp1 := t1.Unix()
	timestamp2 := t2.Unix()

	// 计算两个时间戳的差值
	difference := timestamp2 - timestamp1
	return strconv.FormatInt(difference, 10), nil
}

// 解压
func UnCompress(compressed []byte) (string, error) {

	if len(compressed) == 0 {
		return "", nil
	}

	var b bytes.Buffer
	gz, err := gzip.NewReader(bytes.NewBuffer(compressed))
	if err != nil {
		return "", err
	}
	if _, err = b.ReadFrom(gz); err != nil {
		return "", err
	}
	if err = gz.Close(); err != nil {
		return "", err
	}

	return b.String(), nil
}

// 压缩
func Compress(schema string) ([]byte, error) {

	var b bytes.Buffer
	gz := gzip.NewWriter(&b)
	if _, err := gz.Write([]byte(schema)); err != nil {
		return nil, err

	}
	if err := gz.Close(); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

// json转Map ()
func JSONToMap(str string) (map[string]interface{}, error) {
	var tempMap = make(map[string]interface{})
	err := json.Unmarshal([]byte(str), &tempMap)
	if err != nil {
		return nil, err
	}
	return tempMap, nil
}

// 获取当前时间
func GetNowTime() string {
	timeObj := time.Now()
	return timeObj.Format("2006-01-02 15:04:05")
}

func FormatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

func CalculateTimeDifference(startTimeStr, endTimeStr string) (int64, error) {
	// Define the time layout according to the format of the input strings
	const layout = "2006-01-02 15:04:05"

	// Parse the start time string
	startTime, err := time.Parse(layout, startTimeStr)
	if err != nil {
		return 0, fmt.Errorf("failed to parse start time: %v", err)
	}

	// Parse the end time string
	endTime, err := time.Parse(layout, endTimeStr)
	if err != nil {
		return 0, fmt.Errorf("failed to parse end time: %v", err)
	}

	// Calculate the difference between the two times
	diff := endTime.Sub(startTime)

	// Convert the difference to seconds
	seconds := int64(diff.Seconds())

	return seconds, nil
}

func GetTimeNumStr() string {
	var format = "20060102150405"
	return time.Now().Format(format)
}

func SurveyGroupIdToHashCode(surveyGroupId int64) (string, error) {
	hashCode, err := SurveyIdToHashCode(surveyGroupId)
	return fmt.Sprintf("gs_%v", hashCode), err
}
