package errors

import "gitlab.papegames.com/fringe/sparrow/pkg/ecode"

// Error 异常类型
type Error struct {
	code  int    // 错误码
	msg   string // 错误信息
	cause error  // error
}

func (c *Error) Error() string {
	if c.cause != nil {
		return c.msg + " -> " + c.cause.Error()
	}
	return c.msg
}

// Code 返回错误码
func (c *Error) Value() int {
	return c.code
}

// Msg 返回错误信息
func (c *Error) Msg() string {
	return c.msg
}

// As 判断该错误是否是指定错误
func (c *Error) As(err interface{}) bool {
	if err == nil {
		return c == nil
	}

	var e error = c
	for e != nil {
		if tmp, ok := e.(*Error); ok {
			if as, ok := err.(*Error); ok {
				if tmp.Value() == as.Value() {
					return true
				}
			}
			e = tmp.cause
		} else if tmp, ok := err.(error); ok {
			if e == err || e.Error() == tmp.Error() {
				return true
			}
			return false
		}
	}

	return false
}

// New 创建错误
func New(code int, msg string) *Error {
	return &Error{
		code: code,
		msg:  msg,
	}
}

// Wrap Wrap
func Wrap(err *Error, cause error) *ecode.Status {
	return ecode.Wrap(err, cause)
}

// WrapStr WrapStr
func WrapStr(err *Error, msg string) *ecode.Status {
	return ecode.Error(err, msg)
}

// Cause Cause
func Cause(err *Error) error {
	for err != nil {
		if err.cause == nil {
			break
		}

		if cause, ok := err.cause.(*Error); ok {
			err = cause
		} else {
			return err.cause
		}
	}
	return err
}
