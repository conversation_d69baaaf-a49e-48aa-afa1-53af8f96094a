package service

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"survey/model"
	"survey/proto"
	"survey/util/errors"
)

func SurveyShowService(ctx context.Context, surveyId int64, clientId string) (*proto.Survey, error) {
	gLog := xlog.FromContext(ctx)
	result, err := model.SurveyPreviewData(ctx, surveyId, clientId)
	if err != nil && err.Error() != "record not found" {
		gLog.Error("show survey with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	} else if err != nil && err.Error() == "record not found" {
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	survey := ModelTtoProto(ctx, result)
	if survey == nil {
		return nil, errors.Wrap(errors.ErrSystem, err)
	}
	// 问卷统计
	var questionStatisticsData proto.QuestionStatisticsData
	questionStatisticsData.ValidAnswerTotal, _ = model.GetFullValidSurveyRecordIdCount(ctx, result.ID)
	survey.QuestionStatisticsData = &questionStatisticsData
	return survey, nil
}
