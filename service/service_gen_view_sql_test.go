package service

import (
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/cond"
	"testing"
)

func TestGenSQLFromContent(t *testing.T) {
	contentStr := `{
  "operator": "&&",
  "conditions": [
  {
      "operator": "==",
      "lhs": "r.survey_record_id",
      "rhs": "1"
  },
  {
      "operator": ">",
      "lhs": "_field_duration_",
      "rhs": "1"
  },
  {
      "operator": "between",
      "lhs": "r.ctime",
      "rhs": [
		1724382960000,
		1729653360000
      ]
    },
  {
      "operator": "==",
      "lhs": "r.openid",
      "rhs": "'202669669'"
  },
  {
      "operator": "==",
      "lhs": "r.roleid",
      "rhs": "'9013276242'"
    },
    {
      "operator": "==",
      "lhs": "d.question",
      "rhs": "'OKyW8jT_yz'"
    },
 	{
      "operator": "in",
      "lhs": "d.option",
      "rhs": ["'OKyW8jT_yz'","'OKyW8jT_yz2'"]
    },
    {
      "operator": "wildcard",
      "lhs": "d.text",
      "rhs": "'*abc*'"
    }
  ]
}`
	//BI
	biSql, err := GetConditionViewerInstance().GenSQLFromContent(contentStr, Gen_Sql_Kind_BI)
	if err != nil {
		t.Fatalf("bi fail, err:%+v", err)
	}
	t.Log("biSql:", biSql)

	//mysql
	mySql, err := GetConditionViewerInstance().GenSQLFromContent(contentStr, Gen_Sql_Kind_Mysql)
	if err != nil {
		t.Fatalf("mySql fail, err:%+v", err)
	}
	t.Log("mySql:", mySql)

	//CK
	ckSql, err := GetConditionViewerInstance().GenSQLFromContent(contentStr, Gen_Sql_Kind_CK)
	if err != nil {
		t.Fatalf("ck fail, err:%+v", err)
	}
	t.Log("ckSql:", ckSql)
}

func TestGenSQLFromCond(t *testing.T) {
	contentStr := `{
  "operator": "&&",
  "conditions": [
    {
      "operator": "&&",
      "conditions": [
        {
          "operator": "&&",
          "conditions": [
            {
              "operator": "<=",
              "lhs": "r.survey_record_id",
              "rhs": 1
            },
            {
              "operator": "is_empty",
              "lhs": "r.roleid"
            }
          ]
        },
        {
          "operator": "not_empty",
          "lhs": "r.openid"
        }
      ]
    },
    {
      "rhs": 16,
      "operator": "!=",
      "lhs": "_field_duration_"
    }
  ]
}`
	var condition = &cond.Condition{}
	err := sonic.UnmarshalString(contentStr, condition)
	if err != nil {
		t.Fatalf("TestGenSQLFromCond err:%+v", err)
	}

	where, err := GetConditionViewerInstance().GenSQLFromCond(condition)
	if err != nil {
		t.Fatalf("GenSQLFromCond where fail, err:%+v", err)
	}
	t.Log("where:", where)
}
