package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"go.uber.org/zap"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"survey/config"
	"survey/model"
	"survey/proto"
	"survey/util/base"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func GetCityData(ctx context.Context) ([]Region, error) {
	gLog := xlog.FromContext(ctx)

	citysUrl := config.Get().CitysUrl

	if citysUrl == "" {
		citysUrl = "https://assets.papegames.com/resources/citys-cn.json"
	}
	response, err := http.Get(citysUrl)
	if err != nil {
		gLog.Error("HTTP request failed when get city data", xlog.Err(err))
		return nil, errors.New("HTTP request failed")
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		gLog.Error("HTTP request failed when get city data", xlog.Int("status_code", response.StatusCode))
		return nil, fmt.Errorf("获取城市数据失败: status code %d", response.StatusCode)
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		gLog.Error("Failed to read response body", xlog.Err(err))
		return nil, err
	}

	var regions []Region
	err = json.Unmarshal(data, &regions)
	if err != nil {
		gLog.Error("JSON unmarshalling failed", xlog.Err(err))
		return nil, err
	}
	return regions, nil
}

func GetSurveyInputMethodListService(ctx context.Context, request *proto.SurveyInputMethodListRequest) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)
	clientId := strconv.FormatInt(request.ClientId, 10)
	surveyId := strconv.FormatInt(request.SurveyId, 10)
	page := strconv.FormatInt(int64(request.Page), 10)
	pageSize := strconv.FormatInt(int64(request.PageSize), 10)

	type JSONData struct {
		Page          int `json:"page"`
		PageSize      int `json:"page_size"`
		SurveyID      int `json:"survey_id"`
		RequestConfig struct {
			QuestionUniqueKey string `json:"question_unique_key"`
			ConfigProps       struct {
				IsQuestion   bool   `json:"isQuestion"`
				IsAddress    bool   `json:"isAddress"`
				QuestionType string `json:"questionType"`
				SelectMode   string `json:"selectMode"`
				ValueType    string `json:"valueType"`
				UniqueKey    string `json:"uniqueKey"`
			} `json:"config_props"`
		} `json:"request_config"`
		ClientID int `json:"client_id"`
	}
	var postStr JSONData

	copier.Copy(&postStr, &request)
	// 要传递的参数
	postBody, _ := json.Marshal(postStr)
	// 将参数转换为字节序列
	requestBody := bytes.NewBuffer(postBody)

	// 发送POST请求
	baseUrl := config.Get().NodeCfg.Host + config.Get().NodeCfg.StatisticsInputMethodListUrl

	url := baseUrl + clientId + "/" + surveyId + "?" + "page=" + page + "&pageSize=" + pageSize + "&filterNullValue=" + xcast.ToString(request.FilterNullValue)

	gLog.Info("request url", zap.String("url", url))
	// 创建POST请求
	req, err := http.NewRequest("POST", url, requestBody)
	if err != nil {
		gLog.Error("create request error", xlog.Err(err))
		return nil, err
	}

	// 设置请求头信息，如内容类型为JSON
	req.Header.Set("Content-Type", "application/json")
	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		gLog.Error("send http client error", xlog.Err(err))
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		gLog.Error("ioutil ReadAll error", xlog.Err(err))
		return nil, err
	}

	gLog.Info("response body", zap.String("body", string(body)), zap.Int("status code", resp.StatusCode))
	response := map[string]interface{}{}

	err = json.Unmarshal(body, &response)
	if err != nil {
		gLog.Error("json Unmarshal error", xlog.Err(err))
		return nil, err
	}

	result, err := json.Marshal(response["data"])
	if err != nil {
		gLog.Error("json Marshal error", xlog.Err(err))
		return nil, err
	}

	// 输出data字段的值
	return xtype.NewRawMessage(result), nil
}

func GetSurveyInputMethodList(ctx context.Context, request *proto.SurveyInputMethodListRequest) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)

	// 分页默认值
	if request.Page <= 1 {
		request.Page = 1
	}

	if request.PageSize <= 1 {
		request.PageSize = 10
	}

	var (
		err error
		res = &proto.SurveyInputMethodListResponse{}
		//whereStr       string = fmt.Sprintf("r.is_valid = 0 AND r.is_delete = 0 AND d.question = '%s'", request.RequestConfig.QuestionUniqueKey)
		whereStr       string = fmt.Sprintf(" and question = '%s'", request.RequestConfig.QuestionUniqueKey)
		needFilterNull bool   = request.FilterNullValue == 1
	)

	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	invalidRecordIds, err := model.GetRecordFullInvalidRecordIds(ctx, hashCode)
	if err != nil {
		return nil, err
	}

	if len(invalidRecordIds) > 0 {
		var idSlice []string
		for _, id := range invalidRecordIds {
			idSlice = append(idSlice, cast.ToString(id))
		}
		whereStr += " and survey_record_id not in (" + strings.Join(idSlice, ",") + ")"
	}

	if request.RequestConfig.ConfigProps != nil && request.RequestConfig.ConfigProps.IsAddress {
		// 过滤非空
		if needFilterNull {
			whereStr += " AND (`option`!='' OR `option` != null) "
		}
		res, err = getAddressQuestionData(ctx, request, whereStr)
	} else {
		// 过滤非空
		if needFilterNull {
			whereStr += " AND (`text`!='' OR `text` != null) "
		}
		if request.RequestConfig.OptionValue != "" {
			whereStr += fmt.Sprintf(" AND `option`= '%s' ", request.RequestConfig.OptionValue)
		}
		res, err = getGeneralInputQuestionData(ctx, request, whereStr)
	}

	if err != nil {
		return nil, err
	}

	result, err := json.Marshal(res)
	if err != nil {
		gLog.Error("json Marshal error", xlog.Err(err))
		return nil, err
	}

	// 输出data字段的值
	return xtype.NewRawMessage(result), nil
}

func getAddressQuestionData(ctx context.Context, req *proto.SurveyInputMethodListRequest, whereStr string) (*proto.SurveyInputMethodListResponse, error) {
	var (
		res         = &proto.SurveyInputMethodListResponse{List: make([]*proto.InputMethodListResponseData, 0)}
		err         error
		hashCode, _ = base.SurveyIdToHashCode(req.SurveyId)
	)

	// 获取count
	// count, err := model.GetSurveyInputMethodCount(ctx, req.SurveyId, whereStr)
	count, err := model.GetSurveyInputMethodCountCK(ctx, hashCode, whereStr)
	if err != nil {
		err = fmt.Errorf("getAddressQuestionData model.GetSurveyRecordDetailCountByCond err: %+v", err)
		return nil, err
	}

	if count < 1 {
		return nil, nil
	}

	res.TotalCounts = int32(count)

	// 获取列表数据
	list, err := model.GetSurveyInputMethodListCk(ctx, hashCode, whereStr, req.Page, req.PageSize)
	if err != nil {
		err = fmt.Errorf("getAddressQuestionData model.GetSurveyInputMethodList err: %+v", err)
		return nil, err
	}

	// 数据处理
	cityData, err := GetCityData(ctx)
	for idx, item := range list {
		var (
			path = make([]string, 0)
			text string
		)
		path = treeFindByPath(cityData, func(data Region) bool {
			return data.Value == item.Option
		}, path)
		if len(path) > 0 {
			text = strings.Join(path, "-")
		}
		item.Text = text
		item.Id = int64(idx) + 1 + (int64(req.Page)-1)*int64(req.PageSize)
	}

	res.List = list
	res.CurrentPage = req.Page

	return res, err
}

func getGeneralInputQuestionData(ctx context.Context, req *proto.SurveyInputMethodListRequest, whereStr string) (*proto.SurveyInputMethodListResponse, error) {
	var (
		res         = &proto.SurveyInputMethodListResponse{List: make([]*proto.InputMethodListResponseData, 0)}
		err         error
		hashCode, _ = base.SurveyIdToHashCode(req.SurveyId)
	)

	//TODO: 原接口不支持filter，NodeJS支持

	// 获取count
	//count, err := model.GetSurveyInputMethodCount(ctx, req.SurveyId, whereStr)
	count, err := model.GetSurveyInputMethodCountCK(ctx, hashCode, whereStr)
	if err != nil {
		err = fmt.Errorf("getGeneralInputQuestionData model.GetSurveyRecordDetailCountByCond err: %+v", err)
		return nil, err
	}

	if count < 1 {
		return nil, nil
	}

	res.TotalCounts = int32(count)

	// 获取列表数据
	list, err := model.GetSurveyInputMethodListCk(ctx, hashCode, whereStr, req.Page, req.PageSize)
	if err != nil {
		err = fmt.Errorf("getGeneralInputQuestionData model.GetSurveyInputMethodList err: %+v", err)
		return nil, err
	}

	// 数据处理
	var listReal = make([]*proto.InputMethodListResponseData, 0)
	for idx, item := range list {
		// 过滤 - ID （过滤器不存在，不要）

		// 过滤 - 空
		if req.FilterNullValue == 1 {
			if item.Text == "" || strings.ToLower(item.Text) == "null" {
				continue
			}
		}

		item.Id = int64(idx) + 1 + (int64(req.Page)-1)*int64(req.PageSize)
		listReal = append(listReal, item)
	}

	res.List = listReal
	res.CurrentPage = req.Page

	return res, err
}
