package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"slices"
	"strconv"
	"survey/config"
	"survey/database"
	"survey/helpers"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util/base"
	"survey/util/errors"

	"github.com/bytedance/sonic"
	"github.com/spf13/cast"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"

	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
)

// 问卷预览
func SurveyPreview(ctx context.Context, surveyId int64, clientId string) (*proto.Survey, error) {
	gLog := xlog.FromContext(ctx)
	result, err := model.SurveyPreviewData(ctx, surveyId, clientId)
	if err != nil && err.Error() != "record not found" {
		gLog.Error("SurveyPreviewData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	} else if err != nil && err.Error() == "record not found" {
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	survey := ModelTtoProto(ctx, result)
	// 问卷统计
	var questionStatisticsData proto.QuestionStatisticsData
	questionStatisticsData.ValidAnswerTotal, _ = model.GetFullValidSurveyRecordIdCount(ctx, result.ID)
	if err != nil {
		gLog.Error("json.Unmarshal with error", xlog.Err(err))
		questionStatisticsData.ValidAnswerTotal = 0
	}
	survey.QuestionStatisticsData = &questionStatisticsData
	return survey, nil
}

func SurveyList(ctx context.Context, request *proto.SurveyListRequest) (*proto.SurveyListResponse, error) {
	var data proto.SurveyListResponse
	gLog := xlog.FromContext(ctx)

	// 为了向下兼容
	if request.Id > 0 {
		request.IdList = append(request.IdList, request.Id)
	}

	result, count, err := model.GetSurveyListData(ctx, strconv.FormatInt(request.ClientId, 10), request.Status, request.IdList, request.Name, request.SortCtime, request.PageSize, request.Page)
	if err != nil {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	var surveyList []*proto.Survey
	for _, r := range result {
		setting := &proto.Setting{}
		survey := &proto.Survey{}
		if r.Settings != "" {
			if err := json.Unmarshal([]byte(r.Settings), setting); err != nil {
				gLog.Error("json.Unmarshal with error", xlog.Err(err))
				//continue
			}
		}

		schema, err := base.UnCompress(r.Schema)
		if err != nil {
			gLog.Error("UnCompress with error", xlog.Err(err))
			schema = ""
		}
		previewSchema, err := base.UnCompress(r.PreviewSchema)
		if err != nil {
			gLog.Error("UnCompress with error", xlog.Err(err))
			previewSchema = ""
		}

		survey.Id = r.ID
		ClientId, err := strconv.ParseInt(r.ClientId, 10, 64)
		if err != nil {
			gLog.Error("SurveyListData with error", xlog.Err(err))
			return nil, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
		}
		survey.ClientId = ClientId
		survey.Name = r.Name
		survey.IsClosed = r.IsClosed
		survey.IsPause = r.IsPause
		survey.IsPublish = r.IsPublish
		survey.IsModifyUnpublish = r.IsModifyUnpublish
		survey.IsOpened = r.IsOpened
		if r.Stime == nil {
			survey.Stime = ""
		} else {
			survey.Stime = base.FormatTime(*r.Stime)
		}
		if r.Etime == nil {
			survey.Etime = ""
		} else {
			survey.Etime = base.FormatTime(*r.Etime)
		}
		survey.Type = r.Type
		survey.Schema = schema
		survey.PreviewSchema = previewSchema
		survey.Settings = r.Settings
		survey.WebSettings = r.WebSettings
		survey.Languages = r.Languages
		survey.HashCode = r.HashCode
		survey.IsDelete = r.IsDelete
		if r.Deltime.IsZero() {
			survey.Deltime = ""
		} else {
			survey.Deltime = base.FormatTime(r.Deltime)
		}
		survey.Remark = r.Remark
		survey.Ctime = base.FormatTime(r.Ctime)
		survey.Mtime = base.FormatTime(r.Mtime)
		survey.Creator = r.Creator
		survey.Editor = r.Editor
		if setting.BaseRuleConfig != nil && setting.BaseRuleConfig.TimeLimitConfig != nil && setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit {
			survey.IsTimeLimit = true
		} else {
			survey.IsTimeLimit = false
		}

		if setting.BaseRuleConfig != nil {
			survey.IsTimeLimit = setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit
		} else {
			xlog.Info("setting:", zap.Any("setting", setting), zap.Int64("id", r.ID))
		}
		survey.AllAnsweredUserCount, _ = model.GetFullValidSurveyRecordIdCount(ctx, r.ID)
		survey.Status = int32(model.GetSurveyStatus(r))

		// 投放链接
		deliverList := setting.BaseRuleConfig.DeliverList
		WebPathList := helpers.GenSurveyWebPath(r.HashCode, deliverList, r.ClientId)
		survey.WebPathList = WebPathList

		surveyList = append(surveyList, survey)
	}

	data.List = surveyList
	data.Total = count

	return &data, nil
}

func CreateSurvey(ctx context.Context, request *proto.CreateSurveyRequest) (int64, error) {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("GetUserAndNow with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrNotFound, err)
	}

	webSettings, err := ParseWebSettings(request.Settings)
	if err != nil {
		gLog.Error("ParseWebSettings with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
	}

	_, err = CheckPeriodicControl(ctx, request.Settings, "")
	if err != nil {
		return 0, err
	}

	id, err := model.CreateSurveyData(ctx, strconv.FormatInt(request.ClientId, 10), request.Name, webSettings, request.Settings, request.Stime, request.Etime, baseInfo.Username)
	if err != nil {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return id, nil
}

func StatisticsUpdate(ctx context.Context, request *proto.StatisticsUpdateRequest) error {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("GetUserAndNow with error", xlog.Err(err))
		return errors.Wrap(errors.ErrNotFound, err)
	}

	var webSettings string
	if request.Settings != "" {
		webSettings, err = ParseWebSettings(request.Settings)
		if err != nil {
			gLog.Error("ParseWebSettings with error", xlog.Err(err))
			return errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
		}
		// 频控检测
		hashCode, _ := base.SurveyIdToHashCode(request.Id)
		_, err := CheckPeriodicControl(ctx, request.Settings, hashCode)
		if err != nil {
			return err
		}
	}

	err = model.StatisticsUpdate(ctx, request.Id, strconv.FormatInt(request.ClientId, 10), request.Name, webSettings, request.Settings, request.Stime, request.Etime, baseInfo.Username)
	if err != nil && err.Error() != "record not found" {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	} else if err != nil && err.Error() == "record not found" {
		return errors.Wrap(errors.ErrNotFound, err)
	}
	return nil
}

func GetSurveyRecordDetail(ctx context.Context, surveyId int64, id int64) (*proto.Survey, *proto.SurveyRecord, string, error) {
	gLog := xlog.FromContext(ctx)
	survey, surveyRecord, recordDetail, err := model.SurveyRecordDetailData(ctx, surveyId, id)
	if err != nil {
		gLog.Error("SurveyRecordDetailData with error", zap.Error(err))
		return nil, nil, "", err
	}
	surveyConfig := ModelTtoProto(ctx, survey)

	userRecord := &proto.SurveyRecord{}
	userRecord.Id = surveyRecord.ID
	userRecord.Uid = surveyRecord.UID
	userRecord.RoleId = surveyRecord.RoleID
	userRecord.IsValid = int32(surveyRecord.IsValid)
	userRecord.IsDelete = int32(surveyRecord.IsDeleted)
	if surveyRecord.BeginTime.IsZero() {
		userRecord.BeginTime = ""
	} else {
		userRecord.BeginTime = base.FormatTime(surveyRecord.BeginTime)
	}
	if surveyRecord.EndTime.IsZero() {
		userRecord.EndTime = ""
	} else {
		userRecord.EndTime = base.FormatTime(surveyRecord.EndTime)
	}
	userRecord.Ctime = base.FormatTime(surveyRecord.Ctime)

	var tmpMap = make(map[string][]interface{})
	for _, r := range recordDetail {
		tmpMap[string(r.Question)] = append(tmpMap[string(r.Question)], helpers.OptionItem{
			Option: string(r.Option),
			Text:   string(r.Text),
		})
	}

	tempListStr, err := json.Marshal(tmpMap)
	if err != nil {
		gLog.Error("RecordListData with error", xlog.Err(err))
		return nil, nil, "", err
	}

	return surveyConfig, userRecord, string(tempListStr), nil
}

func DeleteSurvey(ctx context.Context, request *proto.SurveyDelRequest) error {
	db := database.Get()
	gLog := xlog.FromContext(ctx)
	for _, id := range request.DelList {
		info, err := model.GetSurveyById(ctx, id)
		if err != nil && err.Error() != "record not found" {
			gLog.Error("GetSurveybyId with error", xlog.Err(err))
			return err
		} else if err != nil && err.Error() == "record not found" {
			return errors.WrapStr(errors.ErrNotFound, "问卷不存在")
		}
		if info.IsOpened == 1 {
			return errors.WrapStr(errors.ErrSystem, "删除的问卷中有开启过答题的问卷不允许删除")
		}
	}

	err := model.DeleteSurveyData(ctx, db, request)
	if err != nil {
		xlog.Error("DeleteSurveyData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func CopySurvey(ctx context.Context, surveyId int64, clientId string) (int64, error) {
	id, err := model.CopySurveyData(ctx, surveyId, clientId)
	if err != nil && err.Error() == "record not found" {
		return 0, ecode.Error(ecode.NotFound, "问卷不存在")
	}
	return id, err
}

func SetStatusSurvey(ctx context.Context, ClientId string, surveyId int64, status int32) error {
	// todo 1. 该问卷处于答题中不能再次开启答题 2.该问卷处于暂停中不能再次关闭答题 3.该问卷的 schema / settings 字段内容为空，不能设置为开始答题
	gLog := xlog.FromContext(ctx)

	info, err := model.GetSurveyById(ctx, surveyId)
	if err != nil {
		gLog.Error("GetSurveyById with error", zap.Error(err))
		return ecode.ServerError
	}
	// 获取问卷状态
	oldStatus := model.GetSurveyStatus(info)
	if oldStatus == 1 && status == 1 {
		return ecode.Error(ecode.BadRequest, "该问卷处于答题中不能再次开启答题")
	}
	if oldStatus == 0 && status == 0 {
		return ecode.Error(ecode.BadRequest, "该问卷处于暂停中不能再次关闭答题")
	}

	if status == 1 && (info.Settings == "" || info.Schema == nil) {
		return ecode.Error(ecode.BadRequest, "该问卷的 schema / settings 字段内容为空，不能设置为开始答题")
	}
	err = model.SetStatusSurveyData(ctx, ClientId, surveyId, status)
	if err != nil {
		gLog.Error("SetStatusSurveyData with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// 获取更新后问卷数据
	survey, err := model.GetSurveyById(ctx, surveyId)
	// 创建问卷版本记录数据
	err = model.CreateFromCmsSurvey(ctx, survey)
	if err != nil {
		gLog.Error("SurveyPublishData with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	err = NoticeToCReloadConfig(survey.HashCode, "")
	if err != nil {
		return err
	}

	return nil
}

func SurveyPublish(ctx context.Context, surveyId int64, clientId string) error {
	gLog := xlog.FromContext(ctx)
	survey, err := model.GetSurveyById(ctx, surveyId)
	if err != nil {
		gLog.Error("GetSurveybyId with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// todo 检测能否发布
	if survey.Schema == nil && survey.PreviewSchema == nil {
		gLog.Error("SurveyPublishData Schema and PreviewSchema empty with error", xlog.Err(err))
		return errors.Wrap(errors.ErrSchemaOrPreviewSchemaEmpty, err)
	}
	// 获取答题记录
	//count, err := model.GetFullValidSurveyRecordIdCount(ctx, surveyId)
	//if err != nil && count > 0 {
	//
	//}
	// 发布问卷
	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		xlog.Error("GetUserAndNow failed", xlog.Err(err))
		return err
	}
	hashCode, err := base.SurveyIdToHashCode(surveyId)
	if err != nil {
		gLog.Error("SurveyIdToHashCode with error", zap.Error(err))
		return errors.Wrap(errors.ErrSystem, err)
	}
	data := make(map[string]interface{})
	data["is_publish"] = 1
	data["is_modify_unpublish"] = 0
	data["editor"] = baseInfo.Username
	data["hash_code"] = hashCode

	if err := model.UpdatesSurveyByID(ctx, surveyId, data); err != nil {
		gLog.Error("UpdatesSurveyByID with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	survey.IsPublish = 1
	survey.IsModifyUnpublish = 0
	survey.Editor = baseInfo.Username
	survey.HashCode = hashCode

	err = model.CreateFromCmsSurvey(ctx, survey)
	//todo SurveyPublishData 这个函数的功能拆分成 mvc 了,之后 check 一下
	if err != nil {
		gLog.Error("SurveyPublishData with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	//// 请求api
	//err = CallPeginServer(clientId, []int{int(surveyId)})

	err = NoticeToCReloadConfig(hashCode, "")
	if err != nil {
		return err
	}

	return nil
}

// 通知C端配置更新
func NoticeToCReloadConfig(surveyId, group string) error {
	//没配置不通知 - dev 集群问题
	if config.Get().SurveyFrontHost == "" {
		return nil
	}
	url := fmt.Sprintf(config.Get().SurveyFrontHost+"/v1/survey/internal/config/reload?survey_id=%s&group=%s", surveyId, group)
	var result = new(types.Response[any])
	response, err := xresty.New().R().SetResult(result).Post(url)
	if err != nil {
		return err
	}

	if response.StatusCode() != 200 || result.Code != 0 {
		err = fmt.Errorf("NoticeToCReloadConfig Request fail, statusCode:%d result:%+v", response.StatusCode(), result)
		return err
	}

	return nil
}

func CallPeginServer(clientID string, ids []int) error {
	urls := GetReloadServerURL(clientID)
	fmt.Println(urls, "urls")

	if len(urls) == 0 {
		return fmt.Errorf("no pegin URLs found for client %s", clientID)
	}

	parameters := []struct {
		id  int
		url string
	}{}
	for _, url := range urls {
		for _, id := range ids {
			parameters = append(parameters, struct {
				id  int
				url string
			}{id, url})
		}
	}

	// Call pegin for each parameter
	for _, p := range parameters {
		err := CallPegin(p.id, p.url)
		if err != nil {
			return err
		}
	}

	return nil
}

func CallPegin(id int, apiUrl string) error {
	baseURL := apiUrl + "survey/internal/config/reload?survey_id="
	//hashCode := strconv.Itoa(surveyIDToHashCode(id))
	hashCode, err := base.SurveyIdToHashCode(int64(id))
	if err != nil {
		return err
	}
	url := baseURL + hashCode

	fmt.Println("callPegin URL:", url)

	// Create HTTP client
	client := http.Client{
		Timeout: 6000,
	}

	// Make HTTP POST request
	resp, err := client.Post(url, "application/json", bytes.NewBuffer([]byte{}))
	if err != nil {
		return fmt.Errorf("error making HTTP request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status code
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	// Read response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %v", err)
	}

	// Check response data code
	if len(body) > 0 {
		return fmt.Errorf("response error: %s", string(body))
	}
	fmt.Println("同步数据ok")
	return nil
}

func GetReloadServerURL(clientID string) []string {
	reloadServerUrl := base.ReloadServerURL

	// 获取数据中心名称
	dataCenterName := GetClientDataCenterName(clientID)

	// 检查是否存在特定数据中心的重载服务器URL
	if urls, ok := reloadServerUrl[dataCenterName]; ok {
		return urls
	}

	// 如果未找到特定数据中心的URL，则返回默认URL
	if urls, ok := reloadServerUrl["default"]; ok {
		return urls
	}

	// 如果都未找到，则返回原始reloadServerUrl
	return reloadServerUrl["default"]
}

func GetClientDataCenterName(clientID string) string {
	clientDataCenterMap := base.ClientDataCenterMap

	// 获取客户端数据中心映射
	for dataCenter, clientIDs := range clientDataCenterMap {
		for _, id := range clientIDs {
			if strconv.Itoa(id) == clientID {
				return dataCenter
			}
		}
	}
	// 默认返回'hzone'
	return "hzone"
}

func UpdateSurvey(ctx context.Context, request *proto.UpdateSurveyRequest) error {
	gLog := xlog.FromContext(ctx)

	schemeByte, err := base.Compress(request.Schema)
	if err != nil {
		gLog.Error("Compress with error", zap.Error(err))
		return errors.Wrap(errors.ErrSystem, err)
	}

	err = model.UpdatesSurveyDataById(ctx, request.Id, request.Name, schemeByte, request.Languages, request.WebSettings, request.KeyValue, request.Font)
	if err != nil {
		gLog.Error("UpdateSurveyData with error", zap.Error(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return err
}

func ParseWebSettings(set string) (string, error) {
	var settings proto.Settings
	if err := sonic.UnmarshalString(set, &settings); err != nil {
		return "", err

	}
	webSettings := proto.WebSettings{
		LoginType:       settings.BaseRuleConfig.LoginType,
		IsEndPreview:    settings.BaseRuleConfig.IsEndPreview,
		IsGoOnAnswer:    settings.BaseRuleConfig.IsGoOnAnswer,
		LanguageList:    settings.BaseRuleConfig.LanguageList,
		MaterialsConfig: settings.MaterialsConfig,
	}

	return sonic.MarshalString(&webSettings)
}

func SurveyOverwriteSend(ctx context.Context, clientID int64, surveyId int64) (*xtype.Empty, error) {
	//gLog := xlog.FromContext(ctx)

	// 不合法的同步租户ID
	if !slices.Contains(config.Get().Overwrite.ClientId, clientID) {
		return nil, errors.ErrOverwriteClientIDInvalid
	}

	// 获取问卷
	survey, err := model.SurveyPreviewData(ctx, surveyId, xcast.ToString(clientID))
	if err != nil {
		return nil, err
	}

	//// 未发布问卷，不能同步回归服
	//if survey.IsPublish == 0 {
	//	return nil, errors.ErrSurveyNotPublish
	//}

	if survey.ID <= 0 {
		return nil, errors.ErrOverwriteSurveyIDInvalid
	}

	//test: 模拟跨环境情况
	// survey.ID = 1347

	bs, err := sonic.Marshal(survey)
	if err != nil {
		return nil, err
	}

	//// 带上cookie
	//token, err := xgin.FromContext(ctx).Request.Cookie(config.Get().GosCfg.CookieName)
	//if err != nil {
	//	return nil, err
	//}
	//
	//cookie := &http.Cookie{
	//	Name:  config.Get().GosCfg.CookieName,
	//	Value: token.Value,
	//}

	//发送给接受同步方
	var res = &types.Response[any]{}
	response, err := xresty.New().R().SetBody(&proto.SurveyOverwriteSyncReq{
		ClientId: clientID,
		Survey:   string(bs),
	}).SetResult(res).Post(config.Get().Overwrite.SyncUrl)

	if err != nil {
		return nil, err
	}

	if response.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("SurveyOverwriteSync err:%d info:%s request-id:%s response.StatusCode:%d", res.Code, res.Info, res.RequestId, response.StatusCode())
	}

	if res.Code != 0 {
		return nil, fmt.Errorf("SurveyOverwriteSync err:%d info:%s request-id:%s", res.Code, res.Info, res.RequestId)
	}

	return nil, nil
}

func SurveyOverwriteSync(ctx context.Context, clientID int64, surveyStr string) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)

	// 不合法的同步租户ID
	if !slices.Contains(config.Get().Overwrite.ClientId, clientID) {
		return nil, errors.ErrOverwriteClientIDInvalid
	}

	var survey = new(model.CmsSurvey)
	err := sonic.UnmarshalString(surveyStr, survey)
	if err != nil {
		return nil, err
	}

	// 获取问卷
	surveyExist, err := model.GetSurveyByID(ctx, survey.ID)
	if err != nil && !xgorm.RecordNotFound(err) {
		return nil, err
	}

	// 模拟租户不同
	// surveyExist.ClientId = "1106"

	// 问卷重复
	if survey.ID > 0 && surveyExist != nil && surveyExist.ID == survey.ID {
		gLog.Info("SurveyOverwriteSync overwrite repeat", xlog.Int64("surveyId", survey.ID))

		// 租户不同，将冲突的问卷，新增方式重新添加
		if surveyExist.ClientId != cast.ToString(survey.ClientId) {
			//插入字段，参考 model.CopySurveyData
			surveyInsert := &model.CmsSurvey{
				ClientId:      surveyExist.ClientId,
				Name:          surveyExist.Name,
				Stime:         surveyExist.Stime,
				Etime:         surveyExist.Etime,
				Type:          surveyExist.Type,
				Schema:        surveyExist.Schema,
				PreviewSchema: surveyExist.PreviewSchema,
				Settings:      surveyExist.Settings,
				WebSettings:   surveyExist.WebSettings,
				Languages:     surveyExist.Languages,
				KeyValue:      surveyExist.KeyValue,
				Font:          surveyExist.Font,
				Remark:        surveyExist.Remark,
				Ctime:         surveyExist.Ctime,
				Creator:       surveyExist.Creator,
				ApiVersion:    surveyExist.ApiVersion,
			}
			_, err = model.SurveySave(ctx, surveyInsert)
			if err != nil {
				return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
			}
			gLog.Info("SurveyOverwriteSync client id conflict model.SurveySave",
				xlog.Int64("rawSurveyID", survey.ID),
				xlog.Int64("insertId", surveyInsert.ID),
			)
		}

		//return nil, errors.ErrSurveyIDRepeat
	}

	// 重置问卷奖励
	var settings = new(types.SurveySetting)
	err = sonic.UnmarshalString(survey.Settings, settings)
	if err != nil {
		return nil, err
	}

	settings.GiftConfig.IsGiveOutByCms = false
	settings.GiftConfig.GiveOutType = "0"
	settings.GiftConfig.PushAwardConfig = nil

	bs, err := sonic.Marshal(settings)
	if err != nil {
		return nil, err
	}
	survey.Settings = string(bs)

	var isPause = survey.IsPause

	id, err := model.SurveySave(ctx, survey)
	if err != nil {
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 解决gorm default 标签默认值问题
	survey.IsPause = isPause
	err = model.SurveyPauseDefault(ctx, survey.ID, isPause)
	if err != nil {
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 创建version - 无需考虑问卷发布否
	err = model.CreateFromCmsSurvey(ctx, survey)
	if err != nil {
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 请求api - 通知C端问卷变更
	//err = CallPeginServer(cast.ToString(clientID), []int{int(survey.ID)})

	gLog.Info("SurveyOverwriteSync overwrite", xlog.Int64("surveyId", id))
	return nil, nil
}

func CheckPeriodicControl(ctx context.Context, set string, hashCode string) (bool, error) {
	var settings proto.Settings
	if err := sonic.UnmarshalString(set, &settings); err != nil {
		return false, err
	}

	p := settings.GetBaseRuleConfig().GetPeriodicControl()

	if p.GetEnable() {
		_, ok := proto.Settings_PeriodicControl_Unit_name[int32(p.GetUnit())]
		if !ok {
			return false, errors.ErrSurveySettingPeriodicErr
		}
		if p.GetInterval() < 1 {
			return false, errors.ErrSurveySettingPeriodicErr
		}
		if settings.GetBaseRuleConfig().GetAnswerTimesConfig().GetTimes() < 1 {
			return false, errors.ErrSurveySettingPeriodicErr
		}

		// 已作答的问卷不能修改频控参数
		if hashCode != "" {
			count, err := model.GetRecordCountNew(ctx, hashCode, 0, nil, nil, "")
			if err != nil {
				return false, err
			}
			if count > 0 {
				return false, errors.ErrSurveySettingPeriodicUpdateErr
			}
		}
	}

	return true, nil
}
