package service

import (
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/cond"
	"survey/types"
	"testing"
)

func TestQuestionFilter(t *testing.T) {
	var questionJson = `
{
  "operator": "&&",
  "conditions": [
    {
      "lhs": "question_a",
      "rhs": "'OKyW8jT_yz'",
      "operator": "=="
    },
    {
      "operator": "||",
      "conditions": [
        {
          "lhs": "question_b",
          "rhs": [
            "'OKyW8jT_yz'",
            "'OKyW8jT_yz2'"
          ],
          "operator": "in"
        },
        {
          "lhs": "question_c",
          "operator": "not_empty"
        }
      ]
    }
  ]
}
`
	var userRecordDetail = &types.ExportFormatRecordData{
		UserRecord: nil,
		AnswerTime: "",
		RecordDetail: map[string][]types.ExportDetailItem{
			"question_a": []types.ExportDetailItem{
				{
					Option: "OKyW8jT_yz",
				},
			},
			"question_b": []types.ExportDetailItem{
				{
					Option: "OKyW8jT_yz2",
				},
			},
		},
	}

	var questionCond = new(cond.Condition)
	err := sonic.Unmarshal([]byte(questionJson), questionCond)
	if err != nil {
		t.Logf("unmarshal err %v", err)
		return
	}

	if err := checkFilter(questionCond); err != nil {
		t.Logf("checkFilter err %v", err)
		return
	}

	filter, err := questionFilter(questionCond, userRecordDetail)
	if err != nil {
		t.Logf("filter err %v", err)
		return
	}
	t.Logf("filter:%v", filter)
}
