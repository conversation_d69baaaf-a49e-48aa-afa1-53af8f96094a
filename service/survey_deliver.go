package service

import (
	"survey/constants"
	"survey/proto"
	"survey/types"
)

func GetDeliverList(clientID string) []types.Deliver {
	if clientID == "" {
		return constants.DefaultDeliverList
	}
	for _, item := range constants.DeliverConfigList {
		for _, client := range item.Clients {
			if client == clientID {
				return item.DeliverList
			}
		}
	}

	return constants.DefaultDeliverList
}

func ConvertToProtoDeliver(delivers []types.Deliver) []*proto.Deliver {
	var protoDelivers []*proto.Deliver
	for _, d := range delivers {
		protoDelivers = append(protoDelivers, &proto.Deliver{
			DefaultLanguage:     d.<PERSON>anguage,
			Region:              d.Region,
			OfficialWebsiteHost: d.OfficialWebsiteHost,
			SurveyHost:          d.SurveyHost,
		})
	}
	return protoDelivers
}
