package service

import (
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/cond"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"strings"
	"survey/util"
)

type (
	SqlKind              int32
	ViewFieldHandle      func(condObj *cond.Condition)
	ViewSqlReplaceHandle func(sqlStr string) string
)

const (
	Gen_Sql_Kind_BI    SqlKind = 1 // BI
	Gen_Sql_Kind_CK    SqlKind = 2 // Clickhouse
	Gen_Sql_Kind_Mysql SqlKind = 3 // mysql
)

type ConditionViewer struct {
	beforeHandles map[SqlKind]map[string]ViewFieldHandle // 前置处理
	afterHandle   ViewSqlReplaceHandle                   // 后置处理
}

func NewConditionViewer() *ConditionViewer {
	return &ConditionViewer{
		beforeHandles: make(map[SqlKind]map[string]ViewFieldHandle),
		afterHandle: func(sqlStr string) string {
			return sqlStr
		},
	}
}

var svcViewer *ConditionViewer

func GetConditionViewerInstance() *ConditionViewer {
	// 非协程安全 sync.Do
	if svcViewer != nil {
		return svcViewer
	}

	svcViewer = NewConditionViewer()

	// 设置前置处理方法
	var beforeHandles = map[SqlKind]map[string]ViewFieldHandle{
		Gen_Sql_Kind_BI: {
			"r.survey_record_id": func(condObj *cond.Condition) {
				condObj.Lhs = "r.survey_record_id"
			},
			"r.openid": func(condObj *cond.Condition) {
				condObj.Lhs = "r.vopenid"
			},
			"r.roleid": func(condObj *cond.Condition) {
				condObj.Lhs = "r.vroleid"
			},
			"r.ctime": svcViewer.CtimeHandle,
		},
		Gen_Sql_Kind_CK: {
			"r.ctime": svcViewer.CtimeHandle,
		},
		Gen_Sql_Kind_Mysql: {
			"r.survey_record_id": func(condObj *cond.Condition) {
				condObj.Lhs = "r.id"
			},
			"r.roleid": func(condObj *cond.Condition) {
				condObj.Lhs = "r.role_id"
			},
			"r.ctime": svcViewer.CtimeHandle,
		},
	}
	svcViewer.SetBeforeHandles(beforeHandles)

	// 设置后置处理方法
	svcViewer.SetAfterHandle(func(sqlStr string) string {
		return strings.ReplaceAll(sqlStr, "`_field_duration_`", "timestampdiff(second,r.begin_time,r.end_time)")
	})

	return svcViewer
}

func (_this *ConditionViewer) SetBeforeHandles(beforeHandles map[SqlKind]map[string]ViewFieldHandle) {
	_this.beforeHandles = beforeHandles
}

func (_this *ConditionViewer) SetAfterHandle(afterHandle ViewSqlReplaceHandle) {
	_this.afterHandle = afterHandle
}

func (_this *ConditionViewer) GenSQLFromContent(content string, kind SqlKind) (string, error) {
	var condition = &cond.Condition{}
	err := sonic.UnmarshalString(content, condition)
	if err != nil {
		return "", err
	}

	// 根据不同数据源，替换字段名
	switch kind {
	case Gen_Sql_Kind_BI: // BI
		_this.genSQLFromContentReplaceField(kind, condition)
	case Gen_Sql_Kind_CK: // CK
		_this.genSQLFromContentReplaceField(kind, condition)
	case Gen_Sql_Kind_Mysql: // mysql
		_this.genSQLFromContentReplaceField(kind, condition)
	default:
		err = fmt.Errorf("ConditionViewer GenSQLFromContent not support kind:%d", kind)
		return "", err
	}

	sqlStr, err := _this.GenSQLFromCond(condition)
	if err != nil {
		return "", err
	}

	// 后置处理
	sqlStr = _this.afterHandle(sqlStr)

	// cond包bug, 如果是OR会让外层条件失效
	if len(sqlStr) > 0 {
		sqlStr = fmt.Sprintf("(%s)", sqlStr)
	}

	return sqlStr, nil
}

func (_this *ConditionViewer) genSQLFromContentReplaceField(kind SqlKind, condObj *cond.Condition) {

	// 字段特殊处理
	existsMap := _this.beforeHandles[kind]
	if existsMap != nil {
		handle, ok := existsMap[condObj.Lhs]
		if ok {
			handle(condObj)
		}
	}

	for _, c := range condObj.Conditions {
		_this.genSQLFromContentReplaceField(kind, c)
	}
}

func (_this *ConditionViewer) GenSQLFromCond(condObj *cond.Condition) (string, error) {
	if condObj == nil {
		err := fmt.Errorf("GenSQLFromCond condObj is empty")
		return "", err
	}

	expr, err := cond.NewCELBuilder(condObj).Build()
	if err != nil {
		err = fmt.Errorf("ConditionViewer GenSQLFromContent cond.NewCELBuilder(&c).Build() err:%+v", err)
		return "", err
	}

	//CEL 转 SQL
	condition, err := cond.Parse(expr)
	if err != nil {
		err = fmt.Errorf("ConditionViewer GenSQLFromContent cond.Parse err:%+v cel:%+v", err, expr)
		return "", err
	}

	sqlStr, err := cond.NewSQLBuilder(condition).Build()
	if err != nil {
		err = fmt.Errorf("ConditionViewer GenSQLFromContent cond.Parse err:%+v cel:%+v", err, expr)
		return "", err
	}

	return sqlStr, nil
}

func (_this *ConditionViewer) CtimeHandle(condObj *cond.Condition) {
	if condObj.Rhs == nil {
		xlog.Error("viewFieldMaps r.ctime rhs is nil", xlog.Any("condition", condObj))
		return
	}

	// rhs 是数组， 或者值
	var newValue interface{}
	if arr, ok := condObj.Rhs.Value.([]interface{}); ok {
		var newValueArr []interface{}
		for _, val := range arr {
			newValueArr = append(newValueArr, fmt.Sprintf("'%s'", util.MillSecondToFormat(xcast.ToInt64(val))))
		}
		newValue = newValueArr
	} else {
		newValue = fmt.Sprintf("'%s'", util.MillSecondToFormat(xcast.ToInt64(condObj.Rhs.Value)))
	}

	condObj.Rhs = &xtype.Any{
		Value: newValue,
	}
}
