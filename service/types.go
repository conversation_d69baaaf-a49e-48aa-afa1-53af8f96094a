package service

type SurveyInputMethodListItem struct {
	DeviceId       string `json:"device_id,omitempty"`        // 设备ID
	Id             int64  `json:"id,omitempty"`               // 记录ID
	Ip             string `json:"ip,omitempty"`               // IP地址
	Openid         string `json:"openid,omitempty"`           // 用户openid
	Option         string `json:"option,omitempty"`           // 题目选项
	Question       string `json:"question,omitempty"`         // 题目ID
	RoleId         string `json:"role_id,omitempty"`          // 用户roleid
	SurveyRecordId int64  `json:"survey_record_id,omitempty"` // 问卷记录ID
	Text           string `json:"text,omitempty"`
}

type Region struct {
	Value    string   `json:"value"`
	Label    string   `json:"label"`
	Children []Region `json:"children,omitempty"` // 某些叶子节点可能没有children
}
