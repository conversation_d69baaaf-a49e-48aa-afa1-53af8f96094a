package service

import (
	"encoding/json"
	"fmt"
	"survey/config"
	"survey/proto"

	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func biQuery(requestId, sql string, limit int32, username string) (*BIResponse, error) {
	req := BIQuery{
		Data: Data{
			SQL:   sql,
			Limit: limit,
		},
		User:      username, //config.Get().BI.User,
		ClientID:  config.Get().BI.ClientId,
		BiSource:  config.Get().BI.BiSource,
		RequestID: requestId,
	}

	resp := new(BIResponse)
	r, err := BIClient.R().SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetBody(req.ToJson()).
		SetResult(resp).
		Post(config.Get().BI.Url)
	if err != nil {
		xlog.Error("bi query error",
			xlog.String("url", config.Get().BI.Url),
			xlog.String("requestId", requestId),
			xlog.String("sql", sql),
			xlog.String("resp", string(r.Body())))
		return nil, err
	}
	if resp.Status != "success" {
		xlog.Error("bi query error",
			xlog.String("url", config.Get().BI.Url),
			xlog.String("requestId", requestId),
			xlog.String("sql", sql),
			xlog.Any("resp", resp))
		return nil, ecode.Internal
	}

	// 查询错误
	if resp.Result.QueryError.Message != "" {
		err = fmt.Errorf("biQuery resp.Result.QueryError.Message not empty, errMsg:%s", resp.Result.QueryError.Message)
		return nil, err
	}

	return resp, nil
}

type BIQuery struct {
	Data      Data   `json:"data"`
	User      string `json:"user"`
	ClientID  string `json:"clientId"`
	BiSource  string `json:"biSource"`
	RequestID string `json:"requestId"`
}

type Data struct {
	SQL   string `json:"sql"`
	Limit int32  `json:"limit,omitempty"`
}

func (b *BIQuery) ToJson() []byte {
	bb, _ := json.Marshal(b)
	return bb
}

type BIResponse struct {
	RequestID string      `json:"requestId"`
	Type      interface{} `json:"type"`
	Result    Result      `json:"result"`
	Status    string      `json:"status"`
	ErrorMsg  interface{} `json:"errorMsg"`
}

type Result struct {
	// ID interface{} `json:"id"`
	Columns []*proto.Cloumn `json:"columns"`
	Data    [][]string      `json:"data"`
	// ElapsedTimeMillis int `json:"elapsedTimeMillis"`
	// ProgressPercentage float64 `json:"progressPercentage"`
	QueryError BIQueryError `json:"queryError"`
	State      string       `json:"state"` // FINISHED FAILED
	// ResultPath interface{} `json:"resultPath"`
	// QueryStartTime interface{} `json:"queryStartTime"`
	// QueryEndTime interface{} `json:"queryEndTime"`
	// ProcessedRows int `json:"processedRows"`
	// Extra interface{} `json:"extra"`
	ParsedSQL string `json:"parsedSql"`
	// ExecuteSQL string `json:"executeSql"`
}

type BIQueryError struct {
	Message     string      `json:"message"`
	ErrorName   string      `json:"errorName"`
	ErrorType   interface{} `json:"errorType"`
	FailureInfo struct {
		Type       string      `json:"type"`
		Message    string      `json:"message"`
		Suppressed interface{} `json:"suppressed"`
		Stack      []string    `json:"stack"`
	} `json:"failureInfo"`
}

type CrossAnalysisResponse struct {
	Date    [][]string
	Columns []*proto.Cloumn `json:"columns"`
}
