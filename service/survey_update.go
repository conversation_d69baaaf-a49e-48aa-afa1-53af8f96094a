package service

//
//func CheckCanUpdate(ctx context.Context, clientID string, id int64) error {
//	logger := xlog.FromContext(ctx)
//	if detailRet.Schema == nil && detailRet.PreviewSchema == nil {
//		return nil // Nothing to update if both schemas are nil.
//	}
//
//	recordList, err := model.GetFullValidSurveyRecordID(ctx, id)
//	if err != nil {
//		logger.Error("Failed to fetch full valid survey record IDs", "error", err)
//		return err
//	}
//
//	if len(recordList) > 0 {
//		oldSurvey, err := ShowSurvey(ctx, clientID, id)
//		if err != nil {
//			logger.Error("Failed to fetch survey details", "error", err)
//			return err
//		}
//
//		oldTree := oldSurvey.Schema.ComponentsTree
//		var newTree []Component // Define Component based on your proto definitions.
//		if detailRet.Schema != nil {
//			newTree = detailRet.Schema.ComponentsTree
//		} else if detailRet.PreviewSchema != nil {
//			newTree = detailRet.PreviewSchema.ComponentsTree
//		}
//
//		diffCompInfo, err := DiffComponentsTreeChanged(oldTree, newTree)
//		if err != nil {
//			logger.Error("Failed to diff components tree", "error", err)
//			return err
//		}
//
//		if diffCompInfo != nil {
//			return fmt.Errorf("component modifications are not allowed: %v", diffCompInfo)
//		}
//	}
//	return nil
//}
