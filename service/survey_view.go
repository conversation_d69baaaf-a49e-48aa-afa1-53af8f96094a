package service

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"strings"
	"survey/constants"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util/base"
	"survey/util/errors"
)

func CreateSurveyView(ctx context.Context, request *proto.SurveyViewCreateReq) (int64, error) {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("CreateSurveyView GetUserAndNow with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrNotFound, err)
	}

	// 基本参数判断 - surveyID
	if request.SurveyId < 1 {
		err = fmt.Errorf("CreateSurveyView Param err, SurveyId is invalid")
		return 0, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - name
	request.Name = strings.TrimSpace(request.Name)
	if request.Name == "" {
		err = fmt.Errorf("CreateSurveyView Param err, Name is empty")
		return 0, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - content
	if request.Content == "" {
		err = fmt.Errorf("CreateSurveyView Param err, content is empty")
		return 0, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - kind
	if !checkViewKindValid(request.Kind) {
		err = fmt.Errorf("CreateSurveyView Param err, kind is invalid, kind:%+v", request.Kind)
		return 0, errors.Wrap(errors.ErrParamCheckFailed, err)
	}

	// 校验格式合法性
	viewContent, err := checkContentFormat(request.Content)
	if err != nil {
		return 0, err
	}

	// 回收列表过滤器，必须有内容
	if request.Kind == constants.Survey_View_Kind_List {
		if viewContent == nil || viewContent.ContentView == nil {
			err = fmt.Errorf("CreateSurveyView Param err, kind is view list, ContentView array not empty kind:%+v contentStruct:%+v", request.Kind, viewContent)
			return 0, errors.Wrap(errors.ErrParamCheckFailed, err)
		}
	}

	// 同一个surveyID内 筛选器名称唯一
	surveyView, err := model.GetSurveyViewInfoBySurveyIdAndName(request.SurveyId, request.Name, 0)
	if err != nil && !xgorm.RecordNotFound(err) {
		return 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if surveyView != nil {
		return 0, errors.ErrSurveyViewNameRepeat
	}

	id, err := model.CreateSurveyView(request.SurveyId, request.Name, request.Content, request.Kind, baseInfo.Username)
	if err != nil {
		gLog.Error("CreateSurveyView model.CreateSurveyView with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return id, nil
}

func GetSurveyViewList(ctx context.Context, request *proto.SurveyViewListReq) ([]*proto.SurveyView, error) {
	gLog := xlog.FromContext(ctx)

	var list []*proto.SurveyView

	// 基本参数判断 - surveyID
	if request.SurveyId < 1 {
		err := fmt.Errorf("GetSurveyViewList Param err, SurveyId is invalid")
		return list, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	list, err := model.GetSurveyViewList(request, constants.Survey_View_Not_Delete)
	if err != nil {
		gLog.Error("GetSurveyViewList model.GetSurveyViewList with error", xlog.Err(err))
		return list, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return list, nil
}

func checkViewKindValid(kind int32) bool {
	existMap := map[int32]bool{
		constants.Survey_View_Kind_List:     true,
		constants.Survey_View_Kind_Analysis: true,
	}
	return existMap[kind]
}

func UpdateSurveyView(ctx context.Context, request *proto.SurveyView) error {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("UpdateSurveyView GetUserAndNow with error", xlog.Err(err))
		return errors.Wrap(errors.ErrNotFound, err)
	}

	// 基本参数判断 - id
	if request.Id < 1 {
		err = fmt.Errorf("UpdateSurveyView Param err, id is invalid")
		return errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - name
	request.Name = strings.TrimSpace(request.Name)
	if request.Name == "" {
		err = fmt.Errorf("UpdateSurveyView Param err, Name is empty")
		return errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - content
	if request.Content == "" {
		err = fmt.Errorf("UpdateSurveyView Param err, content is empty")
		return errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 校验格式合法性
	viewContent, err := checkContentFormat(request.Content)
	if err != nil {
		return err
	}

	// 回收列表过滤器，必须有内容
	if request.Kind == constants.Survey_View_Kind_List {
		if viewContent == nil || viewContent.ContentView == nil {
			err = fmt.Errorf("UpdateSurveyView Param err, kind is view list, ContentView array not empty kind:%+v contentStruct:%+v", request.Kind, viewContent)
			return errors.Wrap(errors.ErrParamCheckFailed, err)
		}
	}

	viewInfo, err := model.GetSurveyViewInfoByID(request.Id)
	if err != nil && !xgorm.RecordNotFound(err) {
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if viewInfo == nil {
		err = fmt.Errorf("UpdateSurveyView not found survey view, id:%v", viewInfo.Id)
		return err
	}

	// 同一个surveyID内 筛选器名称唯一
	surveyView, err := model.GetSurveyViewInfoBySurveyIdAndName(viewInfo.SurveyId, request.Name, request.Id)
	if err != nil && !xgorm.RecordNotFound(err) {
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if surveyView != nil {
		return errors.ErrSurveyViewNameRepeat
	}

	_, err = model.UpdateSurveyView(request.Id, request.Name, request.Content, baseInfo.Username)
	if err != nil {
		gLog.Error("UpdateSurveyView model.UpdateSurveyView with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func checkContentFormat(content string) (*types.ViewContent, error) {
	if !sonic.ValidString(content) {
		return nil, fmt.Errorf("checkContentFormat fail content is invalid json format")
	}

	var viewContent = &types.ViewContent{}
	err := sonic.UnmarshalString(content, viewContent)
	if err != nil {
		err = fmt.Errorf("checkContentFormat fail sonic.UnmarshalString err:%+v", err)
		return nil, err
	}

	return viewContent, nil
}

func DeleteSurveyView(ctx context.Context, request *proto.SurveyViewDeleteReq) error {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("DeleteSurveyView GetUserAndNow with error", xlog.Err(err))
		return errors.Wrap(errors.ErrNotFound, err)
	}

	if len(request.Ids) < 1 {
		err = fmt.Errorf("DeleteSurveyView Param err, ids len is invalid")
		return errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	for _, id := range request.Ids {
		_, err = model.DeleteSurveyView(id, baseInfo.Username)
		if err != nil {
			gLog.Error("DeleteSurveyView model.DeleteSurveyView with error", xlog.Err(err))
			return errors.Wrap(errors.ErrDBOperateFailed, err)
		}
	}

	return nil
}
