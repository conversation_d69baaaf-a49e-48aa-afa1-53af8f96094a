package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/cond"
	"math"
	"net/url"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"survey/config"
	"survey/constants"
	"survey/database"
	"survey/model"
	"survey/types"
	"survey/util"
	"survey/util/base"
	"sync"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xfile"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var hasQueueRunning bool

func HandleExportJob(ctx context.Context) error {
	gLog := xlog.FromContext(ctx)
	gLog.Debug("handleExportJob Cron job is running!")
	_, err := handleExportJob(ctx, 1)
	if err != nil {
		err = fmt.Errorf("HandleExportJob handleExportJob err: %v", err)
		return err
	}
	return nil
}

func handleExportJob(ctx context.Context, page int) (int, error) {
	gLog := xlog.FromContext(ctx)

	// 保证一个程序只有一个任务再跑
	if hasQueueRunning {
		gLog.Info("handleExportJob has running queue")
		return 0, nil
	}

	defer func() {
		hasQueueRunning = false
	}()

	var (
		//page     = 1
		size     = 100 //测试，数据大大的
		redisTTL = time.Minute * 5
	)
	exportTaskLogList := model.GetExportTaskLogListByStatus(0, page, size)

	var err error
	for _, exportTaskLog := range exportTaskLogList {
		redisKey := fmt.Sprintf("AWE_SURVEY_EXPORT_TASK_%d", exportTaskLog.Id)

		rdbLock := database.GetRdb().SetNX(ctx, redisKey, 1, redisTTL)
		if !rdbLock.Val() {
			gLog.Warn("HandleExportJob logTask is deal", xlog.String("redis key", redisKey))
			continue
		}

		gLog.Debug("handleExportJob start exportTaskLog", xlog.Int64("logID", exportTaskLog.Id))

		err = model.UpdatesSurveyExportLogByID(ctx, exportTaskLog.Id, map[string]interface{}{"status": 1})
		if err != nil {
			err = fmt.Errorf("handleExportJob model.UpdatesSurveyByID err%+v", err)
			database.GetRdb().Del(ctx, redisKey)
			return 1, err
		}

		hasQueueRunning = true
		urls, err := exportUserAnswerRecordTask(ctx, exportTaskLog)
		if err != nil {
			return 0, err
		}

		if len(urls) > 0 {
			urlData, err := url.QueryUnescape(urls)
			if err != nil {
				gLog.Warn("handleExportJob url.QueryUnescape err", xlog.Err(err), xlog.String("urlStr", urls))
			}
			err = model.UpdatesSurveyExportLogByID(ctx, exportTaskLog.Id, map[string]interface{}{
				"status":        2,
				"url":           urlData,
				"complete_time": base.GetNowTime(),
			})
			database.GetRdb().Del(ctx, redisKey)
			if err != nil {
				err = fmt.Errorf("handleExportJob model.UpdatesSurveyExportLogByID status 2 err%+v", err)
				database.GetRdb().Del(ctx, redisKey)
				return 1, err
			}
			gLog.Debug("handleExportJob exportUserAnswerRecordTask success")
		} else {
			err = model.UpdatesSurveyExportLogByID(ctx, exportTaskLog.Id, map[string]interface{}{
				"status": 3,
			})
			database.GetRdb().Del(ctx, redisKey)
			if err != nil {
				gLog.Info("handleExportJob exportUserAnswerRecordTask fail")
				err = fmt.Errorf("handleExportJob model.UpdatesSurveyExportLogByID status 2 err%+v", err)
				return 1, err
			}
		}
		database.GetRdb().Del(ctx, redisKey)
	}
	return page, err
}

func exportUserAnswerRecordTask(ctx context.Context, data *model.SurveyRecordExportLog) (string, error) {
	gLog := xlog.FromContext(ctx)

	var urls string
	surveyHashCode, _ := base.SurveyIdToHashCode(data.SurveyId)
	var (
		st *time.Time
		et *time.Time
	)
	if data.StartTime.Unix() <= 0 {
		st = nil
	} else {
		st = &data.StartTime
	}

	if data.EndTime.Unix() <= 0 {
		et = nil
	} else {
		et = &data.EndTime
	}

	var (
		hasView        = len(data.ViewContent) > 0 // 是否有过滤器
		ckViewWhereSql string
		err            error
	)

	if hasView {
		ckViewWhereSql, err = GetConditionViewerInstance().GenSQLFromContent(data.ViewContent, Gen_Sql_Kind_CK)
		if err != nil {
			err = fmt.Errorf("exportUserAnswerRecordTask genSQLFromContent ck err%+v queueId:%d", err, data.Id)
			return urls, err
		}
	}

	var fullValidRecordCount uint64
	if config.Get().StatisticUseMysqlData { // 使用mysql
		mysqlViewWhereSql, err := GetConditionViewerInstance().GenSQLFromContent(data.ViewContent, Gen_Sql_Kind_Mysql)
		if err != nil {
			err = fmt.Errorf("exportUserAnswerRecordTask genSQLFromContent mysql err%+v queueId:%d", err, data.Id)
			return urls, err
		}
		var count int64
		count, err = model.GetExportSurveyRecordListCount(ctx, data.SurveyId, data.IsValid, base.FormatTime(data.StartTime), base.FormatTime(data.EndTime), mysqlViewWhereSql)
		fullValidRecordCount = uint64(count)
	} else {
		fullValidRecordCount, err = model.GetRecordCountNew(ctx, surveyHashCode, data.IsValid, st, et, ckViewWhereSql)
	}

	if err != nil {
		err = fmt.Errorf("exportUserAnswerRecordTask model.GetSurveyExportCount err%+v", err)
		return urls, err
	}

	gLog.Info("exportUserAnswerRecordTask need export num",
		xlog.Uint64("count:", fullValidRecordCount), xlog.Int64("logID:%+v", data.Id))

	if fullValidRecordCount <= 0 {
		gLog.Info("exportUserAnswerRecordTask batch export record is empty")
		return "", nil
	}

	surveyConfig, err := model.SurveyPreviewData(ctx, data.SurveyId, data.ClientId)
	if err != nil {
		return "", err
	}

	// 获取问题列表 - 元数据
	flatQuestionListRaw, err := GenDetailSurveyQuestionList(ctx, surveyConfig)
	if err != nil {
		err = fmt.Errorf("exportUserAnswerRecordTask GenDetailSurveyQuestionList error: %+v", err)
		return urls, err
	}

	// 矩阵等数据类型要单独处理
	flatQuestionList := flatQuestionListFunc(ctx, flatQuestionListRaw)

	// 创建目录
	gLog.Debug("exportUserAnswerRecordTask create dir")

	if config.Get().ExportCompressPath == "" {
		err = fmt.Errorf("exportUserAnswerRecordTask need config exportCompressPath not empty")
		return urls, err
	}

	var dir = filepath.Join(config.Get().ExportCompressPath, xcast.ToString(data.Id)+"_"+strconv.FormatInt(time.Now().Unix(), 10))
	if !xfile.Exist(dir) {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			err = fmt.Errorf("exportUserAnswerRecordTask os.MkdirAll err%+v", err)
			return urls, err
		}
	}

	// 随机数在1-1000w之间
	randNum := int(data.DataNum)
	if randNum < 1 {
		randNum = config.Get().ExportTaskConf.RandomLimit
	} else if randNum > 10000000 {
		randNum = 10000000
	}

	// 解析问题过滤，并转换结构
	var extra = new(types.ExportQueueExtra)
	if len(data.Extra) > 0 {
		err := sonic.UnmarshalString(data.Extra, extra)
		if err != nil {
			return "", fmt.Errorf("extra 解析失败:%+v extra:%s", err, data.Extra)
		}
		if extra.QuestionFilter != nil {
			data.DataSource = 0 // 关闭问题随机
		}
	}

	var (
		exportRecordNum           = config.Get().ExportTaskConf.QueryRecordNum
		size                      = int(math.Ceil(float64(fullValidRecordCount) / float64(exportRecordNum)))
		needRand             bool = data.DataSource == 1 && int(fullValidRecordCount) > randNum // 是否需要随机 - 随机数量大于等于全量数据时候，不需要随机采样
		needRemoveFilePath        = make(map[string]string)
		writeCsvTotal        int  = 1 // 已写数据编号
		currentWriteCsvTotal int
		recordTotalNo        int     // 数据总编号
		writeCsvNo           int = 1 // 文件序号
		randMap                  = make(map[int]bool)
		writeCsvOpenMap          = make(map[int]bool)
	)

	// 需要随机抽样
	if needRand {
		randMap = util.GenRandomsFromMax(int(fullValidRecordCount), randNum)
		gLog.Info("exportUserAnswerRecordTask needRand randMap is", xlog.Any("map", randMap))
	}

	cityData, err := GetCityData(ctx)
	if err != nil {
		err = fmt.Errorf("exportUserAnswerRecordTask GetCityData err:%+v", err)
		return "", err
	}

	for idx := 0; idx < size; idx++ {

		//随机抽样完成，跳出循环
		if needRand && writeCsvTotal > len(randMap) {
			gLog.Info("exportUserAnswerRecordTask rand finish", xlog.Int("num", randNum))
			break
		}

		var (
			surveyRecordList []*types.ExportSurveyRecordAndDetailItem
			queryStartTime   = time.Now()
		)

		if config.Get().StatisticUseMysqlData { // mysql
			var mysqlViewWhereSql string
			if hasView {
				mysqlViewWhereSql, err = GetConditionViewerInstance().GenSQLFromContent(data.ViewContent, Gen_Sql_Kind_Mysql)
				if err != nil {
					err = fmt.Errorf("exportUserAnswerRecordTask genSQLFromContent mysql err%+v queueId:%d", err, data.Id)
					return urls, err
				}
			}
			surveyRecordList, err = model.GetExportSurveyRecordList(ctx, data.SurveyId, data.IsValid,
				base.FormatTime(data.StartTime), base.FormatTime(data.EndTime), exportRecordNum*idx, exportRecordNum, mysqlViewWhereSql)
		} else { // 使用clickhouse数据源
			var (
				st *time.Time
				et *time.Time
			)
			if data.StartTime.Unix() <= 0 {
				st = nil
			} else {
				st = &data.StartTime
			}

			if data.EndTime.Unix() <= 0 {
				et = nil
			} else {
				et = &data.EndTime
			}

			// 使用CK
			surveyRecordList, err = model.GetRecordsWithDetails(ctx, surveyHashCode, data.IsValid, st, et, uint32(exportRecordNum*idx), uint32(exportRecordNum), ckViewWhereSql)
		}

		if err != nil {
			gLog.Warn("exportUserAnswerRecordTask model.GetExportSurveyRecordList err", xlog.Err(err))
			return "", err
		}

		// 无数据，跳出循环
		if len(surveyRecordList) < 1 {
			gLog.Info("exportUserAnswerRecordTask surveyRecordList is empty", xlog.Any("logData", data))
			break
		}

		gLog.Info("exportUserAnswerRecordTask query surveyRecordList time cost",
			xlog.String("timecost", time.Since(queryStartTime).String()), xlog.Int("idx:", idx))
		var exportStartTime = time.Now()

		// 查询用户分群
		userClusterResult, err := SelectUserQueryClusterExists(ctx, data.ClientId, data.Extra, surveyRecordList)
		if err != nil {
			return "", err
		}

		// 查询用户分群
		userTagResult, err := SelectUserQueryTagVal(ctx, data.ClientId, data.Extra, surveyRecordList)
		if err != nil {
			return "", err
		}

		for _, item := range surveyRecordList {

			// 分群数据过滤
			if userClusterResult.UserClusterExists {
				filterThis := filterRecordByUserCluster(ctx, userClusterResult.ExtraData.UserCluster, userClusterResult.UserClusterDataMap, userClusterResult.UserClusterDataBeyondMap, item.RoleID, item.OpenId)
				if filterThis {
					continue
				}
			}

			// 用户标签过滤
			if userTagResult.UserTagExists {
				filterThis := filterRecordByUserTag(ctx, userTagResult.ExtraData.UserTag, userTagResult.UserTagDataMap, item.RoleID, item.OpenId)
				if filterThis {
					continue
				}
			}

			newRecordList := []*types.ExportSurveyRecordAndDetailItem{item}

			var (
				csvFile          *os.File
				err1             error
				filePath         string
				currentFileNo         = writeCsvNo
				needNewCreateCsv bool = !writeCsvOpenMap[currentFileNo] || currentWriteCsvTotal >= config.Get().ExportTaskConf.FileDataLimit
			)

			// 需要新创建csv
			if needNewCreateCsv {
				filePath = genExportCsvFilePath(dir, data.Name, writeCsvNo)
				csvFile, err1 = os.Create(filePath)
				if err1 != nil {
					err1 = fmt.Errorf("exportUserAnswerRecordTask os.Create err%+v filePath:%s", err1, filePath)
					return urls, err1
				}

				// 写入 UTF-8 BOM，防止中文乱码
				_, err1 = csvFile.WriteString("\xEF\xBB\xBF")
				if err1 != nil {
					csvFile.Close()
					return urls, err1
				}
				currentWriteCsvTotal = 0
				writeCsvNo++
				writeCsvOpenMap[writeCsvNo] = true
			} else {
				// 获取上一个文件
				currentFileNo = writeCsvNo - 1
				filePath = genExportCsvFilePath(dir, data.Name, currentFileNo)
				csvFile, err1 = os.OpenFile(filePath, os.O_WRONLY|os.O_APPEND, 0666)
				if err1 != nil {
					err1 = fmt.Errorf("exportUserAnswerRecordTask os.OpenFile err%+v filePath:%s", err1, filePath)
					return urls, err1
				}
			}

			csvStream := csv.NewWriter(csvFile)
			recordTotalNo, writeCsvTotal, currentWriteCsvTotal = createExportCsvFileStream(
				ctx,
				csvStream,
				needNewCreateCsv,
				newRecordList,
				flatQuestionList,
				cityData,
				data.DataType,
				needRand,
				randMap,
				recordTotalNo,
				writeCsvTotal,
				currentWriteCsvTotal,
				extra,
			)
			needRemoveFilePath[filePath] = filePath
			//gLog.Debug("exportUserAnswerRecordTask file write time cost", xlog.String("timecost", time.Since(queryStartTime).String()), xlog.String("filePath:", filePath))
			csvFile.Close()
		}

		gLog.Info("exportUserAnswerRecordTask createExportCsvFileStream cost time v3", xlog.String("time cost", time.Since(exportStartTime).String()))
	}

	// 开始压缩上传到oss
	var filePath = filepath.Join(config.Get().ExportCompressPath, data.Name+".zip")
	err = util.CompressZip(dir, filePath)
	if err != nil {
		return "", err
	}

	// 移除文件
	for _, needRemoveFilePathStr := range needRemoveFilePath {
		err := os.Remove(needRemoveFilePathStr)
		if err != nil {
			gLog.Warn("exportUserAnswerRecordTask os.Remove fail", xlog.Err(err))
		}
	}

	urls, err = UploadFileToOss(filePath, fmt.Sprintf("/%v", filePath))
	if err != nil {
		err = fmt.Errorf("exportUserAnswerRecordTask UploadFileToOss err:%v", err)
		return urls, err
	}
	return urls, nil
}

func createExportCsvFileStream(
	ctx context.Context,
	csvStream *csv.Writer,
	isNewFile bool,
	res []*types.ExportSurveyRecordAndDetailItem,
	flatQuestionList []types.QuestionList,
	cityData []Region,
	dataType int32,
	needRand bool,
	randMap map[int]bool,
	recordTotalNo int,
	writeCsvTotal int,
	currentWriteCsvTotal int,
	queueExtra *types.ExportQueueExtra,
) (int, int, int) {
	gLog := xlog.FromContext(ctx)
	var csvDataList = make([][]string, 0, 2000)
	var formatUserRecordDataList = formatRecordData(ctx, res)
	var (
		defaultVal      = "-"
		csvHeaders      []string
		csvHeadMaps     = make(map[string]bool)
		firstColumnName = "编号"
	)

	// 基础表头构建
	csvHeaders = append(csvHeaders, "编号")
	for _, header := range types.ExportBaseHeaders {
		csvHeaders = append(csvHeaders, header.Label)
	}

	for recordIndex, userRecordDetail := range formatUserRecordDataList {
		if userRecordDetail.UserRecord == nil || userRecordDetail.RecordDetail == nil {
			gLog.Warn("createExportCsvFileStream userRecordDetail has empty pointer")
			continue
		}

		var isFirst = recordIndex == 0
		var tempMap = make(map[string]interface{})
		tempMap[firstColumnName] = writeCsvTotal
		var extra = new(Extra)
		if userRecordDetail.UserRecord.Extra != "" {
			err := json.Unmarshal([]byte(userRecordDetail.UserRecord.Extra), extra)
			if err != nil {
				gLog.Warn("createExportCsvFileStream userRecordDetail.UserRecord.Extra Unmarshal err", xlog.Err(err), xlog.String("extra", userRecordDetail.UserRecord.Extra))
			} else if userRecordDetail.UserRecord.PlatID == "" {
				userRecordDetail.UserRecord.PlatID = extra.PlatID
			}
		}
		for _, header := range types.ExportBaseHeaders {
			switch header.Value {
			case "id":
				tempMap[header.Label] = userRecordDetail.UserRecord.ID
			case "openid":
				tempMap[header.Label] = userRecordDetail.UserRecord.OpenId
				if userRecordDetail.UserRecord.OpenId == "" {
					tempMap[header.Label] = defaultVal
				}
			case "role_id":
				tempMap[header.Label] = userRecordDetail.UserRecord.RoleID
				if userRecordDetail.UserRecord.RoleID == "" {
					tempMap[header.Label] = defaultVal
				}
			case "device_id":
				tempMap[header.Label] = userRecordDetail.UserRecord.DeviceId
				if userRecordDetail.UserRecord.DeviceId == "" {
					tempMap[header.Label] = defaultVal
				}
			case "ip":
				tempMap[header.Label] = userRecordDetail.UserRecord.IP
				if userRecordDetail.UserRecord.IP == "" {
					tempMap[header.Label] = defaultVal
				}
			case "location":
				tempMap[header.Label] = defaultVal
				if userRecordDetail.UserRecord.Location != "" {
					tempMap[header.Label] = userRecordDetail.UserRecord.Location
				} else if userRecordDetail.UserRecord.Extra != "" {
					if extra != nil {
						tempMap[header.Label] = extra.Location.String()
					}
				}
			case "is_valid":
				if userRecordDetail.UserRecord.IsValid == 1 {
					tempMap[header.Label] = "无效"
				} else {
					tempMap[header.Label] = "有效"
				}
			case "begin_time":
				if userRecordDetail.UserRecord.BeginTime != "" {
					tempMap[header.Label] = fmt.Sprintf("\t%v", userRecordDetail.UserRecord.BeginTime)
				} else {
					tempMap[header.Label] = defaultVal
				}
			case "end_time":
				if userRecordDetail.UserRecord.EndTime != "" {
					tempMap[header.Label] = fmt.Sprintf("\t%v", userRecordDetail.UserRecord.EndTime)
				} else {
					tempMap[header.Label] = defaultVal
				}
			case "answer_time":
				if userRecordDetail.UserRecord.BeginTime != "" && userRecordDetail.UserRecord.EndTime != "" {
					var ts, _ = base.DiffUtcTimeStr(userRecordDetail.UserRecord.BeginTime, userRecordDetail.UserRecord.EndTime)
					tempMap[header.Label] = defaultVal
					if tsInt := xcast.ToInt(ts); tsInt > 0 {
						tempMap[header.Label] = tsInt
					}
				}
			case "ctime":
				if userRecordDetail.UserRecord.Ctime != "" {
					tempMap[header.Label] = fmt.Sprintf("\t%v", userRecordDetail.UserRecord.Ctime)
				} else {
					tempMap[header.Label] = defaultVal
				}
			case "plat_id":
				tempMap[header.Label] = userRecordDetail.UserRecord.PlatID
				if userRecordDetail.UserRecord.PlatID == "" {
					tempMap[header.Label] = defaultVal
				}
			default:
				gLog.Error("createExportCsvFileStream unknown field", xlog.String(":", header.Value))
			}
		}

		var (
			extraHeaderArr = make([]string, 0)            // 拓展header
			extraTempMap   = make(map[string]interface{}) // 拓展字段 - 数据
			isKeepImgMap   = make(map[string]bool)
		)

		for _, questionItem := range flatQuestionList {
			var (
				statisticsMethod                      = questionItem.ConfigProps.StatisticsMethod
				recordDetailList                      = userRecordDetail.RecordDetail[questionItem.ConfigProps.UniqueKey]
				exportQuestionSelectOptionHandleParam = types.ExportQuestionSelectOptionHandleParam{
					QuestionItem:     questionItem,
					RecordDetailList: recordDetailList,
					DataType:         dataType,
					IsFirst:          isFirst,
				}
			)
			if statisticsMethod == "matrix" && questionItem.ConfigProps.QuestionType == "select" && questionItem.ConfigProps.SelectMode == "single" { // 单选矩阵
				exportQuestionSelectOptionHandleParam.SelectedItemFunc = func(option, record string) bool {
					return option == record
				}
				exportQuestionSelectOptionHandleParam.IsSingle = true
				exportQuestionSelectOptionHandleParam.DataType0Func = func(index int) int {
					return index + 1
				}
				extraHeaderArr, extraTempMap = exportQuestionSelectOptionHandle(ctx, exportQuestionSelectOptionHandleParam)
			} else if statisticsMethod == "matrix" && questionItem.ConfigProps.QuestionType == "select" && questionItem.ConfigProps.SelectMode == "multi" { // 多选矩阵
				exportQuestionSelectOptionHandleParam.SelectedItemFunc = func(option, record string) bool {
					return option == record
				}
				exportQuestionSelectOptionHandleParam.DataType0Func = func(index int) int {
					return 1
				}
				extraHeaderArr, extraTempMap = exportQuestionSelectOptionHandle(ctx, exportQuestionSelectOptionHandleParam)
			} else if statisticsMethod == "order" { // 排序
				exportQuestionSelectOptionHandleParam.SelectedItemFunc = func(option, record string) bool {
					optionList := strings.Split(option, "|")
					return len(optionList) > 0 && optionList[0] == record
				}
				exportQuestionSelectOptionHandleParam.DataType0Func = func(index int) int {
					return index + 1
				}
				exportQuestionSelectOptionHandleParam.NeedBlank = true
				extraHeaderArr, extraTempMap = exportQuestionSelectOptionHandle(ctx, exportQuestionSelectOptionHandleParam)
			} else if statisticsMethod == "select" && questionItem.ConfigProps.SelectMode == "multi" { // 多选
				exportQuestionSelectOptionHandleParam.SelectedItemFunc = func(option, record string) bool {
					return option == record
				}
				exportQuestionSelectOptionHandleParam.DataType0Func = func(index int) int {
					return index + 1
				}
				exportQuestionSelectOptionHandleParam.NeedBlank = true
				extraHeaderArr, extraTempMap = exportQuestionSelectOptionHandle(ctx, exportQuestionSelectOptionHandleParam)
			} else if statisticsMethod == "select" && questionItem.ConfigProps.SelectMode == "single" { // 单选
				exportQuestionSelectOptionHandleParam.SelectedItemFunc = func(option, record string) bool {
					return option == record
				}
				exportQuestionSelectOptionHandleParam.DataType0Func = func(index int) int {
					return index + 1
				}
				exportQuestionSelectOptionHandleParam.NeedBlank = true
				exportQuestionSelectOptionHandleParam.IsRate = questionItem.ConfigProps.IsRate
				exportQuestionSelectOptionHandleParam.IsKeepImageTag = true
				exportQuestionSelectOptionHandleParam.IsSingle = true
				extraHeaderArr, extraTempMap = exportQuestionSelectOptionHandle(ctx, exportQuestionSelectOptionHandleParam)
				if questionItem.ConfigProps.IsRate {
					for titleUniKey, _ := range extraTempMap {
						isKeepImgMap[titleUniKey] = true
					}
				}
			} else {
				var value string
				if statisticsMethod == "input" {
					if !questionItem.ConfigProps.IsAddress {
						if len(recordDetailList) > 0 {
							value = fmt.Sprintf("\t%s", recordDetailList[0].Text)
						}
					} else {
						if len(recordDetailList) > 0 {
							var path = make([]string, 0)
							path = treeFindByPath(cityData, func(data Region) bool {
								return data.Value == recordDetailList[0].Option
							}, path)
							if len(path) > 0 {
								value = strings.Join(path, "-")
							}
						}
					}
				} else if statisticsMethod == "gauge" {
					if len(recordDetailList) > 0 {
						value = recordDetailList[0].Option
					}
				}

				var finalValue = value
				questionTitleStr := getQuestionBaseConfigTitle(ctx, questionItem.QuestionBaseConfig.QuestionTitle)
				extraTempMap[questionTitleStr] = util.CsvSpecialValHandle(finalValue)
				if isFirst {
					extraHeaderArr = append(extraHeaderArr, questionTitleStr)
				}
			}
			// 合并数据 - 头
			for _, extraHeader := range extraHeaderArr {
				if !csvHeadMaps[extraHeader] {
					csvHeadMaps[extraHeader] = true
					csvHeaders = append(csvHeaders, extraHeader)
				}
			}
			// 合并数据 - 值
			for extraTitle, extraVal := range extraTempMap {
				tempMap[extraTitle] = extraVal
			}
		}

		// 写入标题行
		needWriteTitle := isFirst && isNewFile
		if needWriteTitle {
			var csvHeadersWithoutHtml []string
			for _, header := range csvHeaders {
				csvHeadersWithoutHtml = append(csvHeadersWithoutHtml, util.RemoveRichTextTags(header, isKeepImgMap[header]))
			}
			csvDataList = append(csvDataList, csvHeadersWithoutHtml)
		}

		// 不在随机数结果集中，丢弃这条数据
		recordTotalNo++
		if needRand && !randMap[recordTotalNo] {
			continue
		}

		// 问题过滤
		if queueExtra != nil && queueExtra.QuestionFilter != nil {
			filter, err := questionFilter(queueExtra.QuestionFilter, userRecordDetail)
			if err != nil {
				xlog.FromContext(ctx).Error("export questionFilter err", xlog.Err(err))
			}
			if filter {
				continue
			}
		}

		// 更新写入数据序号
		currentWriteCsvTotal++
		writeCsvTotal++

		// 写入行数据
		var rawLine []string
		for _, header := range csvHeaders {
			rawVal := tempMap[header]
			if rawVal == nil {
				rawVal = ""
			}
			rawLine = append(rawLine, xcast.ToString(rawVal))
		}
		if len(csvDataList) == 2000 {
			err := csvStream.WriteAll(csvDataList)
			if err != nil {
				gLog.Error("createExportCsvFileStream write rawLine err", xlog.Err(err))
				return recordTotalNo, writeCsvTotal, currentWriteCsvTotal
			} else {
				csvDataList = csvDataList[:0]
			}
		}
		csvDataList = append(csvDataList, rawLine)
	}

	// 清理尾巴
	if len(csvDataList) > 0 {
		err := csvStream.WriteAll(csvDataList)
		if err != nil {
			gLog.Error("createExportCsvFileStream write rawLine err", xlog.Err(err))
			return recordTotalNo, writeCsvTotal, currentWriteCsvTotal
		}
	}

	return recordTotalNo, writeCsvTotal, currentWriteCsvTotal
}

func formatRecordData(ctx context.Context, logList []*types.ExportSurveyRecordAndDetailItem) []*types.ExportFormatRecordData {
	var (
		gLog       = xlog.FromContext(ctx)
		resultList = make([]*types.ExportFormatRecordData, 0)
	)

	for _, item := range logList {

		var (
			recordQuestion []string
			recordOption   []string
			recordText     []string
		)

		if config.Get().StatisticUseMysqlData {
			recordQuestion = strings.Split(item.RecordQuestion, ",|")
			recordOption = strings.Split(item.RecordOption, ",|")
			recordText = strings.Split(item.RecordText, ",|")
		} else {
			recordQuestion = item.Questions
			recordOption = item.Options
			recordText = item.Texts
		}

		var optionMaxIndex = len(recordOption) - 1
		var textMaxIndex = len(recordText) - 1

		var recordDetail = make(map[string][]types.ExportDetailItem)
		for idx, qItem := range recordQuestion {

			if idx > optionMaxIndex || idx > textMaxIndex {
				gLog.Warn("formatRecordData idx beyond", xlog.Uint32("logId", item.ID))
				continue
			}

			var temp = types.ExportDetailItem{
				Option: recordOption[idx],
				Text:   recordText[idx],
			}

			if _, ok := recordDetail[qItem]; !ok {
				recordDetail[qItem] = make([]types.ExportDetailItem, 0)
			}
			recordDetail[qItem] = append(recordDetail[qItem], temp)
		}

		var answerTime string
		if item.BeginTime != "" && item.EndTime != "" {
			var err error
			answerTime, err = base.DiffUtcTimeStr(item.BeginTime, item.EndTime)
			if err != nil {
				gLog.Warn("formatRecordData base.DiffUtcTimeStr has wrong", xlog.Err(err))
			}
		}

		resultList = append(resultList, &types.ExportFormatRecordData{
			UserRecord:   item,
			AnswerTime:   answerTime,
			RecordDetail: recordDetail,
		})
	}

	return resultList
}

func treeFindByPath(tree []Region, handleFunc func(params Region) bool, path []string) []string {
	if len(tree) < 1 {
		return path
	}

	for _, data := range tree {
		path = append(path, data.Label)
		if handleFunc(data) {
			return path
		}
		if len(data.Children) > 0 {
			var findChildren = treeFindByPath(data.Children, handleFunc, path)
			if len(findChildren) > 0 {
				return findChildren
			}
		}
		path = path[:len(path)-1]
	}

	return nil
}

func exportQuestionSelectOptionHandle(ctx context.Context, param types.ExportQuestionSelectOptionHandleParam) ([]string, map[string]interface{}) {
	var (
		csvHeaderArr []string
		tempMap      = make(map[string]interface{})
	)

	if param.IsRate { // 评分
		var otherValue string
		if len(param.RecordDetailList) > 0 {
			otherValue = param.RecordDetailList[0].Option
		}
		questionTitle := param.QuestionItem.QuestionBaseConfig.QuestionTitle
		questionTitleStr := getQuestionBaseConfigTitle(ctx, questionTitle)
		tempMap[questionTitleStr] = otherValue
		if param.IsFirst {
			csvHeaderArr = append(csvHeaderArr, questionTitleStr)
		}
	} else {

		// 该题是否已作答
		var isAnswer bool
		for _, item := range param.RecordDetailList {
			if strings.TrimSpace(item.Option) != "" || strings.TrimSpace(item.Text) != "" {
				isAnswer = true
				break
			}
		}

		var questionTitleBase = getQuestionBaseConfigTitle(ctx, param.QuestionItem.QuestionBaseConfig.QuestionTitle)
		// 单选题目头在这里添加 - 单选题目只添加一次
		if param.IsFirst && param.IsSingle {
			csvHeaderArr = append(csvHeaderArr, questionTitleBase)
		}

		// selectOption
		for index, option := range param.QuestionItem.QuestionSelectConfig.SelectOptions {
			var labelStr string = GetLabelStr(option.Label)

			// 单选 不需要尾巴
			var questionTitle = questionTitleBase
			if !param.IsSingle {
				questionTitle += "_" + labelStr
			}

			var finalValue string
			var tmp *types.ExportDetailItem
			for _, item := range param.RecordDetailList {
				if param.SelectedItemFunc(item.Option, option.Value) {
					tmp = &item
					break
				}
			}
			if tmp != nil {
				finalValue = labelStr
			}

			if param.IsFirst && !param.IsSingle {
				csvHeaderArr = append(csvHeaderArr, questionTitle)
			}

			selected := finalValue != ""
			if param.DataType == 0 {

				// 本题未回答，默认是空
				var defaultIndex interface{} = 0
				if !isAnswer {
					defaultIndex = ""
				}

				if param.IsSingle {
					if selected {
						tempMap[questionTitle] = param.DataType0Func(index)
					} else if tempMap[questionTitle] == nil { // 未选中
						tempMap[questionTitle] = defaultIndex
					}
				} else {
					if selected {
						tempMap[questionTitle] = 1
					} else {
						tempMap[questionTitle] = defaultIndex
					}
				}
			} else {
				if param.IsSingle {
					if selected {
						tempMap[questionTitle] = util.CsvSpecialValHandle(finalValue)
					} else if tempMap[questionTitle] == nil { // 未选中
						tempMap[questionTitle] = ""
					}
				} else {
					tempMap[questionTitle] = util.CsvSpecialValHandle(finalValue)
				}
			}

			var allow bool
			if _, ok := option.BlankFill.(string); !ok {
				blankFillMap, ok2 := option.BlankFill.(map[string]interface{})
				if ok2 {
					if allowVal, ok3 := blankFillMap["allow"]; ok3 {
						allow = xcast.ToBool(allowVal)
					}
				}
			}

			if param.NeedBlank && allow { // 是否有填空
				var otherTitle = questionTitle + "_other"
				if param.IsSingle {
					otherTitle += xcast.ToString(index)
				}
				var otherValue string
				if tmp != nil {
					otherValue = tmp.Text
				}
				tempMap[otherTitle] = util.CsvSpecialValHandle(otherValue)
				if param.IsFirst {
					csvHeaderArr = append(csvHeaderArr, otherTitle)
				}
			}
		}
	}

	return csvHeaderArr, tempMap
}

func getQuestionBaseConfigTitle(ctx context.Context, questionTitle interface{}) string {
	gLog := xlog.FromContext(ctx)
	var titleStr string
	switch title := questionTitle.(type) {
	case map[string]interface{}:
		m := questionTitle.(map[string]interface{})
		titleStr = xcast.ToString(m["value"])
	case string:
		titleStr = title
	default:
		err := fmt.Errorf("getQuestionBaseConfigTitle not report titleType type:%+v questionTitle:%+v", questionTitle, questionTitle)
		gLog.Error("", xlog.Err(err))
	}
	return titleStr
}

func flatQuestionListFunc(ctx context.Context, questionList []types.QuestionList) []types.QuestionList {
	var flatList = make([]types.QuestionList, 0)

	for _, questionItem := range questionList {
		if questionItem.ConfigProps.IsMatrix {
			for idx, t1 := range questionItem.QuestionComponentConfig.RowTitleSelectOptions {
				var (
					chQuestionId  = fmt.Sprintf("%v.%v", questionItem.ConfigProps.QuestionId, idx+1)
					questionTitle = fmt.Sprintf("%v、%s_%v", chQuestionId, getQuestionBaseConfigTitle(ctx, questionItem.QuestionBaseConfig.QuestionTitle), GetLabelStr(t1.Label))
				)

				temp := questionItem
				temp.QuestionBaseConfig = questionItem.QuestionBaseConfig
				temp.QuestionBaseConfig.QuestionTitle = questionTitle
				temp.ConfigProps = questionItem.ConfigProps
				temp.ConfigProps.ParentQuestionId = questionItem.ConfigProps.QuestionId
				temp.ConfigProps.ParentUniqueKey = questionItem.ConfigProps.UniqueKey
				temp.ConfigProps.UniqueKey = t1.Value
				temp.ConfigProps.QuestionId = chQuestionId

				flatList = append(flatList, temp)
			}

			for idx, t1 := range questionItem.QuestionSelectConfig.RowTitleSelectOptionsV2 {
				var (
					chQuestionId  = fmt.Sprintf("%v.%v", questionItem.ConfigProps.QuestionId, idx+1)
					questionTitle = fmt.Sprintf("%v、%s_%v", chQuestionId, getQuestionBaseConfigTitle(ctx, questionItem.QuestionBaseConfig.QuestionTitle), t1.Label.Value) //NOdeJS t1.label?.val
				)
				temp := questionItem
				temp.QuestionBaseConfig = questionItem.QuestionBaseConfig
				temp.QuestionBaseConfig.QuestionTitle = questionTitle

				temp.ConfigProps = questionItem.ConfigProps
				temp.ConfigProps.ParentQuestionId = questionItem.ConfigProps.QuestionId
				temp.ConfigProps.ParentUniqueKey = questionItem.ConfigProps.UniqueKey
				temp.ConfigProps.UniqueKey = t1.Value
				temp.ConfigProps.QuestionId = chQuestionId

				flatList = append(flatList, temp)
			}
			continue
		} else {
			temp := questionItem
			temp.QuestionBaseConfig = questionItem.QuestionBaseConfig
			temp.QuestionBaseConfig.QuestionTitle = fmt.Sprintf("%v、%v", questionItem.ConfigProps.QuestionId, getQuestionBaseConfigTitle(ctx, questionItem.QuestionBaseConfig.QuestionTitle))
			flatList = append(flatList, temp)
		}
	}
	return flatList
}

func GetLabelStr(label interface{}) string {
	var labelStr string
	if labelTmp, ok := label.(string); ok {
		labelStr = labelTmp
	} else if labelTmp1, ok2 := label.(map[string]interface{}); ok2 {
		labelStr = xcast.ToString(labelTmp1["value"])
	} else {
		xlog.Warn(fmt.Sprintf("GetLabelStr Label type:%T label:%+v", label, label))
	}
	return labelStr
}

func genExportCsvFilePath(dir string, name string, writeCsvNo int) string {
	fileName := fmt.Sprintf("%v_%v.csv", name, writeCsvNo)
	filePath := filepath.Join(dir, fileName)
	return filePath
}

type Extra struct {
	Location Location `json:"location"`
	PlatID   string   `json:"platid"`
}

type Localtion struct {
	Location Location `json:"location"`
}

type Location struct {
	ContinentCode string `json:"continent_code"`
	Continent     string `json:"continent"`
	CountryCode   string `json:"country_code"`
	Country       string `json:"country"`
	Province      string `json:"province"`
	City          string `json:"city"`
	European      bool   `json:"european"`
}

func (l *Location) String() string {
	location := l.Country
	if l.Province != "" {
		location += "_" + l.Province
	}

	if l.City != "" {
		location += "_" + l.City
	}
	return location
}

type SelectUserQueryClusterExistsResponse struct {
	UserClusterExists        bool                    // 有用户分群
	UserClusterDataMap       *sync.Map               // 分群数据 - 属于
	UserClusterDataBeyondMap *sync.Map               // 分群数据 - 不属于
	ExtraData                *types.ExportQueueExtra // extra
}

func SelectUserQueryClusterExists(ctx context.Context, clientId string, extra string, surveyRecordList []*types.ExportSurveyRecordAndDetailItem) (*SelectUserQueryClusterExistsResponse, error) {
	gLog := xlog.FromContext(ctx)
	res := &SelectUserQueryClusterExistsResponse{
		UserClusterExists:        false,
		UserClusterDataMap:       new(sync.Map),
		UserClusterDataBeyondMap: new(sync.Map),
		ExtraData:                new(types.ExportQueueExtra),
	}
	if extra != "" {
		var d = new(types.ExportQueueExtra)
		err := sonic.UnmarshalString(extra, d)
		if err != nil {
			return res, fmt.Errorf("extra sonic.UnmarshalString err:%+v", err)
		}
		res.ExtraData = d
		res.UserClusterExists = len(res.ExtraData.UserCluster) > 0

		for _, cluster := range res.ExtraData.UserCluster {
			if cluster == nil || cluster.ClusterInfo.EntityName == "" {
				gLog.Warn("exportUserAnswerRecordTask extra.UserCluster has empty")
				continue
			}

			//切割数据为二维数组，每200个一组
			entityBatch := SplitUserQueryEntityBatch(surveyRecordList, cluster.ClusterInfo.EntityName)

			// 并发请求
			var workers = make(chan struct{}, config.Get().UserQuery.ClusterThreadNum)
			var wg = sync.WaitGroup{}
			for _, batch := range entityBatch {
				workers <- struct{}{}
				wg.Add(1)
				go func(entityIds []string) {
					defer func() {
						<-workers
						wg.Done()
					}()
					if len(entityIds) < 1 {
						return
					}
					req := &types.UserQueryClusterExistReq{
						ClusterName: cluster.ClusterInfo.ClusterName,
						ClientID:    xcast.ToInt64(clientId),
						EntityIds:   entityIds,
					}
					inCluster, err2 := userQueryTool.IsUserExistInCluster(ctx, req)
					if err2 != nil {
						gLog.Error("exportUserAnswerRecordTask userQueryTool.IsUserExistInCluster http err", xlog.Err(err2))
						return
					}
					if inCluster != nil {
						if inCluster.Code != 0 {
							err3 := fmt.Errorf("user-query response code:%+v info:%s request-id:%s", inCluster.Code, inCluster.Info, inCluster.RequestId)
							gLog.Error("exportUserAnswerRecordTask userQueryTool.IsUserExistInCluster code err", xlog.Err(err3))
							return
						}
						for _, item := range inCluster.Data.Item {
							// 只要命中标签的数据
							if !item.Exist {
								continue
							}
							if cluster.Relation == 1 { // 属于
								res.UserClusterDataMap.Store(item.EntityId, item.Exist)
							} else if cluster.Relation == 2 { // 不属于
								res.UserClusterDataBeyondMap.Store(item.EntityId, item.Exist)
							}
						}
					}
				}(batch)
			}
			wg.Wait()
		}
	}
	return res, nil
}

type SelectUserQueryTagValResponse struct {
	UserTagExists  bool                    // 有用户标签
	UserTagDataMap *sync.Map               // 标签数据val map
	ExtraData      *types.ExportQueueExtra // extra
}

func SelectUserQueryTagVal(ctx context.Context, clientId string, extra string, surveyRecordList []*types.ExportSurveyRecordAndDetailItem) (*SelectUserQueryTagValResponse, error) {
	gLog := xlog.FromContext(ctx)
	res := &SelectUserQueryTagValResponse{
		UserTagExists:  false,
		UserTagDataMap: new(sync.Map),
		ExtraData:      new(types.ExportQueueExtra),
	}
	if extra != "" {
		var d = new(types.ExportQueueExtra)
		err := sonic.UnmarshalString(extra, d)
		if err != nil {
			return res, fmt.Errorf("extra sonic.UnmarshalString err:%+v", err)
		}
		res.ExtraData = d
		res.UserTagExists = len(res.ExtraData.UserTag) > 0

		for _, tag := range res.ExtraData.UserTag {
			if tag == nil || tag.TagInfo.TagEntityName == "" {
				gLog.Warn("exportUserAnswerRecordTask extra.UserTag has empty")
				continue
			}

			//切割数据为二维数组，每200个一组
			entityBatch := SplitUserQueryEntityBatch(surveyRecordList, tag.TagInfo.TagEntityName)

			// 并发请求
			var workers = make(chan struct{}, config.Get().UserQuery.ClusterThreadNum)
			var wg = sync.WaitGroup{}
			for _, batch := range entityBatch {
				workers <- struct{}{}
				wg.Add(1)
				go func(entityIds []string) {
					defer func() {
						<-workers
						wg.Done()
					}()
					if len(entityIds) < 1 {
						return
					}
					req := &types.UserQueryTagListReq{
						TagName:   tag.TagInfo.TagName,
						ClientId:  xcast.ToInt64(clientId),
						EntityIds: entityIds,
					}
					tagListRes, err2 := userQueryTool.GetUserTagList(ctx, req)
					if err2 != nil {
						gLog.Error("exportUserAnswerRecordTask userQueryTool.GetUserTagList http err", xlog.Err(err2))
						return
					}
					if tagListRes != nil {
						if tagListRes.Code != 0 {
							err3 := fmt.Errorf("user-query SelectUserQueryTagVal response code:%+v info:%s request-id:%s", tagListRes.Code, tagListRes.Info, tagListRes.RequestId)
							gLog.Error("exportUserAnswerRecordTask SelectUserQueryTagVal userQueryTool.GetUserTagList code err", xlog.Err(err3))
							return
						}
						for _, item := range tagListRes.Data.Item {
							res.UserTagDataMap.Store(item.EntityId, item.Val)
						}
					}
				}(batch)
			}
			wg.Wait()
		}
	}
	return res, nil
}

func SplitUserQueryEntityBatch(surveyRecordList []*types.ExportSurveyRecordAndDetailItem, entityName string) [][]string {
	// 实现类似每次请求200， 并发5个请求
	// 每200条数据为一组
	var entityList []string
	var entityBatch [][]string
	for i, item := range surveyRecordList {
		// 切片请求
		if i > 0 && int32(i)%config.Get().UserQuery.ClusterEntityNum == 0 {
			if len(entityList) > 0 {
				entityIds := make([]string, len(entityList))
				copy(entityIds, entityList)
				entityList = entityList[:0]
				// 查询用户分群信息
				entityBatch = append(entityBatch, entityIds)
			}
		}
		// roleID
		if entityName == constants.UserClusterEntityRoleID {
			if item.RoleID != "" {
				entityList = append(entityList, item.RoleID)
			}
		} else if entityName == constants.UserClusterEntityNID {
			if item.OpenId != "" {
				entityList = append(entityList, item.OpenId)
			}
		}
	}

	// 最后一些不够200条的数据
	if len(entityList) > 0 {
		entityBatch = append(entityBatch, entityList)
	}
	return entityBatch
}

func filterRecordByUserCluster(ctx context.Context, userClusters []*types.UserCluster, userClusterDataMap, userClusterDataBeyondMap *sync.Map, roleID, nid string) bool {
	var filterThis bool
	for _, cluster := range userClusters {
		if cluster == nil {
			continue
		}
		// 属于
		if cluster.Relation == 1 {
			if cluster.ClusterInfo.EntityName == constants.UserClusterEntityNID {
				_, ok := userClusterDataMap.Load(nid)
				if !ok {
					filterThis = true
				}
			} else if cluster.ClusterInfo.EntityName == constants.UserClusterEntityRoleID {
				_, ok := userClusterDataMap.Load(roleID)
				if !ok {
					filterThis = true
				}
			}
		} else if cluster.Relation == 2 { // 不属于
			if cluster.ClusterInfo.EntityName == constants.UserClusterEntityNID {
				_, ok := userClusterDataBeyondMap.Load(nid)
				if ok {
					filterThis = true
				}
			} else if cluster.ClusterInfo.EntityName == constants.UserClusterEntityRoleID {
				_, ok := userClusterDataBeyondMap.Load(roleID)
				if ok {
					filterThis = true
				}
			}
		}
	}
	return filterThis
}

func filterRecordByUserTag(ctx context.Context, userTags []*types.UserTag, userTagDataMap *sync.Map, roleID, nid string) bool {
	var filterThis bool
	for _, tag := range userTags {
		if tag == nil || tag.TagInfo == nil {
			continue
		}

		var (
			entityName = tag.TagInfo.TagEntityName
			isNid      = entityName == constants.UserClusterEntityNID
			isRid      = entityName == constants.UserClusterEntityRoleID
		)

		if tag.Relation == 1 {
			if isNid {
				value, _ := userTagDataMap.Load(nid)
				if value != tag.TagVal {
					filterThis = true
				}
			} else if isRid {
				value, _ := userTagDataMap.Load(roleID)
				if value != tag.TagVal {
					filterThis = true
				}
			}
		} else if tag.Relation == 2 {
			if isNid {
				value, _ := userTagDataMap.Load(nid)
				if value == tag.TagVal {
					filterThis = true
				}
			} else if isRid {
				value, _ := userTagDataMap.Load(roleID)
				if value == tag.TagVal {
					filterThis = true
				}
			}
		}
	}
	return filterThis
}

func checkFilter(questionCond *cond.Condition) error {
	var logicAnd = "&&"
	var logicOr = "||"
	var operator = questionCond.Operator

	// 如果是逻辑符，非运算符，遍历子节点
	var conditions []*cond.Condition
	if operator == logicAnd || operator == logicOr {
		conditions = questionCond.Conditions
	} else { // 本身就是cond
		conditions = append(conditions, questionCond)
	}

	for _, condition := range conditions {
		// 判断数据要不要过滤
		switch condition.Operator {
		case "==", "!=": // 等于、不等于
		case "in", "not_in": // 包含、不包含
			v := reflect.ValueOf(condition.Rhs.Value)
			if v.Kind() == reflect.Slice || v.Kind() == reflect.Array {
				break
			} else {
				return fmt.Errorf("checkFilter not support type kind:%+v", v.Kind())
			}
		case "is_empty", "not_empty": // 为空(无值)、不为空(有值)
		case logicAnd, logicOr: // 逻辑运算符
			err := checkFilter(condition)
			if err != nil {
				return fmt.Errorf("checkFilter logic err:%+v", err)
			}
		default:
			return fmt.Errorf("checkFilter not support math operator:%+v", condition.Operator)
		}
	}

	return nil
}

func questionFilter(questionCond *cond.Condition, userRecordDetail *types.ExportFormatRecordData) (bool, error) {
	var logicAnd = "&&"
	var logicOr = "||"
	var matches = make([]bool, 0)
	var operator = questionCond.Operator

	// 如果是逻辑符，非运算符，遍历子节点
	var conditions []*cond.Condition
	if operator == logicAnd || operator == logicOr {
		conditions = questionCond.Conditions
	} else { // 本身就是cond
		conditions = append(conditions, questionCond)
	}

	for _, condition := range conditions {
		var questionHashCode = condition.Lhs
		var selectedOptions = userRecordDetail.RecordDetail[questionHashCode]
		var noAnswer bool
		if len(selectedOptions) < 1 {
			noAnswer = true
		}

		// 判断数据要不要过滤
		switch condition.Operator {
		case "==": // 等于
			var targetVal = strings.Trim(xcast.ToString(condition.Rhs.Value), "'")
			for _, option := range selectedOptions {
				if option.Option == targetVal {
					matches = append(matches, true)
				} else {
					matches = append(matches, false)
				}
			}
		case "!=": // 不等于
			// 未作答也肯定不等于的
			if noAnswer {
				matches = append(matches, true)
				break
			}
			var targetVal = strings.Trim(xcast.ToString(condition.Rhs.Value), "'")
			for _, option := range selectedOptions {
				if option.Option != targetVal {
					matches = append(matches, true)
				} else {
					matches = append(matches, false)
				}
			}
		case "in": // 包含
			v := reflect.ValueOf(condition.Rhs.Value)
			var targetOptions = make(map[string]bool)
			if v.Kind() == reflect.Slice || v.Kind() == reflect.Array {
				for i := 0; i < v.Len(); i++ {
					targetOptions[strings.Trim(xcast.ToString(v.Index(i).Interface()), "'")] = true
				}
			} else {
				return false, fmt.Errorf("questionFilter not support type kind:%+v", v.Kind())
			}
			for _, option := range selectedOptions {
				exists := targetOptions[option.Option]
				if exists {
					matches = append(matches, true)
				} else {
					matches = append(matches, false)
				}
			}
		case "not_in": // 不包含
			// 未作答也肯定不匹配的
			if noAnswer {
				matches = append(matches, true)
				break
			}
			v := reflect.ValueOf(condition.Rhs.Value)
			var targetOptions = make(map[string]bool)
			if v.Kind() == reflect.Slice || v.Kind() == reflect.Array {
				for i := 0; i < v.Len(); i++ {
					targetOptions[strings.Trim(xcast.ToString(v.Index(i).Interface()), "'")] = true
				}
			} else {
				return false, fmt.Errorf("questionFilter not support type kind:%+v", v.Kind())
			}
			for _, option := range selectedOptions {
				exists := targetOptions[option.Option]
				if exists {
					matches = append(matches, false)
				} else {
					matches = append(matches, true)
				}
			}
		case "is_empty": // 为空(无值)
			if len(selectedOptions) == 0 {
				matches = append(matches, true)
			} else {
				matches = append(matches, false)
			}
		case "not_empty": // 不为空(有值)
			if len(selectedOptions) > 0 {
				matches = append(matches, true)
			} else {
				matches = append(matches, false)
			}
		case logicAnd, logicOr: // 逻辑运算符
			filter, err := questionFilter(condition, userRecordDetail)
			if err != nil {
				return false, fmt.Errorf("questionFilter logic err:%+v", err)
			}
			if !filter {
				matches = append(matches, true)
			} else {
				matches = append(matches, false)
			}
		default:
			return false, fmt.Errorf("questionFilter not support math operator:%+v", condition.Operator)
		}
	}

	// 且
	if operator == "&&" {
		// 所有条件都不匹配，过滤
		if len(matches) == 0 {
			return true, nil
		}
		// 只要有一个不匹配就过滤
		for _, match := range matches {
			if !match {
				return true, nil
			}
		}
	} else if operator == "||" { // 或
		// 只要有一个匹配就不过滤
		var hasMatch bool
		for _, match := range matches {
			if match {
				hasMatch = true
				break
			}
		}
		if !hasMatch {
			return true, nil
		}
	}

	return false, nil
}
