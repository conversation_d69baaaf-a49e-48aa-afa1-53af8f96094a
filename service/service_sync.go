package service

import (
	"context"
	"survey/config"
	"survey/model"
	"survey/proto"
	"survey/util/base"
	"survey/util/errors"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/pkg/http"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func SyncSurvey(ctx context.Context, clientId string, surveyId int64, materialVersion string) error {
	gLog := xlog.FromContext(ctx)
	surveyImpUrl := config.Get().SurveyImpUrl
	if surveyImpUrl == "" {
		gLog.Error("SurveyImpUrl is empty")
		return ecode.Error(ecode.BadRequest, "当前环境不支持同步问卷")
	}

	Survey, err := FetchSurveyDetails(ctx, surveyId)
	if err != nil {
		gLog.Error("FetchSurveyDetails with error", xlog.Err(err))
		return ecode.ServerError
	}

	body := make(map[string]interface{})
	body["clientid"] = Survey.ClientId
	body["name"] = Survey.Name
	body["creator"] = Survey.Creator
	if Survey.Stime != nil {
		body["stime"] = Survey.Stime.Format(time.DateTime)
	}

	if Survey.Etime != nil {
		body["etime"] = Survey.Etime.Format(time.DateTime)

	}

	schema, err := base.UnCompress(Survey.Schema)
	if err != nil {
		gLog.Error("base.UnCompress faild", xlog.Err(err))
		return err
	}
	schemaData := make(map[string]interface{})
	err = sonic.UnmarshalString(schema, &schemaData)
	if err != nil {
		gLog.Error("error unmarshal schema", xlog.Err(err))
		return err
	}
	body["schema"] = schemaData

	previewSchema, err := base.UnCompress(Survey.PreviewSchema)
	if err != nil {
		gLog.Error("error base.UnCompress previewSchema", xlog.Err(err))
		return err
	}
	previewSchemaData := make(map[string]interface{})
	if previewSchema != "" {
		err = sonic.UnmarshalString(previewSchema, &previewSchemaData)
		if err != nil {
			gLog.Error("error unmarshal previewSchemaData", xlog.Err(err))
			return err
		}
		body["previewSchema"] = previewSchemaData
	}
	settingsData := make(map[string]interface{})
	err = sonic.UnmarshalString(Survey.Settings, &settingsData)
	if err != nil {
		gLog.Error("error unmarshal settingsData", xlog.Err(err))
		return err
	}

	materialsConfig := settingsData["materialsConfig"]
	if materialsConfig != nil {
		materialsConfig.(map[string]interface{})["materialVersion"] = materialVersion
	}
	settingsData["materialsConfig"] = materialsConfig
	body["settings"] = settingsData

	webSettingsData := make(map[string]interface{})
	if Survey.WebSettings != "" {
		err = sonic.UnmarshalString(Survey.WebSettings, &webSettingsData)
		if err != nil {
			gLog.Error("error unmarshal WebSettings", xlog.Err(err))
			return err
		}
		body["webSettings"] = webSettingsData
	}

	//body["languages"] = Survey.Languages
	languagesData := make(map[string]interface{})
	err = sonic.UnmarshalString(Survey.Languages, &languagesData)
	if err != nil {
		gLog.Error("error unmarshal Languages", xlog.Err(err))
		return err
	}
	body["languages"] = languagesData
	body["apiVersion"] = Survey.ApiVersion
	body["keyValue"] = Survey.KeyValue
	body["font"] = Survey.Font

	req, err := http.NewRequest(ctx)
	if err != nil {
		gLog.Error("error creating request", xlog.Err(err))
		return ecode.ServerError
	}
	req.Header.Set("Content-Type", "application/json")
	bodyJson, _ := sonic.MarshalString(body)
	req.SetBody(bodyJson)
	resp, err := req.Post(surveyImpUrl)
	if err != nil {
		gLog.Error("error sending request", xlog.Err(err))
		return ecode.ServerError
	}

	gLog.Info("sync survey response", xlog.String("url", surveyImpUrl),
		xlog.String("resp", resp.Status()), xlog.String("body", bodyJson))
	var syncResponse proto.SyncSurveyResponse
	if err := sonic.Unmarshal(resp.Body(), &syncResponse); err != nil {
		gLog.Error("error decoding response", xlog.Err(err))
		return ecode.ServerError
	}

	if !resp.IsSuccess() && !syncResponse.Success {
		gLog.Error("error syncing survey", xlog.String("resp", resp.Status()))
		return ecode.ServerError
	}

	if syncResponse.Code != 0 && syncResponse.Info != "" {
		gLog.Error("error syncing survey2", xlog.Uint32("code", syncResponse.Code), xlog.String("info", syncResponse.Info))
		return ecode.ServerError
	}

	return nil
}

func FetchSurveyDetails(ctx context.Context, surveyId int64) (*model.CmsSurvey, error) {
	result, err := model.GetSurveyById(ctx, surveyId)
	if err != nil {
		xlog.FromContext(ctx).Error("GetSurveybyId with error", xlog.Err(err))
		return &model.CmsSurvey{}, err
	}
	return result, nil
}

func ImpSurvey(ctx context.Context, req *proto.ImpSurveyRequest) error {
	gLog := xlog.FromContext(ctx)

	var survey model.CmsSurvey

	survey.ClientId = req.Clientid
	survey.Name = req.Name
	if req.Stime != "" {
		stime, _ := time.Parse(time.DateTime, req.Stime)
		survey.Stime = &stime
	}
	if req.Etime != "" {
		etime, _ := time.Parse(time.DateTime, req.Etime)
		survey.Etime = &etime
	}
	if req.Schema != nil {
		schema, err := sonic.MarshalString(req.Schema)
		if err != nil {
			gLog.Error("error marshal schema", xlog.Err(err))
			return err
		}
		survey.Schema, _ = base.Compress(schema)
	}
	if req.PreviewSchema != nil {
		previewSchema, err := sonic.MarshalString(req.PreviewSchema)
		if err != nil {
			gLog.Error("error marshal schema", xlog.Err(err))
			return err
		}
		survey.PreviewSchema, _ = base.Compress(previewSchema)

	}
	if req.Settings != nil {
		settings, err := sonic.MarshalString(req.Settings)
		if err != nil {
			gLog.Error("error marshal schema", xlog.Err(err))
			return err
		}
		survey.Settings = settings
	}
	if req.WebSettings != nil {
		webSettings, err := sonic.MarshalString(req.WebSettings)
		if err != nil {
			gLog.Error("error marshal schema", xlog.Err(err))
			return err
		}
		survey.WebSettings = webSettings
	}

	if req.GetLanguages() != nil {
		languages, err := sonic.MarshalString(req.GetLanguages())
		if err != nil {
			gLog.Error("error marshal schema", xlog.Err(err))
			return err
		}
		survey.Languages = languages
	}

	survey.ApiVersion = req.ApiVersion
	survey.KeyValue = req.KeyValue
	survey.Font = req.Font
	survey.Creator = req.Creator
	_, err := model.InsertSurvey(ctx, &survey)
	if err != nil {
		gLog.Error("ImpSurvey with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}
