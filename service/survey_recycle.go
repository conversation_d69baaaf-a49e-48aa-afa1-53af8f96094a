package service

import (
	"context"
	"encoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gorm.io/gorm"
	"strconv"
	"survey/helpers"
	"survey/model"
	"survey/proto"
	"survey/util/base"
	"survey/util/errors"
	"time"
)

func SurveyRecycleList(ctx context.Context, request *proto.SurveyListRequest) (*proto.SurveyListResponse, error) {
	var data proto.SurveyListResponse
	gLog := xlog.FromContext(ctx)

	result, count, err := model.SurveyRecycleListData(ctx, strconv.FormatInt(request.ClientId, 10), request.Id, request.Name, request.SortCtime, request.PageSize, request.Page)
	if err != nil {
		gLog.Error("SurveyRecycleListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	var surveyList []*proto.Survey
	for _, r := range result {
		if survey := ModelTtoProto(ctx, r); survey != nil {
			surveyList = append(surveyList, survey)
		}
	}

	data.List = surveyList
	data.Total = count

	return &data, nil
}

func DeleteSurveyRecycle(ctx context.Context, db *gorm.DB, request *proto.SurveyDelRequest) error {
	err := model.DeleteSurveyRecycleData(ctx, db, request)
	if err != nil {
		xlog.Error("DeleteSurveyRecycleData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func RecoverSurveyRecycle(ctx context.Context, clientId string, list []int64) error {
	err := model.RecoverSurveyRecycleData(ctx, clientId, list)
	if err != nil {
		xlog.Error("DeleteSurveyRecycleData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func ClearAllSurveyRecycle(ctx context.Context, db *gorm.DB, request *proto.ClearAllSurveyRecycleRequest) error {
	err := model.ClearAllSurveyRecycleData(ctx, db, request)
	if err != nil {
		xlog.Error("ClearAllSurveyRecycleData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func RecoverAllSurveyRecycle(ctx context.Context, db *gorm.DB, request *proto.Survey) error {
	err := model.RecoverAllSurveyRecycleData(ctx, db, request)
	if err != nil {
		xlog.Error("RecoverAllSurveyRecycle with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func ModelTtoProto(ctx context.Context, r *model.CmsSurvey) *proto.Survey {
	setting := &proto.Setting{}
	if err := json.Unmarshal([]byte(r.Settings), setting); err != nil {
		xlog.FromContext(ctx).Error("json.Unmarshal with error", xlog.Err(err))
		return nil
	}

	schema, err := base.UnCompress(r.Schema)
	if err != nil {
		xlog.FromContext(ctx).Error("UnCompress with error", xlog.Err(err))
		schema = ""
	}
	previewSchema, err := base.UnCompress(r.PreviewSchema)
	if err != nil {
		xlog.FromContext(ctx).Error("UnCompress with error", xlog.Err(err))
		previewSchema = ""
	}
	var survey proto.Survey
	survey.Id = r.ID
	ClientId, err := strconv.ParseInt(r.ClientId, 10, 64)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyListData with error", xlog.Err(err))
		return nil
	}
	survey.ClientId = ClientId
	survey.Name = r.Name
	survey.IsClosed = r.IsClosed
	survey.IsPause = r.IsPause
	survey.IsPublish = r.IsPublish
	survey.IsModifyUnpublish = r.IsModifyUnpublish
	survey.IsOpened = r.IsOpened
	if r.Stime == nil {
		survey.Stime = "-"
	} else {
		survey.Stime = r.Stime.Format(time.DateTime)
	}
	if r.Etime == nil {
		survey.Etime = "-"
	} else {
		survey.Etime = r.Etime.Format(time.DateTime)
	}
	survey.Type = r.Type
	survey.Schema = schema
	survey.PreviewSchema = previewSchema
	survey.Settings = r.Settings
	survey.WebSettings = r.WebSettings
	survey.Languages = r.Languages
	survey.HashCode = r.HashCode
	survey.IsDelete = r.IsDelete
	if r.Deltime.IsZero() {
		survey.Deltime = "-"
	} else {
		survey.Deltime = r.Deltime.Format(time.DateTime)
	}
	survey.KeyValue = r.KeyValue
	survey.Font = r.Font
	survey.Remark = r.Remark
	survey.Ctime = r.Ctime.Format(time.DateTime)
	if r.Mtime.IsZero() {
		survey.Mtime = "-"
	} else {
		survey.Mtime = r.Mtime.Format(time.DateTime)
	}
	survey.Creator = r.Creator
	survey.Editor = r.Editor
	//survey.IsTimeLimit = setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit
	survey.Status = int32(model.GetSurveyStatus(r))
	survey.FullValidUid, _ = model.GetFullValidSurveyRecordIdCount(ctx, r.ID)

	if setting.BaseRuleConfig != nil && setting.BaseRuleConfig.TimeLimitConfig != nil && setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit {
		survey.IsTimeLimit = true
	} else {
		survey.IsTimeLimit = false
	}
	var deliverList []string
	deliverList = setting.BaseRuleConfig.DeliverList
	WebPathList := helpers.GenSurveyWebPath(r.HashCode, deliverList, r.ClientId)
	survey.WebPathList = WebPathList
	return &survey
}
