package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"survey/config"
	"survey/helpers"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util/base"
	"time"

	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"gorm.io/gorm"
)

func SurveyStatistics(ctx context.Context, clientId string, surveyId int64) (*proto.SurveyStatisticsResponse, error) {
	gLog := xlog.FromContext(ctx)
	result, err := model.GetSurveySettingsById(ctx, clientId, surveyId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		gLog.Error("show survey with error", xlog.Err(err))
		return nil, err
	} else if (err != nil && errors.Is(err, gorm.ErrRecordNotFound)) || result == nil {
		return nil, ecode.Error(ecode.BadRequest, "查询的数据不存在或查询数据已加入回收站")
	}

	return &proto.SurveyStatisticsResponse{
		Name:     result.Name,
		Settings: result.Settings,
		Stime:    result.Stime.Format(time.DateTime),
		Etime:    result.Etime.Format(time.DateTime),
	}, nil
}

// 获取问卷详情
func GetSurveyDetail(ctx context.Context, surveyId int64, clientid string) (*proto.SurveyDetailResponse, error) {
	gLog := xlog.FromContext(ctx)
	result, err := model.SurveyPreviewData(ctx, surveyId, clientid)
	if err != nil {
		gLog.Error("show survey with error", xlog.Err(err))
		return nil, err
	}
	if result == nil {
		return nil, ecode.Error(ecode.BadRequest, "查询的数据不存在或查询数据已加入回收站")
	}

	setting := &proto.Setting{}
	if err = sonic.Unmarshal([]byte(result.Settings), &setting); err != nil {
		gLog.Error("json.Unmarshal with error", xlog.Err(err))
	}

	SurveyConfig := &proto.SurveyConfig{}
	if setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit {
		SurveyConfig.IsTimeLimit = true
	} else {
		SurveyConfig.IsTimeLimit = false
	}

	schema, err := base.UnCompress(result.Schema)
	if err != nil {
		gLog.Error("UnCompress with error", xlog.Err(err))
		schema = ""
	}
	previewSchema, err := base.UnCompress(result.PreviewSchema)
	if err != nil {
		gLog.Error("UnCompress with error", xlog.Err(err))
		previewSchema = ""
	}
	setting = &proto.Setting{}
	if err = json.Unmarshal([]byte(result.Settings), setting); err != nil {
		gLog.Error("json.Unmarshal with error", xlog.Err(err))

	}
	SurveyConfig.Id = result.ID
	ClientId, err := strconv.ParseInt(result.ClientId, 10, 64)
	if err != nil {
		xlog.Error("SurveyListData with error", xlog.Err(err))
		return nil, err
	}
	SurveyConfig.ClientId = ClientId
	SurveyConfig.Name = result.Name
	SurveyConfig.IsClosed = result.IsClosed
	SurveyConfig.IsPause = result.IsPause
	SurveyConfig.IsPublish = result.IsPublish
	SurveyConfig.IsModifyUnpublish = result.IsModifyUnpublish
	SurveyConfig.IsOpened = result.IsOpened
	if result.Stime.IsZero() {
		SurveyConfig.Stime = ""
	} else {
		SurveyConfig.Stime = result.Stime.String()
	}
	if result.Etime.IsZero() {
		SurveyConfig.Etime = ""
	} else {
		SurveyConfig.Etime = result.Etime.String()
	}
	SurveyConfig.Type = result.Type
	SurveyConfig.Schema = schema
	SurveyConfig.PreviewSchema = previewSchema
	SurveyConfig.Settings = result.Settings
	SurveyConfig.WebSettings = result.WebSettings
	SurveyConfig.Languages = result.Languages
	SurveyConfig.HashCode = result.HashCode
	SurveyConfig.IsDelete = result.IsDelete
	if result.Deltime.IsZero() {
		SurveyConfig.Deltime = ""
	} else {
		SurveyConfig.Deltime = result.Deltime.String()
	}
	SurveyConfig.Remark = result.Remark

	// 格式化时间为年月日时分秒的格式
	SurveyConfig.Ctime = result.Ctime.String()
	SurveyConfig.Mtime = result.Mtime.String()
	SurveyConfig.Creator = result.Creator
	SurveyConfig.Editor = result.Editor

	outerQuestionStatisticsData := &proto.OuterQuestionStatisticsData{}

	var s types.Schema

	if err = json.Unmarshal([]byte(schema), &s); err != nil {
		gLog.Error("json.Unmarshal with error", xlog.Err(err))
	}

	//flattedQuestions := helpers.FlattenComponentsTree(&s.ComponentsTree[0])
	//recordList, err := model.GetSurveyRecordListWithoutPages(ctx, result.HashCode)
	//if err != nil {
	//	gLog.Error("GetSurveyRecordListWithoutPages with error", xlog.Err(err))
	//	SurveyConfig.QuestionStatisticsData.ValidAnswerTotals = 0
	//}
	//for _, record := range recordList {
	//
	//	recordDetails, err := model.GetSurveyRecordDetailsByRecordID(ctx, record.ID, result.HashCode, map[string]interface{}{"is_valid": 1})
	//
	//	if err != nil {
	//		gLog.Error("GetSurveyRecordDetailsByRecordID with error", xlog.Err(err))
	//		SurveyConfig.QuestionStatisticsData.ValidAnswerTotals = 0
	//	}
	//	for _, recordDetail := range recordDetails {
	//
	//		var optionDetail []*proto.OptionDetail
	//
	//	}
	//
	//}
	var questionList []*proto.QuestionList
	//for _, question := range flattedQuestions {
	//	var QuestionnList *proto.QuestionList

	//questionBaseConfig, err := json.Marshal(question.Props.QuestionBaseConfig)
	//if err != nil {
	//	gLog.Error("json.Marshal with error", xlog.Err(err))
	//}

	// todo: 这里需要优化 每个题型都要解析一遍 需要优化一下
	//QuestionnList = &proto.QuestionList{
	//	ComponentName:           question.ComponentName,
	//	ComponentTitle:          question.Title,
	//	QuestionBaseConfig:      questionBaseConfig,
	//	ConfigProps:             question.Props.ConfigProps,
	//	QuestionLogicalConfig:   question.Props.QuestionLogicalConfig,
	//	QuestionComponentConfig: question.Props.QuestionComponentConfig,
	//	QuestionSelectConfig:    question.Props.QuestionSelectConfig,
	//	RequestConfig:           question.Props.RequestConfig,
	//	WrapperIndex:            question.Props.WrapperIndex,
	//}
	//

	//questionList = append(questionList, QuestionnList)
	//}

	counts, err := model.GetFullValidSurveyRecordIdCount(ctx, result.ID)
	if err != nil {
		gLog.Error("json.Unmarshal with error", xlog.Err(err))
		SurveyConfig.QuestionStatisticsData.ValidAnswerTotal = 0
	}
	questionStatisticsData := &proto.QuestionStatisticsData{
		ValidAnswerTotal: counts,
		ValidUserTotal:   0,
	}
	SurveyConfig.QuestionStatisticsData = questionStatisticsData
	return &proto.SurveyDetailResponse{
		QuestionList:           questionList,
		SurveyConfig:           SurveyConfig,
		QuestionStatisticsData: outerQuestionStatisticsData,
	}, nil
}

func SurveyStatisticsDetail(ctx context.Context, surveyId int64, clientId string) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)

	// 发送POST请求
	baseUrl := config.Get().NodeCfg.Host + config.Get().NodeCfg.StatisticsDetailUrl

	url := baseUrl + clientId + "/" + strconv.FormatInt(surveyId, 10)
	// 创建POST请求
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		gLog.Error("create request error", xlog.Err(err))
		return nil, err
	}

	gLog.Info("request url", zap.String("url", url))

	// 设置请求头信息，如内容类型为JSON
	req.Header.Set("Content-Type", "application/json")
	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		gLog.Error("send http client error", xlog.Err(err))
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		gLog.Error("ioutil ReadAll error", xlog.Err(err))
		return nil, err
	}

	gLog.Info("response body", xlog.String("body", string(body)), xlog.Int("http code", resp.StatusCode))

	response := map[string]interface{}{}

	err = json.Unmarshal(body, &response)
	if err != nil {
		gLog.Error("json Unmarshal error", xlog.Err(err))
		return nil, err
	}

	result, err := json.Marshal(response["data"])
	if err != nil {
		gLog.Error("json Marshal error", xlog.Err(err))
		return nil, err
	}

	// 输出data字段的值
	return xtype.NewRawMessage(result), nil
}

func SurveyStatisticsDetail2(ctx context.Context, surveyId int64, clientId string) (*xtype.RawMessage, error) {
	type Result struct {
		SurveyConfig           *types.SurveyConfigNew        `json:"surveyConfig"`
		QuestionList           []*types.QuestionList         `json:"questionList"`
		QuestionStatisticsData *types.QuestionStatisticsData `json:"questionStatisticsData"`
	}

	var result = &Result{
		SurveyConfig:           &types.SurveyConfigNew{},
		QuestionList:           make([]*types.QuestionList, 0),
		QuestionStatisticsData: &types.QuestionStatisticsData{},
	}

	surveyConfig, err := model.SurveyPreviewData(ctx, surveyId, clientId)
	if err != nil {
		return nil, err
	}

	// 获取配置
	surveyConfigNew, err := GenDetailSurveyConfig(ctx, surveyConfig)
	if err != nil {
		return nil, err
	}
	result.SurveyConfig = surveyConfigNew

	// 获取问题列表
	questionList, err := GenDetailSurveyQuestionList(ctx, surveyConfig)
	if err != nil {
		return nil, err
	}

	for i := range questionList {
		result.QuestionList = append(result.QuestionList, &questionList[i])
	}

	// 统计数据字段返回
	result.QuestionStatisticsData, err = GenDetailQuestionStatisticsData(ctx, result.QuestionList, surveyId)
	if err != nil {
		return nil, err
	}

	if result.QuestionStatisticsData != nil {
		result.QuestionStatisticsData.ValidAnswerTotals = int(surveyConfigNew.QuestionStatisticsData.ValidAnswerTotal)
	}

	bs, err := sonic.Marshal(result)
	if err != nil {
		err = fmt.Errorf("SurveyStatisticsDetail2 sonic.Marshal(result) with err:%+v surveyID%+v clientID:%v", err, surveyId, clientId)
		return nil, err
	}

	// 输出data字段的值
	return xtype.NewRawMessage(bs), nil
}

func GenDetailSurveyConfig(ctx context.Context, r *model.CmsSurvey) (*types.SurveyConfigNew, error) {
	gLog := xlog.FromContext(ctx)

	setting := &proto.Setting{}
	if err := json.Unmarshal([]byte(r.Settings), setting); err != nil {
		err = fmt.Errorf("GenDetailSurveyConfigFromModel json.Unmarshal setting with error:%+v", err)
		return nil, err
	}

	var resultSettings interface{}
	err := sonic.UnmarshalString(r.Settings, &resultSettings)
	if err != nil {
		err = fmt.Errorf("GenDetailSurveyConfigFromModel sonic.UnmarshalString(r.Settings, &resultSettings) setting with error:%+v surveyID:%v", err, r.ID)
		return nil, err
	}

	var languages interface{}
	if r.Languages != "" {
		sonic.UnmarshalString(r.Languages, &languages)
		// if err != nil {
		// 	return nil, err
		// }
	} else {
		languages = types.Empty{}
	}

	schema, err := base.UnCompress(r.Schema)
	if err != nil {
		gLog.Debug("GenDetailSurveyConfigFromModel base.UnCompress(r.Schema) with error", xlog.Err(err))
		schema = ""
	}

	var resultSchema interface{}
	if schema != "" {
		err := sonic.UnmarshalString(schema, &resultSchema)
		if err != nil {
			gLog.Debug("GenDetailSurveyConfigFromModel sonic.UnmarshalString(schema, &resultSchema) with error", xlog.Err(err), xlog.Int64("id", r.ID))
		}
	} else {
		resultSchema = ""
	}

	previewSchema, err := base.UnCompress(r.PreviewSchema)
	if err != nil {
		err = fmt.Errorf("GenDetailSurveyConfigFromModel base.UnCompress(r.PreviewSchema) err:%+v id:%+v", err, r.ID)
		gLog.Debug("", xlog.Err(err))
		previewSchema = ""
	}

	//getDetail nodeJs 这里不处理就是null,,,
	var startTime string
	if r.Stime == nil {
		startTime = "-"
	} else {
		startTime = r.Stime.Format(time.DateTime)
	}

	var etime string
	if r.Etime == nil {
		etime = "-"
	} else {
		etime = r.Etime.Format(time.DateTime)
	}

	var delTime string
	if r.Deltime.IsZero() {
		delTime = "-"
	} else {
		delTime = r.Deltime.Format(time.DateTime)
	}

	var mtime string
	if r.Mtime.IsZero() {
		mtime = "-"
	} else {
		mtime = r.Mtime.Format(time.DateTime)
	}

	//survey.IsTimeLimit = setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit
	//fullValidUid, _ := model.GetFullValidSurveyRecordIdCount(ctx, r.ID)

	var isTimeLimit bool
	if setting.BaseRuleConfig != nil && setting.BaseRuleConfig.TimeLimitConfig != nil && setting.BaseRuleConfig.TimeLimitConfig.IsTimeLimit {
		isTimeLimit = true
	}

	webPathList := helpers.GenSurveyWebPath(r.HashCode, setting.BaseRuleConfig.DeliverList, r.ClientId)

	// 问卷统计
	var questionStatisticsData proto.QuestionStatisticsData

	if config.Get().StatisticUseMysqlData {
		questionStatisticsData.ValidAnswerTotal, err = model.GetFullValidSurveyRecordIdCount(ctx, r.ID)
		if err != nil {
			return nil, err
		}
	} else {
		n, err := model.GetRecordCountNew(ctx, r.HashCode, 0, nil, nil, "")
		if err != nil {
			return nil, err
		}
		questionStatisticsData.ValidAnswerTotal = int64(n)
	}

	result := &types.SurveyConfigNew{
		ID:                     r.ID,
		ClientID:               r.ClientId,
		Name:                   r.Name,
		IsClosed:               r.IsClosed,
		IsPause:                r.IsPause,
		IsModifyUnpublish:      r.IsModifyUnpublish,
		IsOpened:               r.IsOpened,
		STime:                  startTime,
		ETime:                  etime,
		Type:                   r.Type,
		Schema:                 resultSchema,
		Settings:               resultSettings,
		WebSettings:            r.WebSettings,
		Languages:              languages,
		HashCode:               r.HashCode,
		IsDelete:               r.IsDelete,
		Deltime:                delTime,
		Remark:                 r.Remark,
		Ctime:                  r.Ctime.Format(time.DateTime),
		Mtime:                  mtime,
		Creator:                r.Creator,
		Editor:                 r.Editor,
		IsTimeLimit:            isTimeLimit,
		WebPathList:            webPathList,
		Status:                 int32(model.GetSurveyStatus(r)),
		Font:                   r.Font,
		KeyValue:               r.KeyValue,
		PreviewSchema:          previewSchema,
		ApiVersion:             r.ApiVersion,
		QuestionStatisticsData: &questionStatisticsData,
	}

	return result, nil
}

func GenDetailSurveyQuestionList(ctx context.Context, r *model.CmsSurvey) ([]types.QuestionList, error) {
	//gLog := xlog.FromContext(ctx)

	result := make([]types.QuestionList, 0)

	schema, err := base.UnCompress(r.Schema)
	if err != nil {
		err = fmt.Errorf("GenDetailSurveyQuestionList base.UnCompress(r.Schema) with error:%+v", err)
		return nil, err
	}

	var schemaObj = new(types.Schema)
	err = sonic.UnmarshalString(schema, &schemaObj)
	if err != nil {
		err = fmt.Errorf("GenDetailSurveyQuestionList sonic.UnmarshalString with err:%+v r.Schema%s", err, schema)
		return nil, err
	}

	var surveyConfigSchema types.ComponentsTree
	for _, componentsTree := range schemaObj.ComponentsTree {
		if componentsTree.FileName == "index" {
			surveyConfigSchema = componentsTree
			break
		}
	}

	tempQuestionTreeList := flattenNode(ctx, surveyConfigSchema)

	var index int
	for _, tempQuestionTree := range tempQuestionTreeList {
		if !tempQuestionTree.Props.ConfigProps.IsQuestion {
			continue
		}
		index++
		type RelationSelectOptions struct {
			*types.RelationSelectOptionsConfigItem
			Label          string             `json:"label"`
			Value          string             `json:"value"`
			RelationOption types.SelectOption `json:"-"`
		}

		// 处理引用逻辑部分配置，把引用的配置拿出来并插入到当前题目选项配置内
		//var relationSelectOptions []*RelationSelectOptions
		var relationSelectOptions []types.SelectOption
		if len(tempQuestionTree.Props.QuestionLogicalConfig.OptionsReference.RelationSelectOptionsConfig) > 0 {
			if tempQuestionTree.Props.ConfigProps.IsMatrix {
				relationSelectOptionsConfig := tempQuestionTree.Props.QuestionLogicalConfig.OptionsReference.RelationSelectOptionsConfig
				for _, v := range relationSelectOptionsConfig {
					v.Value = v.NewValue
					relationSelectOptions = append(relationSelectOptions, v)
					// 原NodeJS
					//for _, option := range tempQuestionTree.Props.QuestionSelectConfig.SelectOptions {
					//	if option.Value == v.NewValue {
					//		relationSelectOptions = append(relationSelectOptions, &RelationSelectOptions{
					//			Label:          GetLabelStr(v.Label),
					//			Value:          v.NewValue,
					//			RelationOption: option,
					//		})
					//		break
					//	}
					//}
				}

				// 拼接选项，引用 + 自定义
				var selectOptions = getLastSelectOptions(tempQuestionTree.Props.QuestionComponentConfig.RowTitleSelectOptions, relationSelectOptions)
				tempQuestionTree.Props.QuestionComponentConfig.RowTitleSelectOptions = selectOptions
			} else {
				relationSelectOptionsConfig := tempQuestionTree.Props.QuestionLogicalConfig.OptionsReference.RelationSelectOptionsConfig
				for _, v := range relationSelectOptionsConfig {
					v.Value = v.NewValue
					relationSelectOptions = append(relationSelectOptions, v)
				}

				// 拼接选项，自定义 + 引用
				var selectOptions = getLastSelectOptions(tempQuestionTree.Props.QuestionSelectConfig.SelectOptions, relationSelectOptions)
				tempQuestionTree.Props.QuestionSelectConfig.SelectOptions = selectOptions
				// 原NodeJS
				//var newSelectOptions []types.SelectOption
				//for _, option := range relationSelectOptions {
				//	newSelectOptions = append(newSelectOptions, types.SelectOption{
				//		Value: option.Value,
				//		Label: option.Label,
				//	})
				//}
				//tempQuestionTree.Props.QuestionComponentConfig.SelectOptions = newSelectOptions
			}
		}

		// 2023.8.28，老版本问卷有自由选项功能， 问卷统计中做相关兼容
		if len(tempQuestionTree.Props.QuestionLogicalConfig.OptionsReference.RelationSelectOptionsConfig) < 0 &&
			tempQuestionTree.Props.ConfigProps.HasFillFree &&
			tempQuestionTree.Props.QuestionSelectConfig.FillFreeConfig.Show {
			tempQuestionTree.Props.QuestionSelectConfig.SelectOptions = append(tempQuestionTree.Props.QuestionSelectConfig.SelectOptions, types.SelectOption{
				Value:     tempQuestionTree.Props.QuestionSelectConfig.FillFreeConfig.Value,
				Label:     tempQuestionTree.Props.QuestionSelectConfig.FillFreeConfig.Label,
				BlankFill: types.BlankFill{Allow: true},
			})
		}

		// 把评分当作选择类题目去处理
		if tempQuestionTree.Props.ConfigProps.IsRate {
			var (
				allowHalf = tempQuestionTree.Props.QuestionComponentConfig.AllowHalf
				count     = tempQuestionTree.Props.QuestionComponentConfig.Count
				num       int
				span      float64
			)

			if allowHalf {
				num = count * 2
				span = 0.5
			} else {
				num = count
				span = 1
			}

			var arr = helpers.CreateSpecialLengthArray(num)

			var selectOptions []types.SelectOption
			for idx, _ := range arr {
				selectOptions = append(selectOptions, types.SelectOption{
					Value: fmt.Sprintf("%v", span*(float64(idx)+1)),
					Label: xcast.ToString(span*float64(idx) + 1),
				})
			}
			tempQuestionTree.Props.QuestionSelectConfig.SelectOptions = selectOptions
		}

		statisticsMethod := getQuestionStatisticsMethod(tempQuestionTree.Props.ConfigProps)

		// 此参数用于前端向后端获取输入类题目时传递给后端的固定参数，下面自定义选项的参数作用雷同
		var requestConfig types.RequestConfig
		if statisticsMethod == "input" {
			requestConfig = types.RequestConfig{
				QuestionUniqueKey: tempQuestionTree.Props.ConfigProps.UniqueKey,
				ConfigProps:       tempQuestionTree.Props.ConfigProps,
			}
		}

		tempQuestionTree.Props.ConfigProps.QuestionId = index
		tempQuestionTree.Props.ConfigProps.StatisticsMethod = statisticsMethod

		result = append(result, types.QuestionList{
			ComponentName:           tempQuestionTree.ComponentName,
			ComponentTitle:          helpers.GetComponentTitleByComponentName(tempQuestionTree.ComponentName),
			QuestionBaseConfig:      tempQuestionTree.Props.QuestionBaseConfig,
			ConfigProps:             tempQuestionTree.Props.ConfigProps,
			QuestionLogicalConfig:   tempQuestionTree.Props.QuestionLogicalConfig,
			QuestionComponentConfig: tempQuestionTree.Props.QuestionComponentConfig,
			QuestionSelectConfig:    tempQuestionTree.Props.QuestionSelectConfig,
			RequestConfig:           requestConfig,
			WrapperIndex:            tempQuestionTree.Parent.Index,
		})

	}

	return result, nil
}

func GenDetailQuestionStatisticsData(ctx context.Context, questionList []*types.QuestionList, surveyId int64) (*types.QuestionStatisticsData, error) {
	gLog := xlog.FromContext(ctx)
	result := new(types.QuestionStatisticsData)
	fullValidOrDeleteSurveyRecordId, err := model.GetFullValidOrDeleteSurveyRecordId(ctx, surveyId)
	if err != nil {
		return nil, err
	}
	var (
		surveyDetailStatisticData []*types.SurveyDetailStatisticModel
		excludeRecordIds          []string
	)

	var queryStartTime = time.Now()
	surveyHashCode, _ := base.SurveyIdToHashCode(surveyId)
	var kind string
	if config.Get().StatisticUseMysqlData { // 使用mysql数据源
		kind = "mysql"
		surveyDetailStatisticData, err = model.GetSurveyDetailStatisticData(ctx, surveyId, fullValidOrDeleteSurveyRecordId)
		if err != nil {
			return nil, err
		}
	} else { // 使用clickhouse
		kind = "clickhouse"
		for _, id := range fullValidOrDeleteSurveyRecordId {
			excludeRecordIds = append(excludeRecordIds, cast.ToString(id))
		}
		surveyDetailStatisticData, err = model.GetSurveyStat(ctx, surveyHashCode, excludeRecordIds)
		if err != nil {
			return nil, err
		}
	}

	var detailRecordGroupCount map[string]int64
	if config.Get().StatisticUseMysqlData {
		detailRecordGroupCount, err = model.GetSurveyDetailRecordGroupCount(ctx, surveyId, fullValidOrDeleteSurveyRecordId)
		if err != nil {
			return nil, err
		}
	} else { // 使用clickhouse
		detailRecordGroupCount, err = model.GetGroupCount(ctx, surveyHashCode, excludeRecordIds)
		if err != nil {
			return nil, err
		}
	}

	MetricsStatisticstMs.Observe(float64(time.Since(queryStartTime).Milliseconds()), kind)
	gLog.Debug("GenDetailQuestionStatisticsData queryStartTime log", xlog.Int64("queryStartTime", time.Since(queryStartTime).Milliseconds()))

	var sqlStatResult = make([]*types.SurveyDetailSqlStatisticResult, 0)
	for _, item := range surveyDetailStatisticData {
		questionStr := string(item.Question)
		itemData := &types.SurveyDetailSqlStatisticResult{
			ModelData:  item,
			Question:   questionStr,
			Option:     string(item.Option),
			Count:      item.OptionCount,
			GroupCount: detailRecordGroupCount[questionStr],
		}
		sqlStatResult = append(sqlStatResult, itemData)
	}
	var count uint64
	if config.Get().StatisticUseMysqlData {
		count, err = model.MysqlGetRecordFullValidUserCount(ctx, surveyHashCode)
		if err != nil {
			return nil, err
		}
	} else {
		count, err = model.GetRecordFullValidUserCount(ctx, surveyHashCode)
		if err != nil {
			return nil, err
		}
	}
	result.ValidUserTotals = int(count)
	result.Detail, err = questionStatisticIndex(ctx, questionList, sqlStatResult)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func flattenNode(ctx context.Context, root interface{}) []types.ComponentsTree {
	if root == nil {
		return []types.ComponentsTree{}
	}

	var flatten []types.ComponentsTree
	queue := make([]types.ComponentsTree, 0)

	switch v := root.(type) {
	case []types.ComponentsTree:
		queue = append(queue, v...)
	case types.ComponentsTree:
		queue = append(queue, v)
	default:
		xlog.FromContext(ctx).Debug("flattenNode not support type", xlog.Any("rawType", root))
	}

	for len(queue) > 0 {
		item := queue[0]
		queue = queue[1:]
		flatten = append(flatten, item)

		if item.Children != nil {
			children := item.Children
			for _, childItem := range children {
				if childItem.Index == nil {
					for _, child := range children {
						if childItem.DocID == child.DocID {
							childItem.Index = child.Index
						}
					}
				}
				// 这玩意没用到。。。nodeJS写法
				//if childItem.Parent == nil {
				//	childItem.Parent = &Node{
				//		Index: item.Index,
				//	}
				//}
				queue = append(queue, childItem)
			}
		}
	}

	return flatten
}

func getQuestionStatisticsMethod(configProps types.ConfigProps) string {

	if configProps.IsGauge {
		return "gauge"
	}

	if configProps.IsMatrix {
		return "matrix"
	}

	if configProps.IsOrder {
		return "order"
	}

	if configProps.QuestionType == "select" && !configProps.IsAddress {
		return "select"
	}

	return "input"
}

func questionStatisticIndex(ctx context.Context, questionList []*types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) (interface{}, error) {
	var statisticsDataList = make([]interface{}, 0)
	for _, questionItem := range questionList {
		var statisticsMethod = questionItem.ConfigProps.StatisticsMethod
		// 过滤input
		if statisticsMethod == "input" {
			continue
		}
		//nodejs 这里即可能是对象，也可能是数组， 还可能是nil
		var statisticsData interface{}
		switch statisticsMethod {
		case "gauge":
			statisticsData = getGaugeStatisticsMethodData(ctx, questionItem, sqlStatResult)
		case "matrix":
			statisticsData = getMatrixStatisticsMethodData(ctx, questionItem, sqlStatResult)
		case "order":
			statisticsData = getOrderStatisticsMethodData(ctx, questionItem, sqlStatResult)
		case "select":
			statisticsData = getSelectStatisticsMethodData(ctx, questionItem, sqlStatResult)
		default:
			xlog.Warn("该题型未规定统计方式，请联系开发添加!!!", xlog.String("statisticsMethod", statisticsMethod)) //From NodeJs
		}
		if statisticsData != nil {
			statisticsDataList = append(statisticsDataList, statisticsData)
		}
	}

	return statisticsDataList, nil
}

// 获取量表类型题目统计数据
func getGaugeStatisticsMethodData(ctx context.Context, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) interface{} {
	if questionItem.ConfigProps.IsMatrix {
		return getMatrixGaugeQuestionData(ctx, questionItem, sqlStatResult)
	} else {
		return getGeneralGaugeQuestionData(ctx, questionItem.ConfigProps.UniqueKey, questionItem, sqlStatResult)
	}
}

// 获取矩阵量表类型题目统计数据
func getMatrixGaugeQuestionData(ctx context.Context, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) []types.SurveyQuestionStatsWithExtra {
	var list = make([]types.SurveyQuestionStatsWithExtra, 0)
	for _, option := range questionItem.QuestionComponentConfig.RowTitleSelectOptions {
		list = append(list, getGeneralGaugeQuestionData(ctx, option.Value, questionItem, sqlStatResult))
	}
	return list
}

// 获取常规量表类题目统计数据
func getGeneralGaugeQuestionData(ctx context.Context, uniqueKey string, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) types.SurveyQuestionStatsWithExtra {
	var (
		startValue    = questionItem.QuestionComponentConfig.StartValue
		dimension     = questionItem.QuestionComponentConfig.Dimension
		sortType      = questionItem.QuestionComponentConfig.Sort
		scoreType     = questionItem.QuestionComponentConfig.ScoreType
		headerContent = questionItem.QuestionComponentConfig.HeaderContent
		footerContent = questionItem.QuestionComponentConfig.FooterContent
	)

	// 分制
	var with int
	switch scoreType {
	case 1:
		with = 5 // 五分制
	case 2:
		with = 10 // 十分制
	case 3:
		with = 7 // 七分制
	case 4:
		with = 11 // 十一分制
	default:
		xlog.FromContext(ctx).Warn("getGeneralGaugeQuestionData not support scoreType val", xlog.Int32("scoreType", scoreType))
	}

	var selectOptions = make([]types.SelectOption, 0)
	for i := 0; i < with; i++ {
		value := startValue + i*dimension
		selectOptions = append(selectOptions, types.SelectOption{Value: xcast.ToString(value)})
	}

	if sortType == 0 {
		selectOptions = helpers.ReverseSlice(selectOptions)
	}

	return getCommonStatisticsData(ctx, uniqueKey, selectOptions, sqlStatResult, &types.ExtraData{
		HeaderContent: headerContent,
		FooterContent: footerContent,
	})
}

func getCommonStatisticsData(ctx context.Context, uniqueKey string, selectOptions []types.SelectOption, sqlStatResult []*types.SurveyDetailSqlStatisticResult, extraData *types.ExtraData) types.SurveyQuestionStatsWithExtra {
	var currentQuestionStatResult []*types.SurveyDetailSqlStatisticResult
	for _, statItem := range sqlStatResult {
		if statItem.Question == uniqueKey {
			currentQuestionStatResult = append(currentQuestionStatResult, statItem)
		}
	}

	var count int64
	if len(currentQuestionStatResult) > 0 {
		count = currentQuestionStatResult[0].GroupCount
	}

	var counts uint64
	for _, countVal := range currentQuestionStatResult {
		counts += countVal.Count
	}

	var statDetail = make([]types.Detail, 0)

	for _, optionItem := range selectOptions {
		var findItem *types.SurveyDetailSqlStatisticResult
		for _, statResult := range currentQuestionStatResult {
			if statResult.Option == optionItem.Value {
				findItem = statResult
				break
			}
		}

		var count1 uint64
		if findItem != nil {
			count1 = findItem.Count
		}

		var proportion = helpers.GetProportion(count1, counts, 4)
		statDetail = append(statDetail, types.Detail{
			SelectOption: optionItem,
			Value:        optionItem.Value,
			Count:        int(count1),
			Proportion:   proportion,
		})
	}

	result := types.SurveyQuestionStatsWithExtra{
		ExtraData:   extraData,
		Count:       count,
		SelectCount: int(counts),
		Detail:      statDetail,
		UniqueKey:   uniqueKey,
	}

	return result
}

func getMatrixStatisticsMethodData(ctx context.Context, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) []types.SurveyQuestionStatsWithExtra {
	var list = make([]types.SurveyQuestionStatsWithExtra, 0)
	for _, option := range questionItem.QuestionComponentConfig.RowTitleSelectOptions {
		var selectOptions []types.SelectOption
		if len(questionItem.QuestionSelectConfig.SelectOptions) > 0 {
			selectOptions = questionItem.QuestionSelectConfig.SelectOptions
		}
		list = append(list, getCommonStatisticsData(ctx, option.Value, selectOptions, sqlStatResult, nil))
	}
	return list
}

// 获取选择类型题目统计数据
func getSelectStatisticsMethodData(ctx context.Context, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) types.SurveyQuestionStatsWithExtra {
	return getCommonStatisticsData(ctx, questionItem.ConfigProps.UniqueKey, questionItem.QuestionSelectConfig.SelectOptions, sqlStatResult, nil)
}

// 获取排序类统计方式统计数据
func getOrderStatisticsMethodData(ctx context.Context, questionItem *types.QuestionList, sqlStatResult []*types.SurveyDetailSqlStatisticResult) []types.SurveyQuestionStatsWithExtra {
	gLog := xlog.FromContext(ctx)

	result := make([]types.SurveyQuestionStatsWithExtra, 0)

	var (
		newSelectOptionsTemp []types.SelectOption
		selectOptionsLength  = len(questionItem.QuestionSelectConfig.SelectOptions)
	)

	for _, option := range questionItem.QuestionSelectConfig.SelectOptions {
		for i := 0; i < selectOptionsLength; i++ {
			optionTmp := option
			optionTmp.Value = fmt.Sprintf("%v|%v", option.Value, i)
			newSelectOptionsTemp = append(newSelectOptionsTemp, optionTmp)
		}
	}

	flattens := flatten(newSelectOptionsTemp, 1)

	var newSelectOptions = make([]types.SelectOption, 0)
	for _, flatten1 := range flattens {
		selectOptionTmp, ok := flatten1.(types.SelectOption)
		if ok {
			newSelectOptions = append(newSelectOptions, selectOptionTmp)
		} else {
			gLog.Warn("getOrderStatisticsMethodData type not convert", xlog.String("typeIs", fmt.Sprintf("%T", flatten1)))
		}
	}

	var commonStatRes = getCommonStatisticsData(ctx, questionItem.ConfigProps.UniqueKey, newSelectOptions, sqlStatResult, nil)

	var detailNewList = make([]types.Detail, 0)
	for _, detail := range commonStatRes.Detail {
		var (
			valueTmp         string
			tempWeightReduce int
		)

		str, ok := detail.Value.(string)
		if ok {
			valArr := strings.Split(str, "|")
			if len(valArr) >= 2 {
				valueTmp = valArr[0]
				tempWeightReduce = xcast.ToInt(valArr[1])
			} else {
				gLog.Warn("getOrderStatisticsMethodData detail.Value is string but len < 2", xlog.Any("value：", detail.Value))
			}
		} else {
			valueTmp = xcast.ToString(detail.Value)
		}

		detailNewList = append(detailNewList, types.Detail{
			SelectOption: detail.SelectOption,
			Value:        detail.Value,
			Count:        detail.Count,
			Proportion:   detail.Proportion,
			ValueTmp:     valueTmp,
			Weight:       selectOptionsLength - tempWeightReduce,
		})
	}

	groupList := getOrderStatisticsMethodDataGroupBy(detailNewList)
	for _, details := range groupList {

		//// 其实就是累加值，interface性能太差，还可能报错，直接算
		//var detailListInterface []interface{}
		//for _, detail := range details {
		//	detailListInterface = append(detailListInterface, detail)
		//}
		//helpers.Reduce(detailListInterface, func(accumulator, currentValue interface{}, currentIndex int) interface{} {
		//	return xcast.ToInt(accumulator) + currentValue.(types.NewDetail).Count
		//}, 0)

		var (
			counts     int
			totalScore int
		)

		var resultDetailSlice = make([]types.Detail, 0)
		for _, detail := range details {
			counts += detail.Count
			totalScore += detail.Count * detail.Weight
			detail.Proportion = helpers.GetProportion(uint64(detail.Count), uint64(counts), 4)
			resultDetailSlice = append(resultDetailSlice, detail)
		}

		result = append(result, types.SurveyQuestionStatsWithExtra{
			UniqueKey: questionItem.ConfigProps.UniqueKey,
			Count:     int64(counts),
			Score:     helpers.GetProportion(uint64(totalScore), uint64(counts), 2),
			Detail:    resultDetailSlice,
		})
	}

	// 倒序
	sort.Slice(result, func(i, j int) bool {
		return xcast.ToFloat64(result[i].Score) > xcast.ToFloat64(result[j].Score)
	})

	return result
}

// 来自nodeJS groupBy
func getOrderStatisticsMethodDataGroupBy(data []types.Detail) [][]types.Detail {
	var groups = make(map[string][]types.Detail)
	for _, item := range data {
		//var groupBs, _ = json.Marshal(fn(item)) //优化一下 这行就是返回key string
		//var groupStr = string(groupBs)
		var groupStr = item.ValueTmp
		if _, ok := groups[groupStr]; !ok {
			groups[groupStr] = make([]types.Detail, 0)
		}
		groups[groupStr] = append(groups[groupStr], item)
	}

	var result [][]types.Detail
	for groupKey, _ := range groups {
		result = append(result, groups[groupKey])
	}

	return result
}

// Flatten takes an interface{} and returns a flattened slice of interface{}.
// It flattens nested slices up to a specified depth.
func flatten(slice interface{}, depth int) []interface{} {
	var flatSlice []interface{}

	// Helper function for recursion
	var flattenHelper func(interface{})
	flattenHelper = func(slice interface{}) {
		switch v := slice.(type) {
		case []types.SelectOption:
			if depth > 0 {
				for _, item := range v {
					flattenHelper(item)
				}
			} else {
				flatSlice = append(flatSlice, v)
			}
		default:
			flatSlice = append(flatSlice, v)
		}
	}

	flattenHelper(slice)
	return flatSlice
}

func getLastSelectOptions(allSelectOptions, relationSelectOptions []types.SelectOption) []types.SelectOption {
	var selectOptions []types.SelectOption

	// 引用选项
	selectOptions = append(selectOptions, relationSelectOptions...)

	// 自定义选项
	for _, option := range allSelectOptions {
		if option.NewValue == "" {
			selectOptions = append(selectOptions, option)
		}
	}

	return selectOptions
}
