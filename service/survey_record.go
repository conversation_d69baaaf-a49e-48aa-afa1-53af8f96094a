package service

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"gitlab.papegames.com/fringe/sparrow/pkg/broker/xkafka"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xencoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"
	"gitlab.papegames.com/fringe/sparrow/pkg/xsync/errgroup"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtime"
	"strconv"
	"survey/broker"
	"survey/config"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util/base"
	"survey/util/errors"
	"sync"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type SurveyRecordListResponse struct {
	Columns []*proto.Cloumn `json:"columns,omitempty"`
	Data    [][]string      `json:"data"`
	Total   int64           `json:"total"`
}

func SurveyRecordListV2(ctx context.Context, req *proto.SurveyRecordListV2Request) ([]byte, error) {
	gLog := xlog.FromContext(ctx)
	requestId := xnet.GetRequestId(xgin.FromContext(ctx).Request)

	if req.ClientId < 1 {
		return nil, fmt.Errorf("SurveyRecordListV2 param error, clientID is empty")
	}

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		err = fmt.Errorf("UpdateSurveyView GetUserAndNow with error:%+v", err)
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	var viewWhere string
	if len(req.ViewContent) > 0 {
		sqlFromContent, err := GetConditionViewerInstance().GenSQLFromContent(req.ViewContent, Gen_Sql_Kind_BI)
		if err != nil {
			return nil, err
		}
		if len(sqlFromContent) > 0 {
			viewWhere = " and " + sqlFromContent
		}
	}

	var (
		startTime = time.Now()
		wg        = sync.WaitGroup{}
		total     int64
		errChan   = make(chan error, 2)
		listResp  = new(BIResponse)
	)

	defer close(errChan)

	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()

		countSql := " SELECT count(DISTINCT(r.survey_record_id)) AS total FROM " + config.Get().BI.RecordTable + " r "
		countSql += " LEFT JOIN " + config.Get().BI.DetailTable + " d ON r.survey_record_id = d.survey_record_id "
		countSql += " WHERE r.client_id = " + xcast.ToString(req.ClientId) + " and r.survey_id = " + xcast.ToString(req.SurveyId) + " and r.is_delete = 0 " + viewWhere

		resp, err1 := biQuery(requestId, countSql, 0, baseInfo.Username)
		gLog.Info("SurveyRecordListV2 biQuery count duration", xlog.String("time cost", time.Since(startTime).String()))
		if err1 != nil {
			errChan <- err1
		} else {
			if len(resp.Result.Data) > 0 && len(resp.Result.Data[0]) > 0 {
				total = xcast.ToInt64(resp.Result.Data[0][0])
			}
		}
	})

	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()

		listSql := "SELECT r.survey_record_id, r.vopenid, r.vroleid, r.ctime, r.is_valid, timestampdiff( SECOND, r.begin_time, r.end_time ) AS duration FROM " + config.Get().BI.RecordTable + " r"
		listSql += " LEFT JOIN " + config.Get().BI.DetailTable + " d ON r.survey_record_id = d.survey_record_id "
		listSql += " WHERE r.client_id = " + xcast.ToString(req.ClientId) + " and r.survey_id = " + xcast.ToString(req.SurveyId) + " and r.is_delete = 0 " + viewWhere
		listSql += " GROUP BY r.survey_record_id ORDER BY r.survey_record_id DESC"

		//listResp, err = biQuery(requestId, listSql, req.Limit, baseInfo.Username)
		listResp, err = biQuery(requestId, listSql, 1000, baseInfo.Username)
		gLog.Info("SurveyRecordListV2 biQuery list duration", xlog.String("time cost", time.Since(startTime).String()))
		if err != nil {
			errChan <- err
		}
	})
	wg.Wait()

	select {
	case err = <-errChan:
		if err != nil {
			return nil, err
		}
	default:
	}

	r := new(SurveyRecordListResponse)
	r.Total = total
	if len(listResp.Result.Data) > 0 {
		r.Columns = listResp.Result.Columns
		r.Data = listResp.Result.Data
	}
	b, err := json.Marshal(r)
	if err != nil {
		return nil, err
	}
	return b, nil
}

//func SurveyRecordList(ctx context.Context, request *proto.SurveyRecordListRequest) (*proto.SurveyRecordListResponse, error) {
//	gLog := xlog.FromContext(ctx)
//	if request.SurveyId <= 0 {
//		xlog.FromContext(ctx).Error("survey_id is empty")
//		return nil, ecode.BadRequest
//	}
//	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
//	if err != nil {
//		gLog.Error("SurveyIdToHashCode with error", xlog.Err(err))
//		return nil, errors.Wrap(errors.ErrSurveyIdToHashCodeFailed, err)
//	}
//	result, count, err := model.SurveyRecordListData(ctx, hashCode, request.Page, request.PageSize,
//		request.Id, request.Openid, request.RoleId, request.Ip, request.DeviceId,
//		request.SortId, request.SortEndTime, request.SortSecond, 0)
//	if err != nil {
//		xlog.Error("SurveyRecordListData with error", xlog.Err(err))
//		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
//	}
//	var data proto.SurveyRecordListResponse
//
//	var surveyRecordList []*proto.SurveyRecord
//	for _, v := range result {
//		surveyRecord := &proto.SurveyRecord{}
//		surveyRecord.Id = v.ID
//		surveyRecord.Uid = v.UID
//		surveyRecord.Openid = v.OpenID
//		surveyRecord.RoleId = v.RoleID2
//		surveyRecord.Ip = v.IP
//		surveyRecord.DeviceId = v.DeviceID
//		surveyRecord.Second = v.Second
//		if v.BeginTime.IsZero() {
//			surveyRecord.BeginTime = ""
//		} else {
//			surveyRecord.BeginTime = base.FormatTime(v.BeginTime)
//		}
//		if v.EndTime.IsZero() {
//			surveyRecord.EndTime = ""
//		} else {
//			surveyRecord.EndTime = base.FormatTime(v.EndTime)
//		}
//		if surveyRecord.BeginTime != "" && surveyRecord.EndTime != "" {
//			t1 := surveyRecord.BeginTime
//			t2 := surveyRecord.EndTime
//			// 计算时间差
//			seconds, err := base.CalculateTimeDifference(t1, t2)
//			if err != nil {
//				xlog.Error("CalculateTimeDifference with error", xlog.Err(err))
//			}
//			// 将浮点数转换为整数
//			surveyRecord.Second = strconv.Itoa(int(seconds))
//		}
//		if v.Ctime.IsZero() {
//			surveyRecord.Ctime = ""
//		} else {
//			surveyRecord.Ctime = base.FormatTime(v.Ctime)
//		}
//		surveyRecordList = append(surveyRecordList, surveyRecord)
//	}
//
//	data.List = surveyRecordList
//	data.Total = count
//
//	return &data, nil
//}

func SurveyRecordListNew(ctx context.Context, request *proto.SurveyRecordListV2Request) (*proto.SurveyRecordListResponse, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 {
		gLog.Error("SurveyRecordListNew survey_id is empty")
		return nil, ecode.BadRequest
	}

	if request.Page == 0 {
		request.Page = 1
	}

	if request.PageSize == 0 {
		request.PageSize = 50
	}

	var (
		hasView        = len(request.ViewContent) > 0 // 是否有过滤器
		ckViewWhereSql string
		hashCode, _    = base.SurveyIdToHashCode(request.SurveyId)
		err            error
	)

	if hasView {
		ckViewWhereSql, err = GetConditionViewerInstance().GenSQLFromContent(request.ViewContent, Gen_Sql_Kind_CK)
		if err != nil {
			err = fmt.Errorf("SurveyRecordListNew genSQLFromContent ck err%+v request.ViewContent:%v", err, request.ViewContent)
			return nil, err
		}
	}

	var g = new(errgroup.Group)

	var fullValidRecordCount uint64
	g.Go(func() error {
		var st = time.Now()
		fullValidRecordCount, err = model.GetRecordCountNew(ctx, hashCode, 0, nil, nil, ckViewWhereSql)
		if err != nil {
			err = fmt.Errorf("SurveyRecordListNew model.GetSurveyExportCount err%+v", err)
			return err
		}
		gLog.Info("SurveyRecordListNew count", xlog.String("duration", time.Since(st).String()))
		return nil
	})

	var records = make([]*types.ExportSurveyRecordAndDetailItem, 0)
	g.Go(func() error {
		var st = time.Now()
		start := (request.Page - 1) * request.PageSize
		records, err = model.GetRecords(ctx, hashCode, 0, nil, nil, uint32(start), uint32(request.PageSize), ckViewWhereSql)
		if err != nil {
			gLog.Error("SurveyRecordListNew GetRecords with error", xlog.Err(err), xlog.String("view", ckViewWhereSql))
			return errors.Wrap(errors.ErrDBOperateFailed, err)
		}
		gLog.Info("SurveyRecordListNew GetRecords", xlog.String("duration", time.Since(st).String()))
		return nil
	})

	err = g.Wait()
	if err != nil {
		return nil, err
	}

	if fullValidRecordCount < 1 {
		return nil, nil
	}

	var data proto.SurveyRecordListResponse
	var surveyRecordList []*proto.SurveyRecord
	for _, record := range records {
		var second string = "0"
		if record.BeginTime != "" && record.EndTime != "" {
			var ts, _ = base.DiffUtcTimeStr(record.BeginTime, record.EndTime)
			if tsInt := xcast.ToInt(ts); tsInt > 0 {
				second = xcast.ToString(tsInt)
			}
		}

		var item = new(proto.SurveyRecord)
		err = copier.Copy(item, record)
		if err != nil {
			gLog.Error("SurveyRecordListNew copier.Copy err", xlog.Err(err))
			continue
		}
		item.Second = second
		surveyRecordList = append(surveyRecordList, item)
	}

	data.List = surveyRecordList
	data.Total = int64(fullValidRecordCount)

	return &data, nil
}

func DelSurveyRecord(ctx context.Context, delList []int64, hashCode string, surveyID int64) error {
	err := model.DelSurveyRecordData(ctx, delList, hashCode)
	if err != nil {
		xlog.Error("DelSurveyExportTaskData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// delete clickhouse data
	err = model.DeleteRecord(ctx, hashCode, delList)
	if err != nil {
		xlog.Error("model.DeleteRecord with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// 上报BI
	clientId := xgin.FromContext(ctx).Request.URL.Query().Get("client_id")
	SendToBiBlackList(ctx, clientId, delList, surveyID)
	return nil
}

func SetValidSurveyRecord(ctx context.Context, id int64, hashCode string) error {
	err := model.SetValidSurveyRecordData(ctx, id, hashCode)
	if err != nil {
		xlog.Error("SetValidSurveyRecordData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// update clickhouse data
	err = model.UpdateRecord(ctx, hashCode, id, "is_valid", 0)
	if err != nil {
		xlog.Error("model.UpdateRecord with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func SetInvalidSurveyRecord(ctx context.Context, ids []int64, hashCode string, surveyID int64) error {
	gLog := xlog.FromContext(ctx)
	err := model.SetInvalidSurveyRecordData(ctx, ids, hashCode)
	if err != nil {
		gLog.Error("SetValidSurveyRecordData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	// update clickhouse data
	err = model.SetRecordInvalid(ctx, hashCode, ids)
	if err != nil {
		gLog.Error("model.SetRecordInvalid with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 上报BI
	clientId := xgin.FromContext(ctx).Request.URL.Query().Get("client_id")
	SendToBiBlackList(ctx, clientId, ids, surveyID)

	return nil
}

func InValidSurveyRecordList(ctx context.Context, request *proto.SurveyRecordListRequest) (*proto.SurveyRecordListResponse, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 {
		xlog.Error("SurveyRecordList with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	if err != nil {
		gLog.Error("SurveyIdToHashCode with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSurveyIdToHashCodeFailed, err)
	}
	result, count, err := model.SurveyRecordListData(ctx, hashCode, request.Page, request.PageSize,
		request.Id, request.Openid, request.RoleId, request.Ip, request.DeviceId,
		request.SortId, request.SortEndTime, request.SortSecond, 1)
	if err != nil {
		xlog.Error("SurveyRecordListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	var data proto.SurveyRecordListResponse

	var surveyRecordList []*proto.SurveyRecord
	for _, v := range result {
		surveyRecord := &proto.SurveyRecord{}
		surveyRecord.Id = v.ID
		surveyRecord.Uid = v.UID
		surveyRecord.Openid = v.OpenID
		surveyRecord.RoleId = v.RoleID2
		surveyRecord.Ip = v.IP
		surveyRecord.DeviceId = v.DeviceID
		surveyRecord.Second = v.Second
		if v.BeginTime.IsZero() {
			surveyRecord.BeginTime = ""
		} else {
			surveyRecord.BeginTime = base.FormatTime(v.BeginTime)
		}
		if v.EndTime.IsZero() {
			surveyRecord.EndTime = ""
		} else {
			surveyRecord.EndTime = base.FormatTime(v.EndTime)
		}
		if surveyRecord.BeginTime != "" && surveyRecord.EndTime != "" {
			t1 := surveyRecord.BeginTime
			t2 := surveyRecord.EndTime
			// 计算时间差
			seconds, err := base.CalculateTimeDifference(t1, t2)
			if err != nil {
				xlog.Error("CalculateTimeDifference with error", xlog.Err(err))
			}
			// 将浮点数转换为整数
			surveyRecord.Second = strconv.Itoa(int(seconds))
		}
		if v.Ctime.IsZero() {
			surveyRecord.Ctime = ""
		} else {
			surveyRecord.Ctime = base.FormatTime(v.Ctime)
		}
		surveyRecordList = append(surveyRecordList, surveyRecord)
	}

	data.List = surveyRecordList
	data.Total = count

	return &data, nil
}

func SendToBiBlackList(ctx context.Context, clientId string, ids []int64, surveyID int64) {
	var wg = sync.WaitGroup{}
	for _, id := range ids {
		wg.Add(1)
		go func(surveyRecordId int64) {
			defer wg.Done()
			if r := recover(); r != nil {
				xlog.FromContext(ctx).Error("SendToBiBlackList bi recover", xlog.Any("", r))
				return
			}
			UploadSurveyRecordBlacklist(ctx, &types.SurveyRecordBlacklist{
				ClientId:       clientId,
				SurveyId:       surveyID,
				SurveyRecordId: surveyRecordId,
			})
		}(id)
	}
	wg.Wait()
}

func UploadSurveyRecordBlacklist(ctx context.Context, data *types.SurveyRecordBlacklist) {
	gLog := xlog.FromContext(ctx)
	if broker.GetWriter() == nil {
		xlog.FromContext(ctx).Error("UploadSurveyRecordBlacklist get kafka client is nil, please check config")
		return
	}
	timeStr := xtime.Now().Format(time.DateTime)
	data.EventID = time.Now().UnixNano()
	data.CreateTime = timeStr
	data.PartEvent = "survey_record_blacklist"
	data.DtEventTime = timeStr
	bts, _ := json.Marshal(data)
	msg := xkafka.Message{
		Value: bts,
	}
	err := broker.GetWriter().WriteMessages(ctx, msg)
	gLog.Info("UploadSurveyRecordBlacklist WriteMessages", xlog.String("data:", string(bts)))
	if err != nil {
		gLog.Error("UploadSurveyRecordBlacklist WriteMessages err", xlog.Err(err))
		return
	}
}
