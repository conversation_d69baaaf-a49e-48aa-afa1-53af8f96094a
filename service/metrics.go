package service

import "gitlab.papegames.com/fringe/sparrow/pkg/metric"

var (
	MetricsErrorOccurred = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "survey",
		Subsystem:   "admin",
		Name:        "error_occurred",
		Help:        "survey admin error occurred total",
		Labels:      []string{"kind", "reason"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsStatisticstMs = metric.NewHistogramVec(&metric.HistogramVecOpts{
		Namespace:   "survey",
		Subsystem:   "admin",
		Name:        "export_time_cost",
		Help:        "survey admin statistics time cost",
		Labels:      []string{"kind"},
		ConstLabels: metric.TargetLabels,
		Buckets:     metric.BucketRT,
	})
)
