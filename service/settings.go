package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"net/http"
	"strconv"
	"survey/config"
	"survey/proto"
	"survey/util/errors"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func GetValidRedeemConfigListNew(ctx context.Context, request *proto.ValidRedeemConfigRequest) ([]*proto.ValidRedeemConfig, error) {
	type ValidRedeemConfigParent struct {
		ClientId string `json:"clientid"`
		*proto.ValidRedeemConfig
	}

	type ResultData struct {
		Success bool                       `json:"success"`
		Data    []*ValidRedeemConfigParent `json:"data"`
	}

	var (
		logger = xlog.FromContext(ctx)
		url    = fmt.Sprintf("%s/api/v1/external/validCdkeyList?env=%s&clientid=%d", config.Get().CmsMajorUrl, GetApiExternalPathEnv(), request.ClientId)
		result = &ResultData{}
	)

	_, err := xresty.New().R().SetHeader("Content-Type", "application/x-www-form-urlencoded").SetResult(result).Get(url)
	//logger.Info("GetValidRedeemConfigListNew http response", xlog.String("res", res.String()))
	if err != nil {
		logger.Error("GetValidRedeemConfigListNew http request failed", xlog.String("url", url), xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}

	var list = make([]*proto.ValidRedeemConfig, 0)
	for _, item := range result.Data {
		if item.RedeemGift == "null" {
			item.RedeemGift = ""
		}
		item.RedeemHead2 = item.RedeemHead
		item.ValidRedeemConfig.ClientId = item.ClientId
		list = append(list, item.ValidRedeemConfig)
	}

	return list, nil
}

func GetPreAwardTemplateListNew(ctx context.Context, request *proto.PreAwardTemplateRequest) ([]*proto.PreAwardTemplate, error) {
	// ID 埋得坑啊，类型不对，最好不修改proto
	type PreAwardTemplateParent struct {
		ID       int64  `json:"id"`
		ClientId string `json:"clientid"`
		*proto.PreAwardTemplate
	}

	type ResultData struct {
		Success bool                      `json:"success"`
		Data    []*PreAwardTemplateParent `json:"data"`
	}

	var (
		logger = xlog.FromContext(ctx)
		url    = fmt.Sprintf("%s/api/v1/external/preAwardTemplateList?env=%s&clientid=%d", config.Get().CmsMajorUrl, GetApiExternalPathEnv(), request.ClientId)
		result = &ResultData{}
	)

	_, err := xresty.New().R().SetHeader("Content-Type", "application/x-www-form-urlencoded").SetResult(result).Get(url)
	//logger.Info("GetPreAwardTemplateListNew http response", xlog.String("res", res.String()))
	if err != nil {
		logger.Error("GetPreAwardTemplateListNew http request failed", xlog.String("url", url), xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}

	var list = make([]*proto.PreAwardTemplate, 0)
	for _, item := range result.Data {
		item.PreAwardTemplate.Id = xcast.ToString(item.ID)
		item.PreAwardTemplate.ClientId = item.ClientId
		list = append(list, item.PreAwardTemplate)
	}

	return list, nil
}

func GetApiExternalPathEnv() string {
	var env string = "1"
	if config.Get().NodeEnv == "pre" {
		env = "0"
	}
	return env
}

// 弃用
func GetValidRedeemConfigList(ctx context.Context, request *proto.ValidRedeemConfigRequest) ([]*proto.ValidRedeemConfig, error) {
	logger := xlog.FromContext(ctx)
	majorUrl := config.Get().CmsMajorUrl
	url := fmt.Sprintf("%s/api/v1/external/validCdkeyList?env=%s&clientid=%d", majorUrl, GetApiExternalPathEnv(), request.ClientId)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("GetValidRedeemConfigList Error creating request:", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("http request failed", xlog.String("url", url), xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logger.Error("http status code notok", xlog.String("url", url), xlog.Int("status", resp.StatusCode))
		return nil, errors.ErrSystem
	}
	// RedeemItem 定义 redeem_item 内部的结构体
	// type RedeemItem struct {
	// 	ID       int `json:"id"`
	// 	Type     int `json:"type"`
	// 	Num      int `json:"num"`
	// 	Property int `json:"property"`
	// }

	type Item struct {
		Id                 int    `json:"id"`
		ClientId           string `json:"clientid"`
		RedeemId           int    `json:"redeem_id"`
		RedeemHead         string `json:"redeem_head"`
		RedeemType         int    `json:"redeem_type"`
		IsCustom           int    `json:"is_custom"`
		RedeemNumber       int    `json:"redeem_number"`
		RedeemTimeFor1Name int    `json:"redeem_timefor1name"`
		RedeemTimeFor1Id   int    `json:"redeem_timefor1id"`
		RedeemOpenTime     string `json:"redeem_opentime"`
		RedeemCloseTime    string `json:"redeem_closetime"`
		RedeemItem         string `json:"redeem_item"`
		RedeemGift         string `json:"redeem_gift"`
		RedeemChannel      string `json:"redeem_channel"`
		RedeemCreateId     string `json:"redeem_create_id"`
		RedeemWhitelist    string `json:"redeem_whitelist"` // 泛型可以表示任意类型
		Extra              string `json:"extra"`
		Memo               string `json:"memo"`
		MTime              string `json:"mtime"`
		CTime              string `json:"ctime"`
		RedeemAuto         int    `json:"redeem_auto"`
		Notify             int    `json:"notify"`
		RedeemHeadDup      string `json:"redeemHead"` // 处理重复字段
	}

	type ResultData struct {
		Success bool   `json:"success"`
		Data    []Item `json:"data"`
	}
	var result ResultData

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		logger.Error("json decoding failed", xlog.Err(err))
		return nil, err
	}
	list := []Item{}
	if result.Success {
		list = result.Data
	}
	ValidRedeemConfigList := make([]*proto.ValidRedeemConfig, len(list))
	for i, v := range list {
		redeemGift := v.RedeemGift
		if v.RedeemGift == "null" {
			redeemGift = ""
		}
		ValidRedeemConfigList[i] = &proto.ValidRedeemConfig{
			Id:                 int64(v.Id),
			ClientId:           v.ClientId,
			RedeemId:           int32(v.RedeemId),
			RedeemHead:         v.RedeemHead,
			RedeemType:         int32(v.RedeemType),
			IsCustom:           int32(v.IsCustom),
			RedeemNumber:       int32(v.RedeemNumber),
			RedeemTimefor1Name: int32(v.RedeemTimeFor1Name),
			RedeemTimefor1Id:   int32(v.RedeemTimeFor1Id),
			RedeemOpenTime:     v.RedeemOpenTime,
			RedeemCloseTime:    v.RedeemCloseTime,
			RedeemItem:         v.RedeemItem,
			RedeemGift:         redeemGift,
			RedeemChannel:      v.RedeemChannel,
			RedeemCreateId:     v.RedeemCreateId,
			RedeemWhitelist:    v.RedeemWhitelist,
			Extra:              v.Extra,
			Memo:               v.Memo,
			Mtime:              v.MTime,
			Ctime:              v.CTime,
			RedeemAuto:         int32(v.RedeemAuto),
			Notify:             int32(v.Notify),
			RedeemHead2:        v.RedeemHead,
		}
	}
	return ValidRedeemConfigList, nil
}

// 弃用
func GetPreAwardTemplateList(ctx context.Context, request *proto.PreAwardTemplateRequest) ([]*proto.PreAwardTemplate, error) {
	logger := xlog.FromContext(ctx)
	majorUrl := config.Get().CmsMajorUrl
	url := fmt.Sprintf("%s/api/v1/external/preAwardTemplateList?env=%s&clientid=%d", majorUrl, GetApiExternalPathEnv(), request.ClientId)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.Error("GetPreAwardTemplateList Error creating request:", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("http request failed", xlog.String("url", url), xlog.Err(err))
		return nil, errors.Wrap(errors.ErrSystem, err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logger.Error("http status code notok", xlog.String("url", url), xlog.Int("status", resp.StatusCode))
		return nil, errors.ErrSystem
	}

	type Item struct {
		Id        int    `json:"id"`
		Name      string `json:"name"`
		ClientId  string `json:"clientid"`
		Sender    string `json:"sender"`
		Title     string `json:"title"`
		Body      string `json:"body"`
		Content   string `json:"content"`
		Memo      string `json:"memo"`
		Editor    string `json:"editor"`
		Stime     string `json:"stime"`
		Etime     string `json:"etime"`
		Mtime     string `json:"mtime"`
		Ctime     string `json:"ctime"`
		Permanent int    `json:"permanent"`
		Creator   string `json:"creator"`
		Key       int    `json:"key"`
	}

	type ResultData struct {
		Success bool   `json:"success"`
		Data    []Item `json:"data"`
	}
	var result ResultData

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		logger.Error("json decoding failed", xlog.Err(err))
		return nil, err
	}
	list := []Item{}
	if result.Success {
		list = result.Data
	}
	PreAwardTemplateList := make([]*proto.PreAwardTemplate, len(list))
	for i, v := range list {
		PreAwardTemplateList[i] = &proto.PreAwardTemplate{
			Id:        strconv.Itoa(v.Id),
			Name:      v.Name,
			ClientId:  v.ClientId,
			Sender:    v.Sender,
			Title:     v.Title,
			Body:      v.Body,
			Content:   v.Content,
			Memo:      v.Memo,
			Editor:    v.Editor,
			Stime:     v.Stime,
			Etime:     v.Etime,
			Mtime:     v.Mtime,
			Ctime:     v.Ctime,
			Permanent: int32(v.Permanent),
			Creator:   v.Creator,
			Key:       int32(v.Key),
		}
	}
	return PreAwardTemplateList, nil
}
