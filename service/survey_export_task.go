package service

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	gosstrategy "gitlab.papegames.com/fringe/pkg/shared/gos_strategy"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"strconv"
	"strings"
	"survey/config"
	"survey/constants"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util"
	"survey/util/base"
	"survey/util/errors"
	"time"

	gosError "errors"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtime"
)

func SurveyExportTaskList(ctx context.Context, clientId string, surveyId int64, page int32, pageSize int32) (*proto.SurveyExportTaskListResponse, error) {
	logger := xlog.FromContext(ctx)
	result, count, err := model.SurveyExportTaskListData(ctx, clientId, surveyId, page, pageSize)
	if err != nil {
		logger.Error("Failed to get SurveyExportTaskListData", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	surveyExportTaskList := make([]*proto.SurveyExportTask, len(result))
	for i, r := range result {
		exportTask, err := mapSurveyExportTask(r, ctx)
		if err != nil {
			continue
		}
		surveyExportTaskList[i] = exportTask
	}
	return &proto.SurveyExportTaskListResponse{
		Total: count,
		List:  surveyExportTaskList,
	}, nil
}

func mapSurveyExportTask(r *model.SurveyRecordExportLog, ctx context.Context) (*proto.SurveyExportTask, error) {
	logger := xlog.FromContext(ctx)
	clientId, err := strconv.ParseInt(r.ClientId, 10, 64)
	if err != nil {
		logger.Error("Failed to parse ClientId", xlog.String("clientId", r.ClientId), xlog.Err(err))
		return nil, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
	}
	surveyExportTask := &proto.SurveyExportTask{
		Id:           r.Id,
		ClientId:     clientId,
		SurveyId:     r.SurveyId,
		Name:         r.Name,
		FileType:     r.FileType,
		DataType:     r.DataType,
		IsValid:      r.IsValid,
		Url:          r.Url,
		Status:       r.Status,
		CreateTime:   base.FormatTime(r.CreateTime),
		UpdateTime:   base.FormatTime(r.UpdateTime),
		CompleteTime: base.FormatTime(r.CompleteTime),
	}
	return surveyExportTask, nil
}

func CreateSurveyExportTask(ctx context.Context, request *proto.SurveyExportTask) error {
	var (
		viewWhereSql string
		err          error
	)

	request.ViewContent = strings.TrimSpace(request.ViewContent)
	if request.ViewContent != "" {
		viewWhereSql, err = GetConditionViewerInstance().GenSQLFromContent(request.ViewContent, Gen_Sql_Kind_CK)
		if err != nil {
			err = fmt.Errorf("CreateSurveyExportTask genSQLFromContent ck err%+v ViewContent:%+v", err, request.ViewContent)
			return err
		}
	}

	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyIdToHashCode with error", xlog.Err(err))
		return errors.ErrDBNoData
	}

	var st *time.Time
	if request.StartTime != "" {
		stTmp, err := xtime.ParseTime(request.StartTime)
		if err != nil {
			return err
		}
		st = &stTmp
	}

	var et *time.Time
	if request.EndTime != "" {
		stTmp, err := xtime.ParseTime(request.EndTime)
		if err != nil {
			return err
		}
		et = &stTmp
	}

	// 用户分群
	var extra = new(types.ExportQueueExtra)
	if request.GetExtra() != "" {
		err := sonic.UnmarshalString(request.GetExtra(), extra)
		if err != nil {
			return err
		}

		if extra == nil {
			return errors.ErrSurveyExportUserClusterLost
		}

		// user-query 错误，避免分群错误，而不执行标签任务
		var userQueryError error

		// 用户分群
		if len(extra.UserCluster) > 0 {
			// 检测分群同步任务状态是否创建，完成
			clusterErrMap, err := userQueryTool.CheckUserClusterStatus(ctx, request.ClientId, extra.UserCluster)
			if err != nil {
				// 如果是用户分群任务未创建，自动创建同步任务
				for idx, cluster := range extra.UserCluster {
					if gosError.Is(clusterErrMap[idx], errors.ErrSurveyExportUserClusterTaskLost) {
						// 创建用户分群同步任务
						err1 := submitUserCluster(ctx, request.ClientId, &gosstrategy.Cluster{
							ClusterName: cluster.ClusterInfo.ClusterName,
							EntityName:  cluster.ClusterInfo.EntityName,
						})
						if err1 != nil {
							userQueryError = err1
							break
						}
					}
				}
				userQueryError = err
			}
		}

		// 用户标签
		if len(extra.UserTag) > 0 {
			// 检测标签同步任务状态是否创建，完成
			errMap, err := userQueryTool.CheckUserTagStatus(ctx, request.ClientId, extra.UserTag)
			if err != nil {
				// 如果是用户分群任务未创建，自动创建同步任务
				for idx, tag := range extra.UserTag {
					if gosError.Is(errMap[idx], errors.ErrSurveyExportUserTagTaskLost) {
						// 创建用户标签同步任务
						_, err1 := userQueryTool.SubmitUserTag(ctx, &types.UserQuerySubmitUserTagReq{
							ClientId:      request.ClientId,
							TagName:       tag.TagInfo.TagName,
							TagEntityName: tag.TagInfo.TagEntityName,
						})
						if err1 != nil {
							userQueryError = err1
							break
						}
					}
				}
				userQueryError = err
			}
		}

		if userQueryError != nil {
			return userQueryError
		}

		// 检测问题筛选合法性
		if extra.QuestionFilter != nil {
			err := checkFilter(extra.QuestionFilter)
			if err != nil {
				xlog.FromContext(ctx).Error("CreateSurveyExportTask checkFilter err", xlog.Err(err))
				return errors.ErrSurveyExportQuestionFilterOperatorErr
			}
		}
	}

	// 试运行sql
	records, err := model.GetRecordsWithDetails(ctx, hashCode, request.IsValid, st, et, 0, 1, viewWhereSql)
	if err != nil {
		err = fmt.Errorf("CreateSurveyExportTask model.GetRecords err:%+v", err)
		return err
	}

	if len(records) < 1 {
		return errors.ErrDBNoData
	}

	ok, err := model.ExistSurveyById(ctx, strconv.FormatInt(request.ClientId, 10), request.SurveyId)
	if err != nil {
		xlog.FromContext(ctx).Error("ExistSurveyById with error", xlog.Err(err))
		return err
	} else if !ok {
		xlog.FromContext(ctx).Error("survey not exist")
		return errors.ErrDBNoData
	}

	err = model.CreateSurveyExportTaskData(
		strconv.FormatInt(request.ClientId, 10),
		request.SurveyId,
		request.Name,
		request.FileType,
		request.DataType,
		request.IsValid,
		request.StartTime,
		request.EndTime,
		request.ViewContent,
		request.DataSource,
		request.DataNum,
		request.GetExtra(),
	)
	if err != nil {
		xlog.Error("CreateSurveyExportTaskData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func DelSurveyExportTask(ctx context.Context, clientId string, surveyId int64, ids []int64) error {
	err := model.DelSurveyExportTaskData(ctx, clientId, surveyId, ids)
	if err != nil {
		xlog.Error("DelSurveyExportTaskData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func ResetSurveyExportTaskStatus(ctx context.Context, clientId string, surveyId int64, id int64) error {
	err := model.ResetSurveyExportTaskStatusData(ctx, clientId, surveyId, id)
	if err != nil {
		xlog.Error("ResetSurveyExportTaskStatusData with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil
}

func SurveyExportUserClusterSubmit(ctx context.Context, request *proto.SurveyExportUserClusterSubmitReq) error {
	// 检查是否已创建
	userClusterExist, err := userQueryTool.CheckUserClusterExist(ctx, request.ClientId, request.GetUserStrategy())
	if err != nil {
		return err
	}

	// 已经创建了
	if userClusterExist {
		return nil
	}

	// 创建分群同步任务
	err = submitUserCluster(ctx, request.ClientId, &gosstrategy.Cluster{
		ClusterName: request.GetUserStrategy().ClusterName,
		EntityName:  request.GetUserStrategy().EntityName,
	})

	return err
}

func submitUserCluster(ctx context.Context, clientId int64, userStrategy *gosstrategy.Cluster) error {
	// 提交异步任务
	req := &types.UserQuerySubmitUserPackageReq{
		BizId:    time.Now().Unix(),
		BizType:  constants.UserQueryBiType,
		ClientID: clientId,
		BiEnv:    config.Get().UserQuery.BiEnv,
		UserStrategy: &gosstrategy.UserStrategy{
			SelectMode: 1, // 1: 用户分群
			Cluster:    userStrategy,
		},
	}

	res, err := userQueryTool.SubmitUserPackage(ctx, req)
	if err != nil {
		return err
	}

	if res.Code != 0 {
		return fmt.Errorf("user-query response code:%+v info:%s request-id:%s", res.Code, res.Info, res.RequestId)
	}

	return nil
}

func SurveyExportHeaders(ctx context.Context, request *proto.SurveyExportHeadersReq) (*proto.SurveyExportHeadersRes, error) {
	res := new(proto.SurveyExportHeadersRes)

	survey, err := model.GetSurveyById(ctx, request.GetSurveyId())
	if err != nil {
		return nil, err
	}

	// 获取问题列表 - 元数据
	questionList, err := GenDetailSurveyQuestionList(ctx, survey)
	if err != nil {
		err = fmt.Errorf("SurveyExportHeaders GenDetailSurveyQuestionList error: %+v", err)
		return res, err
	}

	res.List = make([]*proto.SurveyExportHeadersRes_Question, 0)
	res.Version = base.FormatTime(survey.Mtime)
	for _, questionItem := range questionList {
		// 矩阵题目特殊处理
		questionRowTitles := make([]*proto.SurveyExportHeadersRes_QuestionRowTitle, 0)
		if questionItem.ConfigProps.IsMatrix {
			for _, option := range questionItem.QuestionComponentConfig.RowTitleSelectOptions {
				questionRowTitles = append(questionRowTitles, &proto.SurveyExportHeadersRes_QuestionRowTitle{
					RowUniqueKey: option.Value,
					RoleTitle:    util.RemoveRichTextTags(GetLabelStr(option.Label), false),
				})
			}
		}

		// 选择题选项
		var selectOptions = make([]*proto.SurveyExportHeadersRes_SelectOptions, 0)
		var cfg = questionItem.ConfigProps
		if cfg.StatisticsMethod == "select" || cfg.StatisticsMethod == "order" || cfg.QuestionType == "select" {
			for _, option := range questionItem.QuestionSelectConfig.SelectOptions {
				item := &proto.SurveyExportHeadersRes_SelectOptions{
					Value: option.Value,
					Label: util.RemoveRichTextTags(GetLabelStr(option.Label), false),
				}
				// 允许填空
				var allow bool
				if _, ok := option.BlankFill.(string); !ok {
					blankFillMap, ok2 := option.BlankFill.(map[string]interface{})
					if ok2 {
						if allowVal, ok3 := blankFillMap["allow"]; ok3 {
							allow = xcast.ToBool(allowVal)
						}
					}
				}
				item.HasBlank = allow
				selectOptions = append(selectOptions, item)
			}
		}

		// 打分题
		var questionTitle string = getQuestionBaseConfigTitle(ctx, questionItem.QuestionBaseConfig.QuestionTitle)
		if questionItem.ConfigProps.IsRate {
			questionTitle = util.RemoveRichTextTags(questionTitle, true)
		} else {
			questionTitle = util.RemoveRichTextTags(questionTitle, false)
		}

		question := &proto.SurveyExportHeadersRes_Question{
			QuestionUniqueKey: questionItem.ConfigProps.UniqueKey, //矩阵的要从QuestionRowTitles里面获取，来匹配数值
			QuestionTitle:     questionTitle,
			StatisticsMethod:  questionItem.ConfigProps.StatisticsMethod,
			QuestionType:      questionItem.ConfigProps.QuestionType,
			SelectMode:        questionItem.ConfigProps.SelectMode,
			QuestionId:        xcast.ToInt32(questionItem.ConfigProps.QuestionId),
			QuestionRowTitles: questionRowTitles,
			SelectOptions:     selectOptions,
		}

		res.List = append(res.List, question)
	}

	return res, nil
}
