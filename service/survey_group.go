package service

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"net/http"
	"slices"
	"strings"
	"survey/config"
	"survey/constants"
	"survey/model"
	"survey/proto"
	"survey/types"
	"survey/util"
	"survey/util/base"
	"survey/util/errors"
)

func SurveyGroupCreate(ctx context.Context, req *proto.SurveyGroupCreateReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("SurveyGroupCreate GetUserAndNow with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	// 基本参数判断 - name
	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" {
		err = fmt.Errorf("SurveyGroupCreate Param err, Name is empty")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - type
	if req.Type != 1 && req.Type != 2 {
		err = fmt.Errorf("SurveyGroupCreate Param err, type is invalid")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - LimitType
	if req.LimitType < 1 || len(req.Settings) < 1 {
		err = fmt.Errorf("SurveyGroupCreate Param err, LimitType is invalid or setting is empty")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - setting
	if err := CheckSurveyGroupFormat(req.Settings); err != nil {
		return nil, err
	}

	//// 同一个client id内 名称唯一
	//data, err := model.GetSurveyGroupByClientIdAndName(req.ClientId, req.Name, 0)
	//if err != nil && !xgorm.RecordNotFound(err) {
	//	return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	//}
	//
	//if data != nil {
	//	return nil, errors.ErrNameRepeat
	//}

	id, err := model.CreateSurveyGroup(req.ClientId, req.Name, req.LimitType, req.Type, req.Settings, baseInfo.Username)
	if err != nil {
		gLog.Error("CreateSurveyView model.CreateSurveyView with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 创建 survey_group_record_{hash_code}表
	groupHashCode, _ := base.SurveyGroupIdToHashCode(int64(id))
	err = model.CreateSurveyGroupRecordTable(groupHashCode)
	if err != nil {
		gLog.Error("CreateSurveyView model.CreateSurveyGroupRecordTable with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	return nil, nil
}

func SurveyGroupUpdate(ctx context.Context, req *proto.SurveyGroupUpdateReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("UpdateSurveyGroup GetUserAndNow with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	// 基本参数判断 - id
	if req.Id < 1 {
		err = fmt.Errorf("UpdateSurveyGroup Param err, id is invalid")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - name
	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" {
		err = fmt.Errorf("UpdateSurveyGroup Param err, Name is empty")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - type
	if req.Type != 1 && req.Type != 2 {
		err = fmt.Errorf("UpdateSurveyGroup Param err, type is invalid")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - LimitType setting
	if req.LimitType < 1 || len(req.Settings) < 1 {
		err = fmt.Errorf("SurveyGroupUpdate Param err, LimitType is invalid or setting is empty")
		return nil, errors.Wrap(errors.ErrMustParamEmpty, err)
	}

	// 基本参数判断 - setting
	if err := CheckSurveyGroupFormat(req.Settings); err != nil {
		return nil, err
	}

	//// 同一个client id内 名称唯一
	//data, err := model.GetSurveyGroupByClientIdAndName(req.ClientId, req.Name, req.Id)
	//if err != nil && !xgorm.RecordNotFound(err) {
	//	return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	//}
	//
	//if data != nil {
	//	return nil, errors.ErrNameRepeat
	//}

	_, err = model.UpdateSurveyGroup(req.ClientId, req.Id, req.Name, req.LimitType, req.Type, req.Settings, baseInfo.Username)
	if err != nil {
		gLog.Error("UpdateSurveyGroup model.UpdateSurveyGroup with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

func SurveyGroupDetail(ctx context.Context, req *proto.SurveyGroupDetailReq) (*proto.CmsSurveyGroupInfo, error) {
	detail, err := model.GetSurveyGroupDetail(req.ClientId, req.Id)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyGroupDetail model.GetSurveyGroupDetail fail", xlog.Err(err), xlog.Int64("id", req.Id))
		return nil, err
	}
	return detail, nil
}

func SurveyGroupList(ctx context.Context, req *proto.SurveyGroupListReq) (*proto.SurveyGroupListRes, error) {
	list, err := model.GetSurveyGroupList(req, constants.Not_Delete)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyGroupList model.GetSurveyGroupList fail", xlog.Err(err), xlog.Int64("id", req.Id))
		return nil, err
	}

	for _, info := range list {
		ctime, err := util.RFC3339ToDatetime(info.Ctime)
		if err != nil {
			return nil, err
		}
		mtime, err := util.RFC3339ToDatetime(info.Mtime)
		if err != nil {
			return nil, err
		}
		info.Ctime = ctime
		info.Mtime = mtime
	}

	res := &proto.SurveyGroupListRes{
		List:  list,
		Total: int64(len(list)),
	}

	return res, nil
}

func SurveyGroupSubUpdate(ctx context.Context, req *proto.SurveyGroupSubUpdateReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)

	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		gLog.Error("SurveyGroupSubUpdate GetUserAndNow with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrNotFound, err)
	}

	var updateMap = make(map[string]interface{})
	switch req.Type {
	case proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Publish: // 发布
		updateMap["is_publish"] = 1
		updateMap["hash_code"], _ = base.SurveyGroupIdToHashCode(req.Id)
	case proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Delete: // 删除
		updateMap["is_delete"] = constants.Is_Delete
		updateMap["is_publish"] = 0
	case proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Closed: // 关闭
		updateMap["is_closed"] = constants.Is_Delete
		updateMap["is_publish"] = 0
	case proto.SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Settings: // 更新settings
		// 基本参数判断 - setting
		if len(req.Value) < 1 {
			err = fmt.Errorf("SurveyGroupSubUpdate Param err, Settings empty, Settings:%+v", req.Value)
			return nil, errors.Wrap(errors.ErrParamCheckFailed, err)
		}
		// 基本参数判断 - setting
		if err := CheckSurveyGroupFormat(req.Value); err != nil {
			return nil, err
		}
		updateMap["settings"] = req.Value
		updateMap["is_publish"] = 0
	default:
		return nil, errors.ErrInvalidOperate
	}

	updateMap["mtime"] = base.GetNowTime()
	updateMap["editor"] = baseInfo.Username

	_, err = model.SurveyGroupSubUpdate(req.ClientId, req.Id, updateMap)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyGroupSubUpdate model.SurveyGroupSubUpdate fail", xlog.Err(err), xlog.Int64("id", req.Id), xlog.Any("updates", updateMap))
		return nil, err
	}

	// 如果是发布，向cms_survey_group_version 添加一条数据
	if updateMap["is_publish"] == 1 {
		surveyInfo, err := model.GetSurveyGroupDetail(req.ClientId, req.Id)
		if err != nil || surveyInfo == nil {
			gLog.Error("SurveyGroupSubUpdate not found or err", xlog.Int64("id", req.Id), xlog.Err(err))
			return nil, errors.ErrDBOperateFailed
		}

		versionData := GenSurveyGroupModel(surveyInfo)

		_, err = model.CreateCmsSurveyGroupVersion(versionData)
		if err != nil {
			gLog.Error("SurveyGroupSubUpdate CreateCmsSurveyGroupVersion err", xlog.Int64("id", req.Id), xlog.Err(err))
			return nil, errors.ErrDBOperateFailed
		}

		// 通知C端配置表更了
		err = NoticeToCReloadConfig("", versionData.HashCode)
		if err != nil {
			return nil, err
		}
	}

	return nil, nil
}

func CheckSurveyGroupFormat(setting string) error {
	if !sonic.ValidString(setting) {
		return errors.ErrParamCheckFailed
	}
	var dd = new(types.SurveyGroupSetting)
	err := sonic.UnmarshalString(setting, dd)
	if err != nil {
		return errors.Wrap(errors.ErrJsonUnmarshal, err)
	}
	if len(dd.GroupSurvey) == 0 {
		return fmt.Errorf("group_survey is empty")
	}

	var hasDefault bool
	for k, survey := range dd.GroupSurvey {
		if survey.Default {
			hasDefault = true
		}
		if survey.SurveyID < 1 || survey.HashCode == "" {
			return errors.Wrap(errors.ErrGroupIdOrHashCodeLost, fmt.Errorf("%v：问卷参数缺失或未发布", k))
		}
	}

	if !hasDefault {
		return errors.ErrGroupNeedDefault
	}

	return nil
}

func SurveyGroupOverwriteSend(ctx context.Context, clientID int64, surveyGroupId int64) (*xtype.Empty, error) {
	//gLog := xlog.FromContext(ctx)

	// 不合法的同步租户ID
	if !slices.Contains(config.Get().Overwrite.ClientId, clientID) {
		return nil, errors.ErrOverwriteClientIDInvalid
	}

	detail, err := model.GetSurveyGroupDetail(clientID, surveyGroupId)
	if err != nil {
		return nil, err
	}

	//// 未发布问卷，不能同步回归服
	//if detail.IsPublish == 0 {
	//	return nil, errors.ErrSurveyGroupNotPublish
	//}

	if detail.Id <= 0 {
		return nil, errors.ErrNotFound
	}

	bs, err := sonic.Marshal(detail)
	if err != nil {
		return nil, err
	}

	//// 带上cookie
	//token, err := xgin.FromContext(ctx).Request.Cookie(config.Get().GosCfg.CookieName)
	//if err != nil {
	//	return nil, err
	//}
	//
	//cookie := &http.Cookie{
	//	Name:  config.Get().GosCfg.CookieName,
	//	Value: token.Value,
	//}

	//发送给接受同步方
	var res = &types.Response[any]{}
	response, err := xresty.New().R().SetBody(&proto.SurveyGroupOverwriteSyncReq{
		ClientId:    clientID,
		SurveyGroup: string(bs),
	}).SetResult(res).Post(config.Get().Overwrite.SyncGroupUrl)

	if err != nil {
		return nil, err
	}

	if response.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("SurveyGroupOverwriteSend err:%d info:%s request-id:%s response.StatusCode:%d", res.Code, res.Info, res.RequestId, response.StatusCode())
	}

	if res.Code != 0 {
		return nil, fmt.Errorf("SurveyGroupOverwriteSend err:%d info:%s request-id:%s", res.Code, res.Info, res.RequestId)
	}

	return nil, nil
}

func SurveyGroupOverwriteSync(ctx context.Context, clientID int64, surveyGroupStr string) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)

	// 不合法的同步租户ID
	if !slices.Contains(config.Get().Overwrite.ClientId, clientID) {
		return nil, errors.ErrOverwriteClientIDInvalid
	}

	var surveyGroup = new(proto.CmsSurveyGroupInfo)
	err := sonic.UnmarshalString(surveyGroupStr, surveyGroup)
	if err != nil {
		return nil, err
	}

	// 获取问卷组
	surveyExist, err := model.GetSurveyGroupDetail(clientID, int64(surveyGroup.Id))
	if err != nil && !xgorm.RecordNotFound(err) {
		return nil, err
	}

	// 问卷组重复
	if surveyGroup.Id > 0 && surveyExist != nil && surveyExist.Id == surveyGroup.Id {
		gLog.Info("SurveyGroupOverwriteSync overwrite repeat", xlog.Uint64("surveyGroupId", surveyGroup.Id))
		//return nil, errors.ErrSurveyIDRepeat
	}

	id, err := model.SurveyGroupCreateOrUpdate(ctx, surveyGroup)
	if err != nil {
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	// 创建 survey_group_record_{hash_code}表
	groupHashCode, _ := base.SurveyGroupIdToHashCode(int64(id))
	err = model.CreateSurveyGroupRecordTable(groupHashCode)
	if err != nil {
		gLog.Error("SurveyGroupOverwriteSync model.CreateSurveyGroupRecordTable with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	versionData := GenSurveyGroupModel(surveyGroup)

	_, err = model.CreateCmsSurveyGroupVersion(versionData)
	if err != nil {
		gLog.Error("SurveyGroupOverwriteSync CreateCmsSurveyGroupVersion err", xlog.Int64("id", versionData.GroupID), xlog.Err(err))
		return nil, errors.ErrDBOperateFailed
	}

	// 通知C端配置表更了
	// NoticeToClient(ctx, versionData.ClientId, versionData.GroupID)

	gLog.Info("SurveyGroupOverwriteSync overwrite", xlog.Uint64("surveyGroupId", id))
	return nil, nil
}

func GenSurveyGroupModel(surveyInfo *proto.CmsSurveyGroupInfo) model.CmsSurveyGroupVersion {
	versionData := model.CmsSurveyGroupVersion{
		GroupID:   int64(surveyInfo.Id),
		ClientId:  surveyInfo.Clientid,
		Name:      surveyInfo.Name,
		LimitType: surveyInfo.LimitType,
		IsPublish: surveyInfo.IsPublish,
		Type:      surveyInfo.Type,
		Settings:  surveyInfo.Settings,
		HashCode:  surveyInfo.HashCode,
		IsDelete:  surveyInfo.IsDelete,
		Ctime:     surveyInfo.Ctime,
		Mtime:     surveyInfo.Mtime,
		Creator:   surveyInfo.Creator,
		Editor:    surveyInfo.Editor,
	}
	return versionData
}
