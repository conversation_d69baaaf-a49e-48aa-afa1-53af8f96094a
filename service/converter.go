package service

import (
	"github.com/bytedance/sonic"
	"survey/model"
	"survey/proto"
)

func ConvertProtoToModelSetting(setting string) (*model.Setting, error) {

	var protoSetting proto.Setting
	err := sonic.UnmarshalString(setting, &protoSetting)
	if err != nil {
		return nil, err
	}

	modelSetting := &model.Setting{
		BaseRuleConfig:    convertProtoBaseRuleConfigToModel(protoSetting.BaseRuleConfig),
		GiftConfig:        convertProtoGiftConfigToModel(protoSetting.GiftConfig),
		AnswerLimitConfig: convertProtoAnswerLimitConfigToModel(protoSetting.AnswerLimitConfig),
		ZoneIds:           protoSetting.ZoneIds,
		MaterialsConfig:   convertProtoMaterialsConfigToModel(protoSetting.MaterialsConfig),
		FooterConfig:      convertProtoFooterConfigToModel(protoSetting.FooterConfig),
		SourceConfig:      convertProtoSourceConfigToModel(protoSetting.SourceConfig),
	}
	return modelSetting, nil
}

func convertProtoBaseRuleConfigToModel(protoConfig *proto.BaseRuleConfig) *model.BaseRuleConfig {
	if protoConfig == nil {
		return nil
	}

	return &model.BaseRuleConfig{
		ShowQuestionSerialNumber:     false, // Default value, adjust if needed todo需要字段对齐
		AnswerQuestionProcessCanBack: false, // Default value, adjust if needed
		TimeLimitConfig:              convertProtoTimeLimitConfigToModel(protoConfig.TimeLimitConfig),
		ShowLegalPage:                false, // Default value, adjust if needed
		LoginType:                    protoConfig.LoginType,
	}
}

func convertProtoTimeLimitConfigToModel(protoConfig *proto.TimeLimitConfig) model.TimeLimitConfig {
	if protoConfig == nil {
		return model.TimeLimitConfig{}
	}
	return model.TimeLimitConfig{
		IsTimeLimit: protoConfig.IsTimeLimit,
		Stime:       protoConfig.Stime,
		Etime:       protoConfig.Etime,
	}
}

func convertProtoGiftConfigToModel(protoConfig *proto.GiftConfig) *model.GiftConfig {
	if protoConfig == nil {
		return nil
	}
	return &model.GiftConfig{
		IsGiveOutByCms: protoConfig.IsGiveOutByCms,
		GiveOutType:    protoConfig.GiveOutType,
		PreAwardConfig: convertProtoPreAwardConfigToModel(protoConfig.PreAwardConfig),
		RedeemConfig:   protoConfig.RedeemConfig,
	}
}

func convertProtoPreAwardConfigToModel(protoConfig *proto.PreAwardConfig) *model.PreAwardConfig {
	if protoConfig == nil {
		return nil
	}
	return &model.PreAwardConfig{
		Id: protoConfig.Id,
	}
}

func convertProtoAnswerLimitConfigToModel(protoConfig *proto.AnswerLimitConfig) *model.AnswerLimitConfig {
	if protoConfig == nil {
		return nil
	}
	return &model.AnswerLimitConfig{
		LimitType: protoConfig.LimitType,
	}
}

func convertProtoMaterialsConfigToModel(protoConfig *proto.MaterialsConfig) *model.MaterialsConfig {
	if protoConfig == nil {
		return nil
	}
	return &model.MaterialsConfig{
		AutoLatestMaterial: protoConfig.AutoLatestMaterial,
		MaterialVersion:    protoConfig.MaterialVersion,
	}
}

func convertProtoFooterConfigToModel(protoConfig *proto.FooterConfig) *model.FooterConfig {
	if protoConfig == nil {
		return nil
	}
	return &model.FooterConfig{
		Url:  protoConfig.Url,
		Name: protoConfig.Name,
	}
}

func convertProtoSourceConfigToModel(protoConfig *proto.SourceConfig) *model.SourceConfig {
	if protoConfig == nil {
		return nil
	}
	agreements := make([]*model.Agreement, len(protoConfig.Agreements))
	for i, agreement := range protoConfig.Agreements {
		agreements[i] = convertProtoAgreementToModel(agreement)
	}
	return &model.SourceConfig{
		CityUrl:    protoConfig.CityUrl,
		Agreements: agreements,
	}
}

func convertProtoAgreementToModel(protoAgreement *proto.Agreement) *model.Agreement {
	if protoAgreement == nil {
		return nil
	}
	return &model.Agreement{
		Image: protoAgreement.Image,
		Text:  protoAgreement.Text,
		Link:  protoAgreement.Link,
	}
}
