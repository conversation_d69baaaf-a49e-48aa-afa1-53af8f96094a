package service

import (
	"context"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"mime/multipart"
	path2 "path"
	"strings"
	"survey/config"
)

// 上传本地文件到 oss
func UploadFileToOss(from string, to string) (string, error) {
	osscfg := config.Get().OssCfg
	accessKeyId := osscfg.AccessKeyId
	accessKeySecret := osscfg.AccessKeySecret
	cdn := osscfg.Cdn

	// OSS Endpoint 和 Bucket 名称
	endpoint := osscfg.Endpoint
	bucketName := osscfg.Bucket

	// 创建 OSS 客户端对象
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		xlog.Error("Error creating OSS client:", xlog.Err(err))
		return "", err
	}

	// 获取指定 Bucket 对象
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		xlog.Error("Error getting bucket:", xlog.Err(err))
		return "", err
	}

	objectName := strings.TrimLeft(osscfg.BasePath+to, "/")

	err = bucket.PutObjectFromFile(objectName, from)
	if err != nil {
		xlog.Error("Error putObjectFromFile",
			xlog.Err(err),
			xlog.String("objectName", objectName),
			xlog.String("from", from))
		return "", err
	}

	ossUrl := cdn + "/" + objectName

	return ossUrl, nil
}

func UploadToOSS(ctx context.Context, file *multipart.FileHeader) (string, error) {
	osscfg := config.Get().OssCfg
	accessKeyId := osscfg.AccessKeyId
	accessKeySecret := osscfg.AccessKeySecret
	cdn := osscfg.Cdn

	// OSS Endpoint 和 Bucket 名称
	endpoint := osscfg.Endpoint
	bucketName := osscfg.Bucket

	logger := xlog.FromContext(ctx)
	client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	if err != nil {
		logger.Error(" oss New Error:", xlog.Err(err))
		return "", err
	}

	// 指定bucket
	bucket, err := client.Bucket(bucketName) // 根据自己的填写
	if err != nil {
		logger.Error("Bucket Error:", xlog.Err(err))
		return "", err
	}

	src, err := file.Open()
	if err != nil {
		logger.Error("file.Open Error:", xlog.Err(err))
		return "", err
	}
	defer src.Close()

	fileId, err := gonanoid.New(20)
	if err != nil {
		logger.Error("gonanoid.New Error:", xlog.Err(err))
		return "", err
	}

	// 将文件流上传至test目录下
	path := fmt.Sprintf("cms-survey-v2/%s%s", fileId, path2.Ext(file.Filename))
	err = bucket.PutObject(path, src)
	if err != nil {
		logger.Error("PutObject Error:", xlog.Err(err))
		return "", err
	}
	return fmt.Sprintf("%s/%s", cdn, path), nil
}
