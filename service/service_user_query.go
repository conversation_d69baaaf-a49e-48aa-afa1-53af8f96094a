package service

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xnet/xresty"
	"strings"
	"survey/config"
	"survey/constants"
	proto2 "survey/proto"
	"survey/types"
	"survey/util/errors"
	"time"
)

var userQueryTool UserQuery

type UserQuery struct{}

func (_this UserQuery) GetUserClusterStatus(ctx context.Context, requestParam *types.UserQueryClusterStatusReq) (*types.Response[types.UserQueryClusterStatusRes], error) {
	url := _this.getHost() + "/v1/userquery/userpackage/status"
	res := new(types.Response[types.UserQueryClusterStatusRes])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery GetUserClusterStatus fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) SubmitUserPackage(ctx context.Context, requestParam *types.UserQuerySubmitUserPackageReq) (*types.Response[types.UserQuerySubmitUserPackageRes], error) {
	url := _this.getHost() + "/v1/userquery/userpackage/submit"
	res := new(types.Response[types.UserQuerySubmitUserPackageRes])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery SubmitUserPackage fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) IsUserExistInCluster(ctx context.Context, requestParam *types.UserQueryClusterExistReq) (*types.Response[types.UserQueryClusterExistRes], error) {
	url := _this.getHost() + "/v1/userquery/cluster/exist"
	res := new(types.Response[types.UserQueryClusterExistRes])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery IsUserExistInCluster fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) CheckUserClusterStatus(ctx context.Context, clientID int64, clusterList []*types.UserCluster) (map[int]error, error) {
	// 查询分群同步任务状态
	var clusterErrMap = make(map[int]error)
	for idx, cluster := range clusterList {
		if cluster == nil || cluster.ClusterInfo == nil ||
			cluster.ClusterInfo.ClusterName == "" || !_this.CheckEntityValid(cluster.ClusterInfo.EntityName) || cluster.Relation < 1 {
			clusterErrMap[idx] = errors.ErrSurveyExportUserClusterInfoErr
			continue
		}

		req := &types.UserQueryClusterStatusReq{Packages: []*types.UserQueryClusterStatusReqItem{
			{
				Key:      userQueryTool.GetPackageKey(cluster.ClusterInfo.ClusterName),
				ClientID: clientID,
				BizType:  constants.UserQueryBiType,
				BizId:    time.Now().Unix(),
			},
		}}

		statusRes, err := userQueryTool.GetUserClusterStatus(ctx, req)

		if err != nil {
			clusterErrMap[idx] = errors.Wrap(errors.ErrSurveyExportUserClusterHttpErr, err)
			continue
		}

		if statusRes == nil || statusRes.Code != 0 {
			clusterErrMap[idx] = errors.ErrSurveyExportUserClusterHttpResErr
			continue
		}

		// 检测任务创建与否
		if len(statusRes.Data.List) == 0 {
			clusterErrMap[idx] = errors.ErrSurveyExportUserClusterTaskLost
			continue
		}

		//user-query 会返回类似如下空结构
		//{
		//    "code": 0,
		//    "info": "OK",
		//    "request_id": "25672047-8015-96c7-be22-33f510d389dd",
		//    "data": {
		//        "list": [
		//            {}
		//        ]
		//    }
		//}

		for _, item := range statusRes.Data.List {
			if item.ClientID <= 0 { // 具体如上面注释
				clusterErrMap[idx] = errors.ErrSurveyExportUserClusterTaskLost
				continue
			}
			if item.Status != constants.BiUserClusterStatusCompleted {
				clusterErrMap[idx] = errors.ErrSurveyExportUserClusterTaskNotFinish
				continue
			}
		}
	}

	for _, err := range clusterErrMap {
		if err != nil {
			return clusterErrMap, err
		}
	}

	return clusterErrMap, nil
}

func (_this UserQuery) CheckUserClusterExist(ctx context.Context, clientID int64, cluster *proto2.UserStrategy) (bool, error) {
	var exist bool

	if cluster == nil || cluster.ClusterName == "" || !_this.CheckEntityValid(cluster.EntityName) {
		return exist, errors.ErrSurveyExportUserClusterInfoErr
	}

	req := &types.UserQueryClusterStatusReq{Packages: []*types.UserQueryClusterStatusReqItem{
		{
			Key:      userQueryTool.GetPackageKey(cluster.ClusterName),
			ClientID: clientID,
			BizType:  constants.UserQueryBiType,
			BizId:    time.Now().Unix(),
		},
	}}

	statusRes, err := userQueryTool.GetUserClusterStatus(ctx, req)

	if err != nil {
		return exist, errors.Wrap(errors.ErrSurveyExportUserClusterHttpErr, err)
	}

	if statusRes == nil || statusRes.Code != 0 {
		err = fmt.Errorf("user-query response code:%+v info:%s request-id:%s", statusRes.Code, statusRes.Info, statusRes.RequestId)
		return exist, errors.Wrap(errors.ErrSurveyExportUserClusterHttpResErr, err)
	}

	// 检测任务创建与否
	if len(statusRes.Data.List) > 0 {
		for _, item := range statusRes.Data.List {
			if item.ClientID > 0 {
				exist = true
				return exist, nil
			}
		}
	}

	return exist, nil
}

func (_this UserQuery) SubmitUserTag(ctx context.Context, requestParam *types.UserQuerySubmitUserTagReq) (*types.Response[types.Empty], error) {
	url := _this.getHost() + "/v1/userquery/usertag/submit"
	res := new(types.Response[types.Empty])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery SubmitUserPackage fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) GetUserTagStatus(ctx context.Context, requestParam *types.UserQueryTagStatusReq) (*types.Response[types.UserQueryTagStatusRes], error) {
	url := _this.getHost() + "/v1/userquery/tag/status"
	res := new(types.Response[types.UserQueryTagStatusRes])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery GetUserTagStatus fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) GetUserTagList(ctx context.Context, requestParam *types.UserQueryTagListReq) (*types.Response[types.UserQueryTagListRes], error) {
	url := _this.getHost() + "/v1/userquery/tag/get"
	res := new(types.Response[types.UserQueryTagListRes])
	result, err := xresty.New().R().SetResult(res).SetBody(requestParam).Post(url)
	if err != nil {
		xlog.FromContext(ctx).Error("UserQuery GetUserTagList fail",
			xlog.Err(err),
			xlog.Any("response", result),
			xlog.Int("statusCode", result.StatusCode()),
			xlog.String("request-id", res.RequestId),
		)
		return res, err
	}
	_this.successLog(ctx, url, requestParam, result.String(), result.StatusCode())
	return res, nil
}

func (_this UserQuery) CheckUserTagStatus(ctx context.Context, clientID int64, tagList []*types.UserTag) (map[int]error, error) {
	// 查询标签同步任务状态
	var tagErrMap = make(map[int]error)
	for idx, tag := range tagList {
		if tag == nil || tag.TagInfo == nil ||
			tag.TagInfo.TagName == "" || !_this.CheckEntityValid(tag.TagInfo.TagEntityName) || tag.Relation < 1 {
			tagErrMap[idx] = errors.ErrSurveyExportUserTagInfoErr
			continue
		}

		req := &types.UserQueryTagStatusReq{Tags: []*types.UserQueryTagStatusItem{
			{
				ClientId: clientID,
				TagName:  tag.TagInfo.TagName,
				BizId:    time.Now().Unix(),
				BizType:  constants.UserQueryBiType,
			},
		}}

		statusRes, err := userQueryTool.GetUserTagStatus(ctx, req)

		if err != nil {
			tagErrMap[idx] = errors.Wrap(errors.ErrSurveyExportUserTagHttpErr, err)
			continue
		}

		if statusRes == nil || statusRes.Code != 0 {
			tagErrMap[idx] = errors.ErrSurveyExportUserTagHttpResErr
			continue
		}

		// 检测任务创建与否
		if len(statusRes.Data.List) == 0 {
			tagErrMap[idx] = errors.ErrSurveyExportUserTagTaskLost
			continue
		}

		for _, item := range statusRes.Data.List {
			if item.ClientId <= 0 {
				tagErrMap[idx] = errors.ErrSurveyExportUserTagTaskLost
				continue
			}
			if item.Status != constants.BiUserClusterStatusCompleted {
				tagErrMap[idx] = errors.ErrSurveyExportUserTagTaskNotFinish
				continue
			}
		}
	}

	for _, err := range tagErrMap {
		if err != nil {
			return tagErrMap, err
		}
	}

	return tagErrMap, nil
}

func (_this UserQuery) getHost() string {
	return strings.Trim(config.Get().UserQuery.Host, "/")
}

func (_this UserQuery) GetPackageKey(clusterName string) string {
	return "cluster-" + clusterName
}

func (_this UserQuery) CheckEntityValid(entity string) bool {
	validMap := map[string]bool{
		constants.UserClusterEntityRoleID: true,
		constants.UserClusterEntityNID:    true,
	}
	return validMap[entity]
}

func (_this UserQuery) successLog(ctx context.Context, url string, req any, resStr string, code int) {
	xlog.FromContext(ctx).Info("UserQuery success",
		xlog.String("url", url),
		xlog.Any("request", req),
		xlog.String("response", resStr),
		xlog.Int("statusCode", code),
	)
}
