package types

type Setting struct {
	BaseRuleConfig    *BaseRuleConfig    `protobuf:"bytes,1,opt,name=baseRuleConfig,proto3" json:"baseRuleConfig,omitempty"`
	GiftConfig        *GiftConfig        `protobuf:"bytes,2,opt,name=giftConfig,proto3" json:"giftConfig,omitempty"`
	AnswerLimitConfig *AnswerLimitConfig `protobuf:"bytes,3,opt,name=answerLimitConfig,proto3" json:"answerLimitConfig,omitempty"`
	ZoneIds           []int64            `protobuf:"varint,4,rep,packed,name=zoneIds,proto3" json:"zoneIds,omitempty"`
	MaterialsConfig   *MaterialsConfig   `protobuf:"bytes,5,opt,name=materialsConfig,proto3" json:"materialsConfig,omitempty"`
	FooterConfig      *FooterConfig      `protobuf:"bytes,6,opt,name=footerConfig,proto3" json:"footerConfig,omitempty"`
	SourceConfig      *SourceConfig      `protobuf:"bytes,7,opt,name=sourceConfig,proto3" json:"sourceConfig,omitempty"`
}

type AnswerTimesConfig struct {
	LimitType int32 `protobuf:"varint,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
	Times     int32 `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
}

type FooterConfig struct {
	Url  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

type SourceConfig struct {
	CityUrl    string       `protobuf:"bytes,1,opt,name=cityUrl,proto3" json:"cityUrl,omitempty"`
	Agreements []*Agreement `protobuf:"bytes,2,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

type Agreement struct {
	Image string `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Text  string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Link  string `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
}
