package types

import (
	"survey/proto"
)

type Survey struct {
	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:主键ID"`
	// client_id
	ClientId string `protobuf:"bytes,2,opt,name=client_id,proto3" json:"client_id" gorm:"type:varchar(128);comment:client_id"`
	// 问卷名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name" gorm:"type:varchar(128);comment:问卷名称"`
	// 问卷是否打开
	IsClosed int64 `protobuf:"varint,4,opt,name=is_closed,json=isClosed,proto3" json:"is_closed" gorm:"type:int(8);comment:问卷是否打开：（0: 打开，1: 关闭）"`
	// 问卷是否暂停
	IsPause int64 `protobuf:"varint,5,opt,name=is_pause,json=isPause,proto3" json:"is_pause" gorm:"type:int(8);comment:问卷是否暂停：（0: 不暂停，1: 暂停）"`
	// 是否发布
	IsPublish int32 `protobuf:"varint,6,opt,name=is_publish,json=isPublish,proto3" json:"is_publish" gorm:"type:tinyint(4);comment:是否发布（0: 未发布，1: 已发布）"`
	// 是否有修改未发布
	IsModifyUnpublish int32 `protobuf:"varint,7,opt,name=is_modify_unpublish,json=isModifyUnpublish,proto3" json:"is_modify_unpublish" gorm:"type:tinyint(4);comment:是否有修改未发布（0: 无，1: 有）"`
	// 是否开启过答题
	IsOpened int32 `protobuf:"varint,8,opt,name=is_opened,json=isOpened,proto3" json:"is_opened" gorm:"type:tinyint(4);comment:是否开启过答题（0: 未开启过，1: 开启过）"`
	// 问卷开始时间
	Stime string `protobuf:"bytes,9,opt,name=stime,proto3" json:"stime"`
	// 问卷结束时间
	Etime string `protobuf:"bytes,10,opt,name=etime,proto3" json:"etime"`
	// 问卷类型
	Type string `protobuf:"bytes,11,opt,name=type,proto3" json:"type" gorm:"type:int(8);comment:问卷类型"`
	// 配置 schema
	Schema Schema `protobuf:"bytes,12,opt,name=schema,proto3" json:"schema"`
	// 预览 schema todo gernate preview schema
	PreviewSchema Schema `protobuf:"bytes,13,opt,name=preview_schema,json=previewSchema,proto3" json:"preview_schema"`
	// 问卷设置
	Settings *proto.Setting `protobuf:"bytes,14,opt,name=settings,proto3" json:"settings" gorm:"json;comment:问卷设置"`
	// 问卷C端用到的配置
	WebSettings string `protobuf:"bytes,15,opt,name=web_settings,json=webSettings,proto3" json:"web_settings" gorm:"type:varchar;comment:问卷C端用到的配置"`
	// 问卷C端用到的配置
	Languages string `protobuf:"bytes,16,opt,name=languages,proto3" json:"languages" gorm:"type:varchar;comment:语言包"`
	// 问卷id-hash
	HashCode string `protobuf:"bytes,17,opt,name=hash_code,json=hashCode,proto3" json:"hash_code" gorm:"type:varchar;comment:问卷id-hash"`
	// 是否删除
	IsDelete int32 `protobuf:"varint,18,opt,name=is_delete,json=isDelete,proto3" json:"is_delete" gorm:"type:tinyint(4);comment:是否删除（0: 未删除，1: 已删除）"`
	// 删除时间
	Deltime string `protobuf:"bytes,19,opt,name=deltime,proto3" json:"deltime" gorm:"type:varchar;comment:删除时间"`
	// 备注
	Remark string `protobuf:"bytes,20,opt,name=remark,proto3" json:"remark" gorm:"type:varchar;comment:备注"`
	Ctime  string `protobuf:"bytes,21,opt,name=ctime,proto3" json:"ctime,omitempty" gorm:"autoCtime"`
	Mtime  string `protobuf:"bytes,22,opt,name=mtime,proto3" json:"mtime,omitempty" gorm:"autoMtime"`
	// 创建人
	Creator string `protobuf:"bytes,23,opt,name=creator,proto3" json:"creator" gorm:"type:varchar(64);comment:创建人"`
	// 最近修改人
	Editor string `protobuf:"bytes,24,opt,name=editor,proto3" json:"editor" gorm:"type:varchar(64);comment:最近修改人"`
	// isTimeLimit
	IsTimeLimit bool `protobuf:"varint,25,opt,name=isTimeLimit,proto3" json:"isTimeLimit" gorm:"-"`
	// 用户答卷数量
	AllAnsweredUserCount int64 `protobuf:"varint,26,opt,name=allAnsweredUserCount,proto3" json:"allAnsweredUserCount" gorm:"-"`
	// 状态
	Status int32 `protobuf:"varint,27,opt,name=status,proto3" json:"status" gorm:"-"`
	// 答卷统计
	FullValidUid int64 `protobuf:"varint,28,opt,name=fullValidUid,proto3" json:"fullValidUid" gorm:"-"`
	//QuestionStatisticsData *QuestionStatisticsData `protobuf:"bytes,29,opt,name=questionStatisticsData,proto3" json:"questionStatisticsData" gorm:"-"`
}

type SurveyQuestionStatsDetail struct {
	Value         string `json:"value"`
	Label         Label  `json:"label"`
	ImagePosition int    `json:"image_position"`
	BlankFill     string `json:"blank_fill"`
	Image         Image  `json:"image"`
	Count         int    `json:"count"`
	Proportion    string `json:"proportion"`
}

type SurveyQuestionStats struct {
	UniqueKey   string                      `json:"uniqueKey"`
	Count       string                      `json:"count"`
	SelectCount int                         `json:"selectCount"`
	Detail      []SurveyQuestionStatsDetail `json:"detail"`
}

type ExtraData struct {
	HeaderContent interface{} `json:"headerContent"` // string or Label
	FooterContent interface{} `json:"footerContent"` // string or Label
}

type SurveyQuestionStatsWithExtra struct {
	ExtraData   *ExtraData `json:"extraData,omitempty"`
	Count       int64      `json:"count"`
	SelectCount int        `json:"selectCount"`
	Detail      []Detail   `json:"detail"`
	UniqueKey   string     `json:"uniqueKey"`
	Score       string     `json:"score,omitempty"`
}

type Detail struct {
	SelectOption
	Value      interface{} `json:"value"` // int 或者 字符串("value|idx")
	Count      int         `json:"count"`
	Proportion string      `json:"proportion"`
	ValueTmp   string      `json:"$value,omitempty"`
	Weight     int         `json:"weight,omitempty"`
}

type QuestionStatisticsData struct {
	ValidAnswerTotals int `json:"validAnswerTotals"`
	ValidUserTotals   int `json:"validUserTotals"`
	//Detail            []SurveyQuestionStatsWithExtra `json:"detail"`
	Detail interface{} `json:"detail"` // []SurveyQuestionStatsWithExtra 或 SurveyQuestionStatsWithExtra 或 nil
}

type SurveyResult struct {
	QuestionStatisticsData QuestionStatisticsData `json:"questionStatisticsData"`
}

type ConfigProps struct {
	//IsQuestion       bool   `json:"isQuestion"`
	//QuestionType     string `json:"questionType"`
	//ValueType        string `json:"valueType"`
	//UniqueKey        string `json:"uniqueKey"`
	//QuestionId       int    `json:"questionId"`
	//StatisticsMethod string `json:"statisticsMethod"`

	IsQuestion       bool        `json:"isQuestion,omitempty"`
	IsMatrix         bool        `json:"isMatrix,omitempty"`
	IsGauge          bool        `json:"isGauge,omitempty"`
	IsOrder          bool        `json:"isOrder,omitempty"`
	IsAddress        bool        `json:"isAddress,omitempty"`
	HasFillFree      bool        `json:"hasFillFree,omitempty"`
	IsRate           bool        `json:"isRate,omitempty"`
	QuestionId       interface{} `json:"questionId,omitempty"`
	QuestionType     string      `json:"questionType,omitempty"` // 'select' | 'input' | 'submit'
	SelectMode       string      `json:"selectMode,omitempty"`   // 'multi' | 'single'
	UniqueKey        string      `json:"uniqueKey,omitempty"`
	StatisticsMethod string      `json:"statisticsMethod,omitempty"` // 'gauge' | 'matrix' | 'order' | 'select' | 'input'
	ValueType        string      `json:"valueType,omitempty"`
	ParentQuestionId interface{} `json:"-"`
	ParentUniqueKey  string      `json:"-"`
}

type QuestionBaseConfig struct {
	QuestionTitle interface{} `json:"question_title"` // string or Label
	QuestionDesc  interface{} `json:"question_desc"`  // string or Label
	QuestionTip   interface{} `json:"question_tip"`   // string or Label
}

type DisplayLogic struct {
	CurrentQuestionId string  `json:"currentQuestionId"`
	Rules             []Rules `json:"rules"`
	Relation          string  `json:"relation"`
}

type Rules struct {
	Key                  string               `json:"key"`
	ID                   string               `json:"id"`
	RelatedContentConfig RelatedContentConfig `json:"relatedContentConfig"`
}

type RelatedContentConfig struct {
	Relation   string `json:"relation"`
	ChooseType string `json:"chooseType"`
	//Content    []ContentRelatedContentConfig `json:"content"`
	//Content []string `json:"content"`
	Content interface{} `json:"content"`
}

type ContentRelatedContentConfig struct {
	Content []int `json:"content"`
}

type QuestionComponentConfig struct {
	HeaderContent         interface{}    `json:"headerContent"` // string or Label
	FooterContent         interface{}    `json:"footerContent"` // string or Label
	Dimension             int            `json:"dimension"`
	StartValue            int            `json:"startValue"`
	Sort                  int            `json:"sort"`
	QuestionTitle         Label          `json:"question_title"`
	RowTitleSelectOptions []SelectOption `json:"row_title_select_options"`
	ScoreType             int32          `json:"scoreType"`
	AllowHalf             bool           `json:"allowHalf"`
	Count                 int            `json:"count"`
	SelectOptions         []SelectOption `json:"select_options,omitempty"`
}

type RowTitleSelectOptions struct {
	Label     Label     `json:"label"`
	Value     string    `json:"value"`
	BlankFill BlankFill `json:"blank_fill,omitempty"`
}

type QuestionSelectConfig struct {
	MaxSelected           int            `json:"max_selected,omitempty"`
	SelectOptions         []SelectOption `json:"select_options,omitempty"`
	SelectItemLayout      bool           `json:"select_item_layout_horizontal,omitempty"`
	SelectItemRandomSort  bool           `json:"select_item_random_sort,omitempty"`
	RowTitleSelectOptions interface{}    `json:"row_title_select_options,omitempty"`
	FillFreeConfig        struct {
		Show  bool   `json:"show"`
		Value string `json:"value"`
		Label string `json:"label"`
	} `json:"fill_free_config,omitempty"`
	RowTitleSelectOptionsV2 []RowTitleSelectOptions `json:"rowTitleSelectOptionsv2,omitempty"`
}

type SelectOption struct {
	Value         string      `json:"value,omitempty"`
	Label         interface{} `json:"label,omitempty"` // string or label
	ImagePosition int         `json:"image_position"`
	BlankFill     interface{} `json:"blank_fill"`      // string or BlankFill string主要是空字符串时候
	Image         interface{} `json:"image,omitempty"` // string or Image
	Mutex         bool        `json:"mutex,omitempty"` // Mutex only present in some options
	NewValue      string      `json:"newValue"`
}

type Image struct {
	LanguKey string `json:"languKey"`
	Value    string `json:"value"`
}

type BlankFill struct {
	Allow bool `json:"allow"`
}

type Label struct {
	Value    string `json:"value"`
	LanguKey string `json:"languKey"`
}

// 定义题目列表
type QuestionList struct {
	ComponentName           string                  `json:"componentName"`
	ComponentTitle          string                  `json:"componentTitle"`
	QuestionBaseConfig      QuestionBaseConfig      `json:"question_base_config"`
	ConfigProps             ConfigProps             `json:"configProps"`
	QuestionLogicalConfig   QuestionLogicalConfig   `json:"question_logical_config"`
	QuestionComponentConfig QuestionComponentConfig `json:"question_component_config,omitempty"`
	QuestionSelectConfig    QuestionSelectConfig    `json:"question_select_config,omitempty"`
	RequestConfig           RequestConfig           `json:"request_config"`
	WrapperIndex            int                     `json:"wrapperIndex"`
}

type QuestionLogicalConfig struct {
	//VisibleSwitch    VisibleSwitch      `json:"visible_switch"`
	//ConditionDesc    string             `json:"condition_desc"`
	//DisplayLogic     DisplayLogic       `json:"display_logic"`
	//SkipQuestion     SkipQuestionConfig `json:"skip_question"`
	VisibleSwitch    interface{}      `json:"visible_switch"`
	ConditionDesc    interface{}      `json:"condition_desc"`
	DisplayLogic     interface{}      `json:"display_logic"`
	SkipQuestion     interface{}      `json:"skip_question"`
	OptionsReference OptionsReference `json:"options_reference"`
}

type VisibleSwitch struct {
}

type OptionsReference struct {
	CurrentQuestionId           string         `json:"currentQuestionId,omitempty"`
	RelationQuestionId          string         `json:"relationQuestionId,omitempty"`
	RelationSelectOptionsConfig []SelectOption `json:"relationSelectOptionsConfig"`
}

type RelationSelectOptionsConfigItem struct {
	Value    string      `json:"value"`
	Label    interface{} `json:"label"` // string or Label
	NewValue string      `json:"newValue"`
}

type SkipQuestionConfig struct {
	Source string `json:"source"`
	Type   string `json:"type"`
	//Config ConfigSkipQuestion `json:"config"`
	//Config []ConfigSkipQuestion `json:"config"`
	Config interface{} `json:"config"`
}

type ConfigSkipQuestion struct {
	Select string `json:"select"`
	Target string `json:"target"`
}

// SurveyConfig 定义问卷配置的结构体
type SurveyConfig struct {
	ID                     int64                  `json:"id"`
	ClientID               string                 `json:"client_id"`
	Name                   string                 `json:"name"`
	IsClosed               int32                  `json:"isClosed"`
	IsPause                int32                  `json:"isPause"`
	IsModifyUnpublish      int32                  `json:"isModifyUnpublish"`
	IsOpened               int32                  `json:"isOpened"`
	STime                  string                 `json:"stime"`
	ETime                  string                 `json:"etime"`
	Type                   int64                  `json:"type"`
	Schema                 Schema                 `json:"schema"`
	Settings               Settings               `json:"settings"`
	WebSettings            WebSettings            `json:"webSettings"`
	Languages              string                 `json:"languages"`
	HashCode               string                 `json:"hashCode"`
	IsDelete               int32                  `json:"isDelete"`
	Deltime                string                 `json:"deltime"`
	Remark                 string                 `json:"remark"`
	Ctime                  string                 `json:"ctime"`
	Mtime                  string                 `json:"mtime"`
	Creator                string                 `json:"creator"`
	Editor                 string                 `json:"editor"`
	IsTimeLimit            bool                   `json:"isTimeLimit"`
	WebPathList            []WebPath              `json:"webPathList"`
	Status                 int                    `json:"status"`
	QuestionStatisticsData QuestionStatisticsData `json:"questionStatisticsData"`
}

type Settings struct {
	BaseRuleConfig    BaseRuleConfig    `json:"baseRuleConfig"`
	GiftConfig        GiftConfig        `json:"giftConfig"`
	AnswerLimitConfig AnswerLimitConfig `json:"answerLimitConfig"`
	MaterialsConfig   MaterialsConfig   `json:"materialsConfig"`
	FooterConfig      FooterConfig      `json:"footerConfig"`
	SourceConfig      SourceConfig      `json:"sourceConfig"`
}

type WebSettings struct {
	LoginType       string          `json:"loginType"`
	IsEndPreview    bool            `json:"isEndPreview"`
	IsGoOnAnswer    bool            `json:"isGoOnAnswer"`
	LanguageList    []string        `json:"languageList"`
	MaterialsConfig MaterialsConfig `json:"materialsConfig"`
	FooterConfig    FooterConfig    `json:"footerConfig"`
	SourceConfig    SourceConfig    `json:"sourceConfig"`
}

// Component 定义问卷组件的通用结构体
type Component struct {
	Package       string `json:"package"`
	Version       string `json:"version"`
	ExportName    string `json:"exportName"`
	Main          string `json:"main"`
	Destructuring bool   `json:"destructuring"`
	SubName       string `json:"subName"`
	ComponentName string `json:"componentName"`
	DevMode       string `json:"devMode,omitempty"`
}

// ComponentTree 定义问卷组件树的结构体
type ComponentTree struct {
	ComponentName  string          `json:"componentName"`
	ID             string          `json:"id"`
	DocId          string          `json:"docId"`
	Props          ConfigProps     `json:"props"`
	Meta           MetaTree        `json:"meta"`
	FileName       string          `json:"fileName"`
	Hidden         bool            `json:"hidden"`
	Title          string          `json:"title"`
	IsLocked       bool            `json:"isLocked"`
	Condition      bool            `json:"condition"`
	ConditionGroup string          `json:"conditionGroup"`
	Children       []ComponentTree `json:"children"`
	Parent         Parent          `json:"parent"`
}

type RequestConfig struct {
	QuestionUniqueKey string      `json:"questionUniqueKey,omitempty"`
	ConfigProps       ConfigProps `json:"configProps,omitempty"`
}

type QuestionConfig struct {
	RequestConfig RequestConfig `json:"request_config"`
}

type PreviewSchema struct {
	Version        string            `json:"version"`
	ComponentsMap  []Component       `json:"componentsMap"`
	ComponentsTree []Component       `json:"componentsTree"`
	I18n           map[string]string `json:"i18n"`
	Config         Config            `json:"config"`
	Meta           Meta              `json:"meta"`
	Clientid       string            `json:"client_id"`
}

type SurveyConfigNewSchema struct {
	Version        string                `json:"version,omitempty"`
	ComponentsMap  []ComponentsMap       `json:"componentsMap,omitempty"`
	ComponentsTree []ComponentsTree      `json:"componentsTree,omitempty"`
	I18N           I18N                  `json:"i18n,omitempty"`
	Config         ConfigWithPropsLayout `json:"config,omitempty"`
	Meta           Meta                  `json:"meta,omitempty"`
	ClientId       string                `json:"clientid,omitempty"`
}

// SurveyConfigNew 定义问卷配置的结构体
type SurveyConfigNew struct {
	ID                int64       `json:"id"`
	ClientID          string      `json:"clientid"` // nodejs是这样的
	Name              string      `json:"name"`
	IsClosed          int32       `json:"isClosed"`
	IsPause           int32       `json:"isPause"`
	IsModifyUnpublish int32       `json:"isModifyUnpublish"`
	IsOpened          int32       `json:"isOpened"`
	STime             string      `json:"stime"`
	ETime             string      `json:"etime"`
	Type              int32       `json:"type"`
	Schema            interface{} `json:"schema"`
	//Schema                 SurveyConfigNewSchema         `json:"schema"`
	//Settings               *proto.Setting                `json:"settings"`
	Settings               interface{}                   `json:"settings"`
	WebSettings            string                        `json:"webSettings"`
	Languages              interface{}                   `json:"languages"`
	HashCode               string                        `json:"hashCode"`
	IsDelete               int32                         `json:"isDelete"`
	Deltime                string                        `json:"deltime"`
	Remark                 string                        `json:"remark"`
	Ctime                  string                        `json:"ctime"`
	Mtime                  string                        `json:"mtime"`
	Creator                string                        `json:"creator"`
	Editor                 string                        `json:"editor"`
	IsTimeLimit            bool                          `json:"isTimeLimit"`
	WebPathList            []*proto.WebPath              `json:"webPathList"`
	Status                 int32                         `json:"status"`
	Font                   string                        `json:"font"`
	KeyValue               string                        `json:"keyValue"`
	PreviewSchema          string                        `json:"previewSchema"`
	ApiVersion             int32                         `json:"apiVersion"`
	QuestionStatisticsData *proto.QuestionStatisticsData `json:"questionStatisticsData"`
}

type Empty struct {
}

type SurveyDetailStatisticModel struct {
	Question    []byte
	Option      []byte
	Text        string //SurveyID           int64      `gorm:"column:survey_id"`
	OptionCount uint64
}

type SurveyDetailSqlStatisticResult struct {
	ModelData  *SurveyDetailStatisticModel
	Question   string
	Option     string
	Count      uint64
	GroupCount int64
}

//type NewDetail struct {
//	Detail
//	ValueTemp string `json:"$value"`
//	Weight    int    `json:"weight"`
//}

type ExportSurveyRecordAndDetailItem struct {
	ID             uint32   `gorm:"column:id" ch:"survey_record_id"`
	UID            string   `gorm:"column:uid" ch:"uid"`
	RoleID         string   `gorm:"column:role_id" ch:"roleid"`
	OpenId         string   `gorm:"column:openid" ch:"openid"`
	DeviceId       string   `gorm:"column:device_id" ch:"device_id"`
	IP             string   `gorm:"column:ip" ch:"ip"`
	IsValid        uint32   `gorm:"column:is_valid" ch:"is_valid"`
	BeginTime      string   `gorm:"column:begin_time" ch:"begin_time"`
	EndTime        string   `gorm:"column:end_time" ch:"end_time"`
	Extra          string   `gorm:"column:extra" ch:"extra"`
	Ctime          string   `gorm:"column:ctime" ch:"ctime"`
	RecordQuestion string   `gorm:"column:record_question"`
	RecordOption   string   `gorm:"column:record_option"`
	RecordText     string   `gorm:"column:record_text"`
	Questions      []string `gorm:"-" ch:"questions"`
	Options        []string `gorm:"-" ch:"options"`
	Texts          []string `gorm:"-" ch:"texts"`
	Location       string   `gorm:"-" ch:"location"`
	PlatID         string   `gorm:"-" ch:"record_ext"`
}

type ExportDetailItem struct {
	Option string
	Text   string
}

type ExportFormatRecordData struct {
	UserRecord   *ExportSurveyRecordAndDetailItem
	AnswerTime   string
	RecordDetail map[string][]ExportDetailItem
}

type ExportQuestionSelectOptionHandleParam struct {
	QuestionItem     QuestionList
	RecordDetailList []ExportDetailItem
	SelectedItemFunc func(option string, record string) bool
	DataType         int32
	DataType0Func    func(index int) int
	NeedBlank        bool
	IsRate           bool
	IsKeepImageTag   bool
	IsFirst          bool
	IsSingle         bool
}

type SurveyRecordBlacklist struct {
	EventID        int64  `json:"eventid"` // 事件类型  固定纳秒时间戳
	ClientId       string `json:"client_id"`
	SurveyId       int64  `json:"survey_id"`
	SurveyRecordId int64  `json:"survey_record_id"`
	CreateTime     string `json:"createtime"`  // 事件发生时间  2006-01-02 15:04:05
	PartEvent      string `json:"part_event"`  // 事件类型  固定"survey_record_blacklist"
	DtEventTime    string `json:"DtEventTime"` // 事件发生时间 2006-01-02 15:04:05
}

type SurveySetting struct {
	BaseRuleConfig    any                     `json:"baseRuleConfig"`
	GiftConfig        SurveySettingGiftConfig `json:"giftConfig"`
	AnswerLimitConfig any                     `json:"answerLimitConfig"`
	MaterialsConfig   any                     `json:"materialsConfig"`
}

type SurveySettingGiftConfig struct {
	IsGiveOutByCms  bool   `json:"isGiveOutByCms"`
	GiveOutType     string `json:"giveOutType"`
	PreAwardConfig  any    `json:"preAwardConfig"`
	RedeemConfig    any    `json:"redeemConfig"`
	PushAwardConfig any    `json:"pushAwardConfig"`
}
