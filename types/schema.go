package types

type Schema struct {
	Version        string           `json:"version,omitempty"`
	ComponentsMap  []ComponentsMap  `json:"componentsMap,omitempty"`
	ComponentsTree []ComponentsTree `json:"componentsTree,omitempty"`
	I18N           I18N             `json:"i18n,omitempty"`
	Config         Config           `json:"config,omitempty"`
	Meta           Meta             `json:"meta,omitempty"`
	Clientid       string           `json:"clientid,omitempty"`
}

type ComponentsMap struct {
	Package       string `json:"package,omitempty"`
	Version       string `json:"version,omitempty"`
	ExportName    string `json:"exportName,omitempty"`
	Main          string `json:"main,omitempty"`
	Destructuring bool   `json:"destructuring,omitempty"`
	SubName       string `json:"subName,omitempty"`
	ComponentName string `json:"componentName,omitempty"`
	DevMode       string `json:"devMode,omitempty"`
}
type SurveyBaseRule struct {
	ShowQuestionSerialNumber     bool `json:"showQuestionSerialNumber"`
	AnswerQuestionProcessCanBack bool `json:"answerQuestionProcessCanBack"`
}

type Description struct {
	Value    string `json:"value"`
	LanguKey string `json:"languKey"`
}

type ExtraProps map[string]interface{}

type Props struct {
	WrapperMainTitle        string                  `json:"wrapperMainTitle,omitempty"`
	WrapperSubTitle         string                  `json:"wrapperSubTitle,omitempty"`
	SurveyBaseRule          SurveyBaseRule          `json:"surveyBaseRule,omitempty"`
	SkinConfig              SkinConfig              `json:"skinConfig,omitempty"`
	SettingsProps           SettingsProps           `json:"settingsProps,omitempty"`
	Clientid                string                  `json:"clientid,omitempty"`
	QuestionComponentConfig QuestionComponentConfig `json:"question_component_config,omitempty"`
	ConfigProps             ConfigProps             `json:"configProps,omitempty"`
	ExtraProps              ExtraProps              `json:"extraProps,omitempty"`
	QuestionLogicalConfig   QuestionLogicalConfig   `json:"question_logical_config,omitempty"`
	QuestionBaseConfig      QuestionBaseConfig      `json:"question_base_config,omitempty"`
	QuestionSelectConfig    QuestionSelectConfig    `json:"question_select_config,omitempty"`
}

type MetaTree struct {
	Title  string `json:"title,omitempty"`
	Router string `json:"router,omitempty"`
}
type SurveySettings struct {
	IsCustomSkinURL              string `json:"isCustomSkinUrl,omitempty"`
	SkinURL                      string `json:"skinUrl,omitempty"`
	ShowQuestionSerialNumber     bool   `json:"showQuestionSerialNumber,omitempty"`
	AnswerQuestionProcessCanBack bool   `json:"answerQuestionProcessCanBack,omitempty"`
}
type TimeLimitConfig struct {
	IsTimeLimit bool   `json:"isTimeLimit,omitempty"`
	Stime       string `json:"stime,omitempty"`
	Etime       string `json:"etime,omitempty"`
}
type BaseRuleConfig struct {
	ShowQuestionSerialNumber     bool            `json:"showQuestionSerialNumber,omitempty"`
	AnswerQuestionProcessCanBack bool            `json:"answerQuestionProcessCanBack,omitempty"`
	TimeLimitConfig              TimeLimitConfig `json:"timeLimitConfig,omitempty"`
	ShowLegalPage                bool            `json:"showLegalPage,omitempty"`
	LoginType                    string          `json:"loginType,omitempty"`
}
type SkinConfig struct {
	IsCustomSkinURL string `json:"isCustomSkinUrl,omitempty"`
	SkinURL         string `json:"skinUrl,omitempty"`
	BgColor         string `json:"bgColor,omitempty"`
}
type MaterialsConfig struct {
	AutoLatestMaterial bool   `json:"autoLatestMaterial,omitempty"`
	MaterialVersion    string `json:"materialVersion,omitempty"`
}
type AnswerLimitConfig struct {
	LimitType string `json:"limitType,omitempty"`
}
type GiftConfig struct {
	IsGiveOutByCms bool            `json:"isGiveOutByCms,omitempty"`
	GiveOutType    string          `json:"giveOutType,omitempty"`
	PreAwardConfig *PreAwardConfig `json:"preAwardConfig,omitempty"`
	RedeemConfig   interface{}     `json:"redeemConfig,omitempty"`
}
type PreAwardConfig struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}
type SettingsProps struct {
	BaseRuleConfig    BaseRuleConfig    `json:"baseRuleConfig,omitempty"`
	SkinConfig        SkinConfig        `json:"skinConfig,omitempty"`
	MaterialsConfig   MaterialsConfig   `json:"materialsConfig,omitempty"`
	AnswerLimitConfig AnswerLimitConfig `json:"answerLimitConfig,omitempty"`
	GiftConfig        GiftConfig        `json:"giftConfig,omitempty"`
	EngineIsInit      bool              `json:"engineIsInit,omitempty"`
}
type PropsTree struct {
	Spacing                      int            `json:"spacing,omitempty"`
	WrapperMainTitle             string         `json:"wrapperMainTitle,omitempty"`
	WrapperSubTitle              string         `json:"wrapperSubTitle,omitempty"`
	ShowQuestionSerialNumber     bool           `json:"showQuestionSerialNumber,omitempty"`
	SkinURL                      string         `json:"skinUrl,omitempty"`
	AnswerQuestionProcessCanBack bool           `json:"answerQuestionProcessCanBack,omitempty"`
	SurveySettings               SurveySettings `json:"surveySettings,omitempty"`
	QuestionWrapperActiveKey     int            `json:"questionWrapperActiveKey,omitempty"`
	EngineIsInit                 bool           `json:"engineIsInit,omitempty"`
	SettingsProps                SettingsProps  `json:"settingsProps,omitempty"`
	Clientid                     string         `json:"clientid,omitempty"`
}
type Parent struct {
	Index int
}

type ComponentsTree struct {
	ComponentName  string           `json:"componentName,omitempty"`
	ID             string           `json:"id,omitempty"`
	DocID          string           `json:"docId,omitempty"`
	Props          Props            `json:"props,omitempty"`
	Meta           MetaTree         `json:"meta,omitempty"`
	FileName       string           `json:"fileName,omitempty"`
	Hidden         bool             `json:"hidden,omitempty"`
	Title          string           `json:"title,omitempty"`
	IsLocked       bool             `json:"isLocked,omitempty"`
	Condition      bool             `json:"condition,omitempty"`
	ConditionGroup string           `json:"conditionGroup,omitempty"`
	Children       []ComponentsTree `json:"children,omitempty"`
	Index          *int32           `json:"index,omitempty"`
	Parent         Parent           `json:"parent,omitempty"`
}

type I18N struct {
}
type PropsLayout struct {
	Logo string `json:"logo,omitempty"`
	Name string `json:"name,omitempty"`
}
type Layout struct {
	ComponentName any         `json:"componentName,omitempty"`
	Props         PropsLayout `json:"props,omitempty"`
}
type Config struct {
	HistoryMode  string `json:"historyMode,omitempty"`
	TargetRootID string `json:"targetRootID,omitempty"`
	Layout       Props  `json:"layout,omitempty"`
}

type ConfigWithPropsLayout struct {
	HistoryMode  string `json:"historyMode,omitempty"`
	TargetRootID string `json:"targetRootID,omitempty"`
	Layout       Layout `json:"layout,omitempty"`
}

type Meta struct {
	Name        string `json:"name,omitempty"`
	ProjectName string `json:"project_name,omitempty"`
	Description string `json:"description,omitempty"`
	Spma        string `json:"spma,omitempty"`
	Creator     string `json:"creator,omitempty"`
}
