package types

import "survey/proto"

type Deliver struct {
	DefaultLanguage     string `json:"default_language"`
	Region              string `json:"region"`
	OfficialWebsiteHost string `json:"official_website_host"`
	SurveyHost          string `json:"survey_host"`
}

type DeliverList struct {
	Clients     []string  `json:"clients"`
	Name        string    `json:"name"`
	DeliverList []Deliver `json:"deliver_list"`
}

type WebPath struct {
	Region  string
	WebPath string
}

type GetZoneListResponse struct {
	Success bool             `json:"success"`
	Data    []proto.DataItem `json:"data"`
}
