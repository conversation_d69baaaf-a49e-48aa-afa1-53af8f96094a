package types

import (
	"gitlab.papegames.com/fringe/cond"
	gosstrategy "gitlab.papegames.com/fringe/pkg/shared/gos_strategy"
	proto2 "survey/proto"
)

type ExportBaseLabel struct {
	Label string
	Value string
}

var ExportBaseHeaders = []ExportBaseLabel{
	{Label: "答卷ID", Value: "id"},
	{Label: "平台账号ID", Value: "openid"},
	{Label: "玩家角色ID", Value: "role_id"},
	{Label: "设备信息", Value: "device_id"},
	{Label: "IP", Value: "ip"},
	{Label: "地区", Value: "location"},
	{Label: "答卷是否有效", Value: "is_valid"},
	{Label: "答题开始时间", Value: "begin_time"},
	{Label: "答题结束时间", Value: "end_time"},
	{Label: "答题时间", Value: "answer_time"},
	{Label: "问卷提交时间", Value: "ctime"},
	{Label: "渠道id", Value: "plat_id"},
}

type Response[T any] struct {
	Code      int32  `json:"code"`
	Info      string `json:"info"`
	RequestId string `json:"request_id"`
	Data      T      `json:"data"`
}

type ExportQueueExtra struct {
	UserCluster    []*UserCluster  `json:"user_cluster"`    // BI 用户分群
	UserTag        []*UserTag      `json:"user_tag"`        // BI 标签
	QuestionFilter *cond.Condition `json:"question_filter"` // 问题筛选器
}

type UserCluster struct {
	ClusterInfo *proto2.UserStrategy `json:"cluster_info"`
	Relation    int32                `json:"relation"` // 1:属于 2：不属于
}

type UserTag struct {
	TagInfo  *UserTagInfo `json:"tag_info"`
	Relation int32        `json:"relation"` // 1:等于 2：不等于
	TagVal   string       `json:"tag_val"`
}

type UserTagInfo struct {
	TagName       string `json:"tag_name"`
	TagEntityName string `json:"tag_entity_name"`
	Version       string `json:"version,omitempty"`
}

//type ClusterInfoData struct {
//	ClusterName string `json:"cluster_name"` // 用户分群名称
//	EntityName  string `json:"entity_name"`  // 用户分群基于的用户维度, 如vroleid, vopenid
//	Version     string `json:"version"`      // 分群版本
//}

type UserQueryClusterExistReq struct {
	ClusterName string   `json:"cluster_name"`
	ClientID    int64    `json:"client_id"`
	EntityIds   []string `json:"entity_ids"`
}

type UserQueryClusterExistRes struct {
	Item []*UserQueryClusterExistItem `json:"item"`
}

type UserQueryClusterExistItem struct {
	EntityId string `json:"entity_id"`
	Exist    bool   `json:"exist,omitempty"`
}

type UserQueryClusterStatusReq struct {
	Packages []*UserQueryClusterStatusReqItem `json:"packages"`
}

type UserQueryClusterStatusReqItem struct {
	Key      string `json:"key"`
	Version  string `json:"version"`
	ClientID int64  `json:"clientid"`
	BizType  string `json:"biz_type"`
	BizId    int64  `json:"biz_id"`
}

type UserQueryClusterStatusRes struct {
	List []*UserQueryClusterStatusResItem `json:"list"`
}

type UserQueryClusterStatusResItem struct {
	UserNum  int64  `json:"user_num"`
	Status   int32  `json:"status"`
	ClientID int64  `json:"clientid"`
	Key      string `json:"key"`
	Version  string `json:"version"`
}

type UserQuerySubmitUserPackageReq struct {
	BizId             int64                          `json:"biz_id"`
	BizType           string                         `json:"biz_type"`
	ClientID          int64                          `json:"clientid"`
	BiEnv             string                         `json:"bi_env"`
	UserStrategy      *gosstrategy.UserStrategy      `json:"user_strategy"`
	UserStrategyExtra *gosstrategy.UserStrategyExtra `json:"user_strategy_extra"`
}

type UserQuerySubmitUserPackageRes struct {
	PackageKey string `json:"package_key"`
}

type UserQuerySubmitUserTagReq struct {
	ClientId      int64  `json:"clientid"`
	TagName       string `json:"tag_name"`
	TagEntityName string `json:"tag_entity_name"`
}

type UserQueryTagStatusItem struct {
	ClientId int64  `json:"clientid"`
	TagName  string `json:"tag_name"`
	BizId    int64  `json:"biz_id"`
	BizType  string `json:"biz_type"`
	Version  string `json:"version"`
}

type UserQueryTagStatusReq struct {
	Tags []*UserQueryTagStatusItem `json:"tags"`
}

type UserQueryTagStatusResItem struct {
	UserNum  int64  `json:"user_num"`
	Status   int32  `json:"status"`
	ClientId int64  `json:"clientid"`
	TagName  string `json:"tag_name"`
	Version  string `json:"version"`
}

type UserQueryTagStatusRes struct {
	List []*UserQueryTagStatusResItem `json:"list"`
}

type UserQueryTagListReq struct {
	TagName   string   `json:"tag_name"`
	ClientId  int64    `json:"client_id"`
	EntityIds []string `json:"entity_ids"`
}

type UserQueryTagListResItem struct {
	EntityId string `json:"entity_id"`
	Val      string `json:"val"`
}

type UserQueryTagListRes struct {
	Item []*UserQueryTagListResItem `json:"item"`
}
