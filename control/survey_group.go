package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"survey/proto"
	"survey/service"
)

func (c Control) SurveyGroupCreate(ctx context.Context, req *proto.SurveyGroupCreateReq) (*xtype.Empty, error) {
	res, err := service.SurveyGroupCreate(ctx, req)
	return res, err
}

func (c Control) SurveyGroupUpdate(ctx context.Context, req *proto.SurveyGroupUpdateReq) (*xtype.Empty, error) {
	res, err := service.SurveyGroupUpdate(ctx, req)
	return res, err
}

func (c Control) SurveyGroupDetail(ctx context.Context, req *proto.SurveyGroupDetailReq) (*proto.CmsSurveyGroupInfo, error) {
	res, err := service.SurveyGroupDetail(ctx, req)
	return res, err
}

func (c Control) SurveyGroupList(ctx context.Context, req *proto.SurveyGroupListReq) (*proto.SurveyGroupListRes, error) {
	res, err := service.SurveyGroupList(ctx, req)
	return res, err
}

func (c Control) SurveyGroupSubUpdate(ctx context.Context, req *proto.SurveyGroupSubUpdateReq) (*xtype.Empty, error) {
	res, err := service.SurveyGroupSubUpdate(ctx, req)
	return res, err
}

func (c Control) SurveyGroupOverwriteSend(ctx context.Context, request *proto.SurveyGroupOverwriteSendReq) (*xtype.Empty, error) {
	res, err := service.SurveyGroupOverwriteSend(ctx, request.ClientId, request.SurveyGroupId)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyGroupOverwriteSend error", xlog.Err(err))
		return nil, err
	}
	return res, err
}

func (c Control) SurveyGroupOverwriteSync(ctx context.Context, request *proto.SurveyGroupOverwriteSyncReq) (*xtype.Empty, error) {
	res, err := service.SurveyGroupOverwriteSync(ctx, request.ClientId, request.SurveyGroup)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyGroupOverwriteSync error", xlog.Err(err))
		return nil, err
	}
	return res, err
}
