package control

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gvar"
	"gitlab.papegames.com/fringe/sparrow/pkg/xencoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"io"
	"net/http"
	"strconv"
	"survey/config"
	"survey/proto"
	"survey/service"
)

func (c Control) GetDeliverList(ctx context.Context, request *proto.GetDeliverListRequest) (*proto.GetDeliverListResponse, error) {
	gLog := xlog.FromContext(ctx)

	deliverList := service.GetDeliverList(strconv.FormatInt(request.ClientId, 10))
	protoDeliverList := service.ConvertToProtoDeliver(deliverList)

	gLog.Info("GetDeliverList response", xlog.Any("deliver_list", deliverList), xlog.Any("proto_deliver_list", protoDeliverList), xlog.Any("client_id", request.ClientId))
	return &proto.GetDeliverListResponse{
		List: protoDeliverList,
	}, nil
}

func (c Control) GetZoneList(ctx context.Context, request *proto.GetZoneListRequest) (*proto.GetZoneListResponse, error) {
	gLog := xlog.FromContext(ctx)
	majorUrl := config.Get().CmsMajorUrl
	CmsMajorZoneListUri := config.Get().CmsMajorZoneListUri
	url := fmt.Sprintf("%s%s%s&clientid=%d", majorUrl, CmsMajorZoneListUri, service.GetApiExternalPathEnv(), request.ClientId)

	resp, err := http.Get(url)
	if err != nil {
		gLog.Error("Failed to fetch zone list", xlog.String("url", url), xlog.Err(err))
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		gLog.Error("Failed to read response body", xlog.Err(err))
		return nil, err
	}
	gLog.Info("GetZoneList response", xlog.String("url", url), xlog.String("body", string(body)))

	var getZoneListResponse struct {
		Success bool
		Data    []struct {
			Value interface{}
			Label string
		}
	}
	if err := json.Unmarshal(body, &getZoneListResponse); err != nil {
		gLog.Error("JSON unmarshalling failed", xlog.Err(err), zap.String("body", string(body)))
		return nil, err
	}

	if !getZoneListResponse.Success {
		gLog.Error("API response not successful", xlog.String("response", string(body)))
		return nil, nil
	}

	protoZoneList := make([]*proto.DataItem, len(getZoneListResponse.Data))
	for i, v := range getZoneListResponse.Data {
		gLog.Info("GetZoneList response", xlog.Any("value", v.Value), xlog.Any("label", v.Label))
		val := gvar.New(v.Value)
		protoZoneList[i] = &proto.DataItem{
			Value: val.Int32(),
			Label: v.Label,
		}
	}

	gLog.Info("GetZoneList response", xlog.Any("zone_list", getZoneListResponse.Data), xlog.Any("proto_zone_list", protoZoneList), xlog.Any("client_id", request.ClientId))
	return &proto.GetZoneListResponse{
		List: protoZoneList,
	}, nil
}
