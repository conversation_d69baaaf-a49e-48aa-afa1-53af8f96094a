package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"strconv"
	"survey/proto"
	"survey/service"
	"survey/util/errors"
)

func (c Control) SurveyShow(ctx context.Context, request *proto.ShowSurveyRequest) (*proto.Survey, error) {
	gLog := xlog.FromContext(ctx)
	if request.ClientId <= 0 || request.Id <= 0 {
		gLog.Error("SurveyShow with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "client_id or id cannot be empty")
	}
	return service.SurveyShowService(ctx, request.Id, strconv.FormatInt(request.ClientId, 10))
}
