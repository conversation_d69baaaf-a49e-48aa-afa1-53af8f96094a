package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"survey/proto"
	"survey/service"
)

func (c Control) GetValidRedeemConfigList(ctx context.Context, request *proto.ValidRedeemConfigRequest) (*proto.ValidRedeemConfigResponse, error) {
	logger := xlog.FromContext(ctx)
	//result, err := service.GetValidRedeemConfigList(ctx, request)
	result, err := service.GetValidRedeemConfigListNew(ctx, request)
	if err != nil {
		logger.Error("ValidRedeemConfigList with error", xlog.Err(err))
		return nil, err
	}
	return &proto.ValidRedeemConfigResponse{
		List:  result,
		Total: int64(request.PageSize),
	}, nil
}

func (c Control) GetPreAwardTemplateList(ctx context.Context, request *proto.PreAwardTemplateRequest) (*proto.PreAwardTemplateResponse, error) {
	logger := xlog.FromContext(ctx)
	//result, err := service.GetPreAwardTemplateList(ctx, request)
	result, err := service.GetPreAwardTemplateListNew(ctx, request)
	if err != nil {
		logger.Error("GetPreAwardTemplateList with error", xlog.Err(err))
		return nil, err
	}
	return &proto.PreAwardTemplateResponse{
		List:  result,
		Total: int64(request.PageSize),
	}, nil
}
