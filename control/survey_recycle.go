package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"strconv"
	"survey/database"
	"survey/proto"
	"survey/service"
	"survey/util/errors"
)

// 问卷回收列表
func (c Control) SurveyRecycleList(ctx context.Context, request *proto.SurveyListRequest) (*proto.SurveyListResponse, error) {
	result, err := service.SurveyRecycleList(ctx, request)
	if err != nil {
		xlog.Error("SurveyRecycleList with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

// 回收站-彻底删除
func (c Control) DeleteSurveyRecycle(ctx context.Context, request *proto.SurveyDelRequest) (*xtype.Empty, error) {
	if len(request.DelList) <= 0 {
		xlog.Error("GetSurveyRecordDetail with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "DelList cannot be empty")
	}
	db := database.Get()
	err := service.DeleteSurveyRecycle(ctx, db, request)
	if err != nil {
		xlog.Error("DeleteSurveyRecycleData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 回收站-恢复问卷
func (c Control) RecoverSurveyRecycle(ctx context.Context, request *proto.RecoverSurveyRecycleReq) (*xtype.Empty, error) {
	if len(request.List) <= 0 || request.ClientId <= 0 {
		xlog.Error("GetSurveyRecordDetail with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "List or client_id cannot be empty")
	}
	err := service.RecoverSurveyRecycle(ctx, strconv.FormatInt(request.ClientId, 10), request.List)
	if err != nil {
		xlog.Error("DeleteSurveyRecycleData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 清空回收站
func (c Control) ClearAllSurveyRecycle(ctx context.Context, request *proto.ClearAllSurveyRecycleRequest) (*xtype.Empty, error) {
	db := database.Get()
	err := service.ClearAllSurveyRecycle(ctx, db, request)
	if err != nil {
		xlog.Error("ClearAllSurveyRecycleData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 恢复所有问卷
func (c Control) RecoverAllSurveyRecycle(ctx context.Context, request *proto.Survey) (*xtype.Empty, error) {
	db := database.Get()
	err := service.RecoverAllSurveyRecycle(ctx, db, request)
	if err != nil {
		xlog.Error("RecoverAllSurveyRecycle with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}
