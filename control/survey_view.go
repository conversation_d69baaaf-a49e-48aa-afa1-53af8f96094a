package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"survey/proto"
	"survey/service"
	"survey/util/errors"
)

func (c Control) SurveyViewCreate(ctx context.Context, req *proto.SurveyViewCreateReq) (*proto.SurveyViewCreateRes, error) {
	gLog := xlog.FromContext(ctx)
	id, err := service.CreateSurveyView(ctx, req)
	if err != nil {
		gLog.Error("SurveyViewCreate with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	res := &proto.SurveyViewCreateRes{Id: id}
	return res, nil
}

func (c Control) SurveyViewList(ctx context.Context, req *proto.SurveyViewListReq) (*proto.SurveyViewListRes, error) {
	gLog := xlog.FromContext(ctx)
	list, err := service.GetSurveyViewList(ctx, req)
	if err != nil {
		gLog.Error("SurveyViewList with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	res := &proto.SurveyViewListRes{List: list}
	return res, nil
}

func (c Control) SurveyViewUpdate(ctx context.Context, req *proto.SurveyView) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	err := service.UpdateSurveyView(ctx, req)
	if err != nil {
		gLog.Error("SurveyViewUpdate with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return &xtype.Empty{}, nil
}

func (c Control) SurveyViewDelete(ctx context.Context, req *proto.SurveyViewDeleteReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	err := service.DeleteSurveyView(ctx, req)
	if err != nil {
		gLog.Error("SurveyViewDelete with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return &xtype.Empty{}, nil
}
