package control

import (
	"context"
	"encoding/json"
	sErr "errors"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"strconv"
	"survey/model"
	"survey/proto"
	"survey/service"
	"survey/util/base"
	"survey/util/errors"

	"gorm.io/gorm"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"go.uber.org/zap"
)

// 问卷列表
func (c Control) SurveyList(ctx context.Context, request *proto.SurveyListRequest) (*proto.SurveyListResponse, error) {
	gLog := xlog.FromContext(ctx)

	result, err := service.SurveyList(ctx, request)
	if err != nil {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

// 创建问卷
func (c Control) CreateSurvey(ctx context.Context, request *proto.CreateSurveyRequest) (*proto.SurveyResponse, error) {
	gLog := xlog.FromContext(ctx)
	id, err := service.CreateSurvey(ctx, request)
	if err != nil {
		gLog.Error("CreateSurvey with error", zap.Error(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return &proto.SurveyResponse{
		Id: id,
	}, err
}

func (c Control) StatisticsUpdate(ctx context.Context, request *proto.StatisticsUpdateRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	err := service.StatisticsUpdate(ctx, request)
	if err != nil {
		gLog.Error("StatisticsUpdate with error", zap.Error(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 编辑问卷
func (c Control) UpdateSurvey(ctx context.Context, request *proto.UpdateSurveyRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	err := service.UpdateSurvey(ctx, request)
	if err != nil {
		gLog.Error("UpdateSurvey with error", zap.Error(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 问卷预览
func (c Control) SurveyPreview(ctx context.Context, request *proto.SurveyPreviewReq) (*proto.Survey, error) {
	gLog := xlog.FromContext(ctx)
	if request.Id <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyPreview with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	return service.SurveyPreview(ctx, request.Id, strconv.FormatInt(request.ClientId, 10))
}

// 答卷管理-问卷详情
func (c Control) SurveyRecordDetail(ctx context.Context, request *proto.SurveyRecordConfDetailsReq) (*proto.SurveyRecordConfDetailsRes, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.Id <= 0 {
		gLog.Error("SurveyRecordDetail with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, nil)
	}
	surveyConfig, userRecord, SurveyRecordDetail, err := service.GetSurveyRecordDetail(ctx, request.SurveyId, request.Id)
	if err != nil {
		return nil, err
	}
	survey, err := json.Marshal(surveyConfig)
	if err != nil {
		xlog.Error("SurveyRecordDetail with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	surveyRecord, err := json.Marshal(userRecord)
	if err != nil {
		xlog.Error("userRecord with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	result := &proto.SurveyRecordConfDetailsRes{}
	result.SurveyConfig = string(survey)
	result.UserRecord = string(surveyRecord)
	result.RecordDetail = SurveyRecordDetail
	return result, nil
}

// 删除问卷
func (c Control) DeleteSurvey(ctx context.Context, request *proto.SurveyDelRequest) (*xtype.Empty, error) {
	err := service.DeleteSurvey(ctx, request)
	if err != nil {
		xlog.FromContext(ctx).Error("DeleteSurvey with error", xlog.Err(err))
		return nil, err
	}
	return nil, nil
}

// 复制问卷
func (c Control) CopySurvey(ctx context.Context, request *proto.SurveyRequest) (*proto.SurveyResponse, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyRecordDetail with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, nil)
	}
	id, err := service.CopySurvey(ctx, request.SurveyId, strconv.FormatInt(request.ClientId, 10))
	return &proto.SurveyResponse{
		Id: id,
	}, err
}

// 开启和暂停问卷
func (c Control) SetStatusSurvey(ctx context.Context, request *proto.SurveySetStatusRequest) (*xtype.Empty, error) {
	err := service.SetStatusSurvey(ctx, strconv.FormatInt(request.ClientId, 10), request.Id, request.Status)
	if err != nil {
		xlog.FromContext(ctx).Error("SetStatusSurvey with error", xlog.Err(err))
		return nil, err
	}
	return nil, nil
}

// 问卷发布
func (c Control) PublishSurvey(ctx context.Context, request *proto.PublishSurveyRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.Id <= 0 || request.ClientId <= 0 {
		gLog.Error("PublishSurvey with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "id or client_id cannot be empty")
	}
	// 检测问卷是否已有用户答题
	err := service.SurveyPublish(ctx, request.Id, strconv.FormatInt(request.ClientId, 10))
	if err != nil {
		gLog.Error("SurveyPublishData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 获取最版版本问卷配置
func (c Control) GetLatestSurveyBySurveyId(ctx context.Context, request *proto.GetLatestSurveyBySurveyIdRequest) (*proto.GetLatestSurveyBySurveyIdResponse, error) {
	gLog := xlog.FromContext(ctx)

	surveyData, err := model.GetLatestSurveyBySurveyId(ctx, request.SurveyId)
	if err != nil && !sErr.Is(err, gorm.ErrRecordNotFound) {
		gLog.Error("GetLatestSurveyBySurveyId with error", xlog.Err(err))
		return nil, err
	} else if err != nil && sErr.Is(err, gorm.ErrRecordNotFound) {
		gLog.Info("GetLatestSurveyBySurveyId is empty", zap.Int64("surveyId", request.SurveyId))
		return nil, nil
	}

	schemaStr, err := base.UnCompress(surveyData.Schema)
	if err != nil {
		return nil, err
	}

	return &proto.GetLatestSurveyBySurveyIdResponse{
		Id:          surveyData.ID,
		SurveyId:    surveyData.SurveyID,
		Name:        surveyData.Name,
		Schema:      schemaStr,
		Settings:    surveyData.Settings,
		WebSettings: surveyData.WebSettings,
	}, nil
}

// 文件上传
func (c Control) Upload(ctx context.Context, request *proto.UploadRequest) (*proto.UploadResponse, error) {
	gCtx := xgin.FromContext(ctx)
	file, err := gCtx.FormFile("file")
	if err != nil {
		xlog.FromContext(ctx).Error("Upload with error", xlog.Err(err))
		return nil, err
	}
	url, err := service.UploadToOSS(ctx, file)
	if err != nil {
		xlog.FromContext(ctx).Error("Upload with error", xlog.Err(err))
		return nil, err
	}
	return &proto.UploadResponse{Url: url}, err
}

func (c Control) SurveyOverwriteSend(ctx context.Context, request *proto.SurveyOverwriteSendReq) (*xtype.Empty, error) {
	res, err := service.SurveyOverwriteSend(ctx, request.ClientId, request.SurveyId)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyOverwriteSend error", xlog.Err(err))
		return nil, err
	}
	return res, err
}

func (c Control) SurveyOverwriteSync(ctx context.Context, request *proto.SurveyOverwriteSyncReq) (*xtype.Empty, error) {
	res, err := service.SurveyOverwriteSync(ctx, request.ClientId, request.Survey)
	if err != nil {
		xlog.FromContext(ctx).Error("SurveyOverwriteSync error", xlog.Err(err))
		return nil, err
	}
	return res, err
}
