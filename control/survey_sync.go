package control

import (
	"context"
	"strconv"
	"survey/proto"
	"survey/service"
	"survey/util/errors"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func (c Control) SyncSurvey(ctx context.Context, request *proto.SyncSurveyRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.ClientId <= 0 || request.Id <= 0 {
		gLog.Error("SurveyShow with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "client_id or id cannot be empty")
	}
	err := service.SyncSurvey(ctx, strconv.FormatInt(request.ClientId, 10), request.Id, request.MaterialVersion)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (c Control) ImpSurvey(ctx context.Context, request *proto.ImpSurveyRequest) (*xtype.Empty, error) {
	err := service.ImpSurvey(ctx, request)
	if err != nil {
		return nil, err
	}
	return nil, nil
}
