package control

import (
	"context"
	"strconv"
	"survey/config"
	"survey/proto"
	"survey/service"
	"survey/util/errors"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"go.uber.org/zap"
)

func (c Control) SurveyStatistics(ctx context.Context, request *proto.SurveyStatisticsRequest) (*proto.SurveyStatisticsResponse, error) {
	gLog := xlog.FromContext(ctx)
	if request.Id <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyStatistics with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	return service.SurveyStatistics(ctx, strconv.FormatInt(request.ClientId, 10), request.Id)
}

func (c Control) GetSurveyStatisticsList(ctx context.Context, request *proto.SurveyDetailRequest) (*proto.SurveyDetailResponse, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyRecordDetail with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, nil)
	}
	result, err := service.GetSurveyDetail(ctx, request.SurveyId, strconv.FormatInt(request.ClientId, 10))
	if err != nil {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

// 老接口
func (c Control) SurveyStatisticsDetailOld(ctx context.Context, request *proto.SurveyRequest) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyRecordDetail with error", zap.Error(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, nil)
	}
	result, err := service.SurveyStatisticsDetail(ctx, request.SurveyId, strconv.FormatInt(request.ClientId, 10))
	if err != nil {
		gLog.Error("SurveyListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

func (c Control) SurveyStatisticsDetail(ctx context.Context, request *proto.SurveyRequest) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.ClientId <= 0 {
		gLog.Error("SurveyStatisticsDetail2 with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, nil)
	}

	var result *xtype.RawMessage
	var err error
	if config.Get().DetailApiUseNode {
		result, err = service.SurveyStatisticsDetail(ctx, request.SurveyId, strconv.FormatInt(request.ClientId, 10))
		if err != nil {
			gLog.Error("SurveyStatisticsDetail SurveyListData with error", xlog.Err(err))
			return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
		}
	} else {
		result, err = service.SurveyStatisticsDetail2(ctx, request.SurveyId, strconv.FormatInt(request.ClientId, 10))
		if err != nil {
			gLog.Error("SurveyStatisticsDetail2 with error", xlog.Err(err))
			return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
		}
	}
	return result, nil
}
