package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	"net/http"
	"survey/proto"
)

func (c Control) GetUserInfo(ctx context.Context, req *proto.GetUserInfoRequest) (*proto.GetUserInfoResponse, error) {
	gCtx := xgin.FromContext(ctx)
	ret := `{"data":{"userInfo":{"uid":"1692158775508852886","name":"lingyun","nickname":"凌雲","phone":"+8617721473132","avatar":"https://s1-imfile.feishucdn.com/static-resource/v1/v2_c83cba23-0453-43a5-8ab2-34bfffa53b6g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp","email":"<EMAIL>","clientId":"1068","clientid":"1068"},"clientList":[{"clientid":"1001","memo":"恋与制作人","default_language":"zh","group_id":"22","group_name":"恋与制作人"},{"clientid":"1002","memo":"暖暖环游世界","default_language":"zh","group_id":"685","group_name":"暖暖环游世界（二代）"},{"clientid":"1008","memo":"闪耀暖暖","default_language":"zh","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1009","memo":"恋与制作人韩国","default_language":"ko","group_id":"22","group_name":"恋与制作人"},{"clientid":"1011","memo":"恋与制作人台湾","default_language":"zh-CHT","group_id":"22","group_name":"恋与制作人"},{"clientid":"1012","memo":"NNSDK自动化测试","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"1017","memo":"恋与制作人日本","default_language":"jp","group_id":"22","group_name":"恋与制作人"},{"clientid":"1019","memo":"闪耀暖暖台湾","default_language":"zh-CHT","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1021","memo":"恋与制作人欧美","default_language":"en","group_id":"22","group_name":"恋与制作人"},{"clientid":"1025","memo":"闪耀暖暖韩国","default_language":"ko","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1027","memo":"恋与制作人泰国","default_language":"th","group_id":"22","group_name":"恋与制作人"},{"clientid":"1029","memo":"闪耀暖暖体验服","default_language":"zh","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1030","memo":"无期迷途（国内）","default_language":"zh","group_id":"38","group_name":"无期迷途"},{"clientid":"1033","memo":"恋与深空（研发）","default_language":"zh","group_id":"19","group_name":"恋与深空"},{"clientid":"1035","memo":"闪耀暖暖日本","default_language":"jp","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1037","memo":"闪耀暖暖欧美","default_language":"en","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1039","memo":"恋与制作人越南","default_language":"en","group_id":"22","group_name":"恋与制作人"},{"clientid":"1051","memo":"逆光潜入（国服）","default_language":"zh","group_id":"689","group_name":"逆光潜入"},{"clientid":"1053","memo":"闪耀暖暖欧美cbt","default_language":"en","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1055","memo":"木狼","default_language":"zh","group_id":"693","group_name":"测试新增组"},{"clientid":"1057","memo":"x9","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1059","memo":"x5cbt","default_language":"zh","group_id":"689","group_name":"逆光潜入"},{"clientid":"1061","memo":"无期迷途（海外）","default_language":"en","group_id":"38","group_name":"无期迷途"},{"clientid":"1063","memo":"逆光潜入（海外）","default_language":"en","group_id":"689","group_name":"逆光潜入"},{"clientid":"1065","memo":"平台中心","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"1067","memo":"恋与深空（海外）","default_language":"en","group_id":"19","group_name":"恋与深空"},{"clientid":"1068","memo":"恋与深空（国服）","default_language":"zh","group_id":"19","group_name":"恋与深空"},{"clientid":"1069","memo":"恋与深空（审核）","default_language":"zh","group_id":"19","group_name":"恋与深空"},{"clientid":"1077","memo":"百面千相（国服）","default_language":"zh","group_id":"692","group_name":"百面千相"},{"clientid":"1078","memo":"百面千相（WEB）","default_language":"zh","group_id":"692","group_name":"百面千相"},{"clientid":"1081","memo":"无限暖暖（研发）","default_language":"zh","group_id":"691","group_name":"无限暖暖"},{"clientid":"1083","memo":"海外账号中心","default_language":"en","group_id":"684","group_name":"平台组"},{"clientid":"1085","memo":"无限暖暖（国服OBT）","default_language":"zh","group_id":"693","group_name":"测试新增组"},{"clientid":"1087","memo":"无限暖暖（版署）","default_language":"zh","group_id":"691","group_name":"无限暖暖"},{"clientid":"1091","memo":"叠桌面（国内）","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1092","memo":"叠桌面（海外）","default_language":"en","group_id":null,"group_name":null},{"clientid":"1093","memo":"社区","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"1094","memo":"奇迹暖暖（国服）","default_language":"zh","group_id":"61","group_name":"奇迹暖暖"},{"clientid":"1095","memo":"闪暖越南","default_language":"vn","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"1101","memo":"叠纸支付中心","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1102","memo":"问卷","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1103","memo":"闪暖越南（审核）","default_language":"en","group_id":null,"group_name":null},{"clientid":"1104","memo":"恋与深空（海外）-欧美CBT","default_language":"en","group_id":"19","group_name":"恋与深空"},{"clientid":"1106","memo":"无限暖暖（国服CBT）","default_language":"zh","group_id":"691","group_name":"无限暖暖"},{"clientid":"1108","memo":"X12（国服）","default_language":"zh","group_id":"694","group_name":"X12"},{"clientid":"1110","memo":"奇迹暖暖（台服）","default_language":"zh-CHT","group_id":"61","group_name":"奇迹暖暖"},{"clientid":"1116","memo":"无限暖暖（海外）","default_language":"en","group_id":"691","group_name":"无限暖暖"},{"clientid":"1118","memo":"游戏运营中心","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1120","memo":"客服中心","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1122","memo":"X12（海外）","default_language":"en","group_id":"694","group_name":"X12"},{"clientid":"1126","memo":"无限暖暖海外（研发）","default_language":"en","group_id":null,"group_name":null},{"clientid":"1128","memo":"问卷平台（研发）","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1130","memo":"叠友记","default_language":"zh","group_id":null,"group_name":null},{"clientid":"1999","memo":"NNSDK自动化测试2","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"2002","memo":"测试","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"2008","memo":"闪耀暖暖-不停服测试1","default_language":"zh","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"2009","memo":"测试用可删1","default_language":"en","group_id":"684","group_name":"平台组"},{"clientid":"2012","memo":"NNSDK自动化测试-不停服","default_language":"zh","group_id":null,"group_name":null},{"clientid":"2019","memo":"闪耀暖暖台湾-不停服测试","default_language":"zh-CHT","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"2035","memo":"闪耀暖暖日本-不停服测试","default_language":"jp","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"2037","memo":"闪耀暖暖欧美-不停服测试","default_language":"en","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"3007","memo":"测试服","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"3008","memo":"闪耀暖暖-不停服测试3008","default_language":"zh","group_id":"16","group_name":"闪耀暖暖"},{"clientid":"3012","memo":"infold自动化测试","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"3030","memo":"x1自动化测试","default_language":"zh","group_id":"684","group_name":"平台组"},{"clientid":"3031","memo":"海外自动化测试","default_language":"en","group_id":"684","group_name":"平台组"},{"clientid":"9995","memo":"web统一埋点上报 (测试)","default_language":"zh","group_id":null,"group_name":null},{"clientid":"9996","memo":"web统一埋点上报","default_language":"zh","group_id":"694","group_name":"X12"}],"permission":{"surveySummary.get":true,"surveyManage.add":true,"surveyManage.edit":true,"surveySummary.export":true,"surveyManage.delete":true,"member.get":true,"member.add":true,"member.delete":true}},"code":0,"success":true,"timestamp":"2024-05-03T08:04:36.777Z"}`

	gCtx.String(http.StatusOK, ret)
	return nil, ecode.ResetContent
}

func (c Control) UserCheck(ctx context.Context, req *proto.UserCheckRequest) (*proto.UserCheckResponse, error) {
	return nil, nil
}
