package control

import (
	"context"
	"survey/proto"
	"survey/service"
	"survey/util/base"
	"survey/util/errors"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

// 回收答卷列表
func (c Control) SurveyRecordList(ctx context.Context, request *proto.SurveyRecordListV2Request) (*proto.SurveyRecordListResponse, error) {
	gLog := xlog.FromContext(ctx)
	result, err := service.SurveyRecordListNew(ctx, request)
	if err != nil {
		gLog.Error("SurveyRecordListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

func (c Control) SurveyRecordListV2(ctx context.Context, request *proto.SurveyRecordListV2Request) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)
	result, err := service.SurveyRecordListV2(ctx, request)
	if err != nil {
		gLog.Error("SurveyRecordListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return xtype.NewRawMessage(result), nil
}

// 删除答卷
func (c Control) DelSurveyRecord(ctx context.Context, request *proto.SurveyRecordDetailsRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	if err != nil {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
	}
	err = service.DelSurveyRecord(ctx, request.DelList, hashCode, request.SurveyId)
	if err != nil {
		xlog.Error("DelSurveyExportTaskData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 将答卷标记为有效
func (c Control) SetValidSurveyRecord(ctx context.Context, request *proto.SurveyRecordsRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.Id <= 0 || request.SurveyId <= 0 {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	if err != nil {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
	}
	err = service.SetValidSurveyRecord(ctx, request.Id, hashCode)
	if err != nil {
		xlog.Error("SetValidSurveyRecordData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	return nil, nil
}

// 将答卷标记为无效
func (c Control) SetInvalidSurveyRecord(ctx context.Context, request *proto.SetValidSurveyRecordRequest) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if len(request.Ids) <= 0 || request.SurveyId <= 0 {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	hashCode, err := base.SurveyIdToHashCode(request.SurveyId)
	if err != nil {
		gLog.Error("SetValidSurveyRecord with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrParamTypeSwitchFailed, err)
	}
	err = service.SetInvalidSurveyRecord(ctx, request.Ids, hashCode, request.SurveyId)
	if err != nil {
		xlog.Error("SetValidSurveyRecordData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	return nil, nil
}

// 无效答卷列表
func (c Control) InValidSurveyRecordList(ctx context.Context, request *proto.SurveyRecordListRequest) (*proto.SurveyRecordListResponse, error) {
	gLog := xlog.FromContext(ctx)
	result, err := service.InValidSurveyRecordList(ctx, request)
	if err != nil {
		gLog.Error("InValidSurveyRecordListData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}
