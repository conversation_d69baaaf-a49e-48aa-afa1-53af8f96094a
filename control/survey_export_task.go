package control

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"math/rand"
	"strconv"
	"survey/proto"
	"survey/service"
	"survey/util/base"
	"survey/util/errors"
)

// 导出任务列表
func (c Control) SurveyExportTaskList(ctx context.Context, request *proto.SurveyExportTaskListRequest) (*proto.SurveyExportTaskListResponse, error) {
	logger := xlog.FromContext(ctx)
	if request.ClientId <= 0 || request.SurveyId <= 0 {
		logger.Error("SurveyExportTaskList client_id or survey_id cannot be empty", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "client_id or survey_id cannot be empty")
	}

	result, err := service.SurveyExportTaskList(ctx, strconv.FormatInt(request.ClientId, 10), request.SurveyId, request.Page, request.PageSize)
	if err != nil {
		logger.Error("SurveyExportTaskList with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, nil
}

// 创建导出任务
func (c Control) CreateSurveyExportTask(ctx context.Context, request *proto.SurveyExportTask) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.SurveyId <= 0 || request.ClientId <= 0 {
		gLog.Error("GetSurveyRecordDetail with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.WrapStr(errors.ErrMustParamEmpty, "ClientId or SurveyId cannot be empty")
	}
	valid := ""
	if request.IsValid == 0 {
		valid = "_有效"
	}
	if request.IsValid == 1 {
		valid = "_无效"
	}
	if request.IsValid == 2 {
		valid = "_全部"
	}
	dataTypeName := ""
	if request.DataType == 0 {
		dataTypeName = "_答案编码_"
	}
	if request.DataType == 1 {
		dataTypeName = "_答案文本_"
	}

	//timestamp := strconv.Itoa(int(time.Now().Unix()))

	// 生成指定范围内的随机整数
	minNum := 100
	maxNum := 999
	randomIntRange := rand.Intn(maxNum-minNum+1) + minNum // 生成min到max之间的随机整数
	randomNumber := strconv.Itoa(randomIntRange)

	dateTime := base.GetTimeNumStr()

	name := fmt.Sprintf("%s%s%s%s", randomNumber, valid, dataTypeName, dateTime)

	request.Name = name

	err := service.CreateSurveyExportTask(ctx, request)
	if err != nil {
		xlog.Error("CreateSurveyExportTaskData with error", xlog.Err(err))
		return nil, err
	}
	return nil, nil
}

// 删除导出任务
func (c Control) DelSurveyExportTask(ctx context.Context, request *proto.DelSurveyExportTaskReq) (*xtype.Empty, error) {
	err := service.DelSurveyExportTask(ctx, strconv.FormatInt(request.ClientId, 10), request.SurveyId, request.Ids)
	if err != nil {
		xlog.Error("DelSurveyExportTaskData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

// 重置导出任务状态
func (c Control) ResetSurveyExportTaskStatus(ctx context.Context, request *proto.SurveyExportTaskDetailsReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	if request.ClientId <= 0 || request.SurveyId <= 0 || request.Id <= 0 {
		gLog.Error("ResetSurveyExportTaskStatus with error", xlog.Err(errors.ErrMustParamEmpty))
		return nil, errors.Wrap(errors.ErrMustParamEmpty, errors.ErrMustParamEmpty)
	}
	err := service.ResetSurveyExportTaskStatus(ctx, strconv.FormatInt(request.ClientId, 10), request.SurveyId, request.Id)
	if err != nil {
		xlog.Error("ResetSurveyExportTaskStatusData with error", xlog.Err(err))
		return nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return nil, nil
}

func (c Control) SurveyExportUserClusterSubmit(ctx context.Context, request *proto.SurveyExportUserClusterSubmitReq) (*xtype.Empty, error) {
	gLog := xlog.FromContext(ctx)
	err := service.SurveyExportUserClusterSubmit(ctx, request)
	if err != nil {
		gLog.Error("SurveyExportUserClusterSubmit with error", xlog.Err(err))
		return nil, err
	}
	return nil, nil
}

func (c Control) SurveyExportHeaders(ctx context.Context, request *proto.SurveyExportHeadersReq) (*proto.SurveyExportHeadersRes, error) {
	gLog := xlog.FromContext(ctx)
	res, err := service.SurveyExportHeaders(ctx, request)
	if err != nil {
		gLog.Error("SurveyExportHeaders with error", xlog.Err(err))
		return nil, err
	}
	return res, nil
}
