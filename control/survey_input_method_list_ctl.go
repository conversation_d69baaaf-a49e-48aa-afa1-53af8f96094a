package control

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	"survey/config"
	"survey/proto"
	"survey/service"
)

func (c Control) GetInputMethodList(ctx context.Context, req *proto.SurveyInputMethodListRequest) (*xtype.RawMessage, error) {
	gLog := xlog.FromContext(ctx)

	var (
		inputMethodList *xtype.RawMessage
		err             error
	)

	if config.Get().MethodApiUseNode {
		inputMethodList, err = service.GetSurveyInputMethodListService(ctx, req)
	} else {
		inputMethodList, err = service.GetSurveyInputMethodList(ctx, req)
	}

	if err != nil {
		gLog.Error("SurveyInputMethodListService with error", xlog.Err(err))
		return nil, err
	}
	return inputMethodList, err
}
