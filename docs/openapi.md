# SurveyService API

> Version 0.0.1

This API represents survey service.

## Path Table

| Method | Path | Description |
| --- | --- | --- |
| GET | [/v1/general/get-deliver-list](#getv1generalget-deliver-list) |  |
| GET | [/v1/general/get-zone-list](#getv1generalget-zone-list) |  |
| POST | [/v1/survey/clear-logical-destory](#postv1surveyclear-logical-destory) |  |
| POST | [/v1/survey/copy/:surveyId](#postv1surveycopysurveyid) |  |
| POST | [/v1/survey/create](#postv1surveycreate) |  |
| DELETE | [/v1/survey/delete](#deletev1surveydelete) |  |
| GET | [/v1/survey/list](#getv1surveylist) |  |
| DELETE | [/v1/survey/logical-destory](#deletev1surveylogical-destory) |  |
| GET | [/v1/survey/preview/:id](#getv1surveypreviewid) |  |
| POST | [/v1/survey/publish/:id](#postv1surveypublishid) |  |
| POST | [/v1/survey/recover-all-logical-destory](#postv1surveyrecover-all-logical-destory) |  |
| POST | [/v1/survey/recover-logical-destory](#postv1surveyrecover-logical-destory) |  |
| GET | [/v1/survey/recycle-list](#getv1surveyrecycle-list) |  |
| PUT | [/v1/survey/set-status/:id](#putv1surveyset-statusid) |  |
| GET | [/v1/survey/show/:surveyId](#getv1surveyshowsurveyid) |  |
| POST | [/v1/survey/statistics/detail/:surveyId](#postv1surveystatisticsdetailsurveyid) |  |
| POST | [/v1/survey/statistics/detail/input-method-list/:surveyId](#postv1surveystatisticsdetailinput-method-listsurveyid) |  |
| DELETE | [/v1/survey/statistics/user-answer-record/delete/:surveyId](#deletev1surveystatisticsuser-answer-recorddeletesurveyid) |  |
| GET | [/v1/survey/statistics/user-answer-record/detail/:surveyId/:id](#getv1surveystatisticsuser-answer-recorddetailsurveyidid) |  |
| GET | [/v1/survey/statistics/user-answer-record/invalid-list/:surveyId](#getv1surveystatisticsuser-answer-recordinvalid-listsurveyid) |  |
| GET | [/v1/survey/statistics/user-answer-record/list/:surveyId](#getv1surveystatisticsuser-answer-recordlistsurveyid) |  |
| PUT | [/v1/survey/statistics/user-answer-record/set-invalid/:surveyId/:id](#putv1surveystatisticsuser-answer-recordset-invalidsurveyidid) |  |
| PUT | [/v1/survey/statistics/user-answer-record/set-valid/:surveyId/:id](#putv1surveystatisticsuser-answer-recordset-validsurveyidid) |  |
| POST | [/v1/survey/statistics/user-answer-record/survey-export/:surveyId](#postv1surveystatisticsuser-answer-recordsurvey-exportsurveyid) |  |
| POST | [/v1/survey/statistics/user-answer-record/survey-export/:surveyId/del](#postv1surveystatisticsuser-answer-recordsurvey-exportsurveyiddel) |  |
| POST | [/v1/survey/statistics/user-answer-record/survey-export/:surveyId/resetStatus](#postv1surveystatisticsuser-answer-recordsurvey-exportsurveyidresetstatus) |  |
| GET | [/v1/survey/statistics/user-answer-record/survey-export/list/](#getv1surveystatisticsuser-answer-recordsurvey-exportlist) |  |
| POST | [/v1/survey/sync/:id](#postv1surveysyncid) |  |
| PUT | [/v1/survey/update/:id](#putv1surveyupdateid) |  |

## Reference Table

| Name | Path | Description |
| --- | --- | --- |
| Agreement | [#/components/schemas/Agreement](#componentsschemasagreement) |  |
| AnswerLimitConfig | [#/components/schemas/AnswerLimitConfig](#componentsschemasanswerlimitconfig) |  |
| AnswerTimesConfig | [#/components/schemas/AnswerTimesConfig](#componentsschemasanswertimesconfig) |  |
| BaseRuleConfig | [#/components/schemas/BaseRuleConfig](#componentsschemasbaseruleconfig) |  |
| ConfigProps | [#/components/schemas/ConfigProps](#componentsschemasconfigprops) |  |
| DataItem | [#/components/schemas/DataItem](#componentsschemasdataitem) |  |
| DelSurveyExportTaskReq | [#/components/schemas/DelSurveyExportTaskReq](#componentsschemasdelsurveyexporttaskreq) |  |
| Deliver | [#/components/schemas/Deliver](#componentsschemasdeliver) |  |
| FooterConfig | [#/components/schemas/FooterConfig](#componentsschemasfooterconfig) |  |
| GetDeliverListResponse | [#/components/schemas/GetDeliverListResponse](#componentsschemasgetdeliverlistresponse) | 投放列表 |
| GetZoneListResponse | [#/components/schemas/GetZoneListResponse](#componentsschemasgetzonelistresponse) |  |
| GiftConfig | [#/components/schemas/GiftConfig](#componentsschemasgiftconfig) |  |
| InputMethodListRequest | [#/components/schemas/InputMethodListRequest](#componentsschemasinputmethodlistrequest) |  |
| InputMethodListResponse | [#/components/schemas/InputMethodListResponse](#componentsschemasinputmethodlistresponse) |  |
| MaterialsConfig | [#/components/schemas/MaterialsConfig](#componentsschemasmaterialsconfig) |  |
| PreAwardConfig | [#/components/schemas/PreAwardConfig](#componentsschemaspreawardconfig) |  |
| PublishSurveyResponse | [#/components/schemas/PublishSurveyResponse](#componentsschemaspublishsurveyresponse) |  |
| RecoverSurveyRecycleReq | [#/components/schemas/RecoverSurveyRecycleReq](#componentsschemasrecoversurveyrecyclereq) |  |
| RequestConfig | [#/components/schemas/RequestConfig](#componentsschemasrequestconfig) |  |
| Setting | [#/components/schemas/Setting](#componentsschemassetting) | Settings message |
| SourceConfig | [#/components/schemas/SourceConfig](#componentsschemassourceconfig) |  |
| Survey | [#/components/schemas/Survey](#componentsschemassurvey) |  |
| SurveyDelReq | [#/components/schemas/SurveyDelReq](#componentsschemassurveydelreq) |  |
| SurveyDetailRequest | [#/components/schemas/SurveyDetailRequest](#componentsschemassurveydetailrequest) | 问卷详情 |
| SurveyExportTask | [#/components/schemas/SurveyExportTask](#componentsschemassurveyexporttask) |  |
| SurveyExportTaskDetailsReq | [#/components/schemas/SurveyExportTaskDetailsReq](#componentsschemassurveyexporttaskdetailsreq) | 详情 |
| SurveyExportTaskDetailsRes | [#/components/schemas/SurveyExportTaskDetailsRes](#componentsschemassurveyexporttaskdetailsres) |  |
| SurveyExportTaskListRes | [#/components/schemas/SurveyExportTaskListRes](#componentsschemassurveyexporttasklistres) |  |
| SurveyListRes | [#/components/schemas/SurveyListRes](#componentsschemassurveylistres) |  |
| SurveyRecord | [#/components/schemas/SurveyRecord](#componentsschemassurveyrecord) |  |
| SurveyRecordConfDetailsRes | [#/components/schemas/SurveyRecordConfDetailsRes](#componentsschemassurveyrecordconfdetailsres) |  |
| SurveyRecordDetailsRequest | [#/components/schemas/SurveyRecordDetailsRequest](#componentsschemasSurveyRecordDetailsRequest) |  |
| SurveyRecordDetailsRes | [#/components/schemas/SurveyRecordDetailsRes](#componentsschemassurveyrecorddetailsres) |  |
| SurveyRecordListRes | [#/components/schemas/SurveyRecordListRes](#componentsschemassurveyrecordlistres) |  |
| SurveyRecordsReq | [#/components/schemas/SurveyRecordsReq](#componentsschemassurveyrecordsreq) |  |
| SurveyRes | [#/components/schemas/SurveyRes](#componentsschemassurveyres) |  |
| SyncSurveyRequest | [#/components/schemas/SyncSurveyRequest](#componentsschemassyncsurveyrequest) |  |
| SyncSurveyResponse | [#/components/schemas/SyncSurveyResponse](#componentsschemassyncsurveyresponse) |  |
| SyncSurveyResponseData | [#/components/schemas/SyncSurveyResponseData](#componentsschemassyncsurveyresponsedata) |  |
| TimeLimitConfig | [#/components/schemas/TimeLimitConfig](#componentsschemastimelimitconfig) |  |
| Timestamp | [#/components/schemas/Timestamp](#componentsschemastimestamp) |  |
| UpdateSurveyResponse | [#/components/schemas/UpdateSurveyResponse](#componentsschemasupdatesurveyresponse) |  |
| _failReturn | [#/components/schemas/_failReturn](#componentsschemas_failreturn) |  |

## Path Details

***

### [GET]/v1/general/get-deliver-list

- Description  
获取投放列表

#### Parameters(Query)

```ts
client_id?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  // 投放列表
  data: {
    deliver: {
      default_language?: string
      region?: string
      official_website_host?: string
      survey_host?: string
    }[]
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/general/get-zone-list

- Description  
获取区服列表

#### Parameters(Query)

```ts
clientid?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    data: {
      value?: integer
      label?: string
    }[]
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/clear-logical-destory

- Description  
清空回收站

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/copy/:surveyId

- Description  
复制问卷

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/create

- Description  
问卷创建

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [DELETE]/v1/survey/delete

- Description  
彻底删除

#### RequestBody

- application/json

```ts
{
  delList?: integer[]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/list

- Description  
问卷列表

#### Parameters(Query)

```ts
page?: integer
```

```ts
page_size?: integer
```

```ts
_filter_id?: integer
```

```ts
_filter_name?: string
```

```ts
_sort_ctime?: string
```

```ts
_filter_status?: integer
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    list: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // clientid
      clientid?: string
      // 问卷名称
      name?: string
      // 问卷是否打开
      is_closed?: integer
      // 问卷是否暂停
      is_pause?: integer
      // 是否发布
      is_publish?: integer
      // 是否有修改未发布
      is_modify_unpublish?: integer
      // 是否开启过答题
      is_opened?: integer
      // 问卷开始时间
      stime?: #/components/schemas/Timestamp
      // 问卷结束时间
      etime?: #/components/schemas/Timestamp
      // 问卷类型
      type?: integer
      // 配置 schema
      schema?: string
      // 预览 schema
      preview_schema?: string
      // 问卷设置
      settings?: #/components/schemas/Setting
      // 问卷C端用到的配置
      web_settings?: string
      // 问卷C端用到的配置
      languages?: string
      // 问卷id-hash
      hash_code?: string
      // 是否删除
      is_delete?: integer
      // 删除时间
      deltime?: #/components/schemas/Timestamp
      // 备注
      remark?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 更新时间
      mtime?: #/components/schemas/Timestamp
      // 创建人
      creator?: string
      // 最近修改人
      editor?: string
      // isTimeLimit
      isTimeLimit?: boolean
      // 用户答卷数量
      allAnsweredUserCount?: integer
      // 状态
      status?: integer
      // 答卷统计
      fullValidUid?: integer
      questionStatisticsData?: string
    }[]
    totalCounts?: integer
    currentPage?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [DELETE]/v1/survey/logical-destory

- Description  
问卷删除

#### RequestBody

- application/json

```ts
{
  delList?: integer[]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/preview/:id

- Description  
问卷预览

#### Parameters(Query)

```ts
id?: integer
```

```ts
clientid?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // clientid
    clientid?: string
    // 问卷名称
    name?: string
    // 问卷是否打开
    is_closed?: integer
    // 问卷是否暂停
    is_pause?: integer
    // 是否发布
    is_publish?: integer
    // 是否有修改未发布
    is_modify_unpublish?: integer
    // 是否开启过答题
    is_opened?: integer
    // 问卷开始时间
    stime?: #/components/schemas/Timestamp
    // 问卷结束时间
    etime?: #/components/schemas/Timestamp
    // 问卷类型
    type?: integer
    // 配置 schema
    schema?: string
    // 预览 schema
    preview_schema?: string
    // 问卷设置
    settings?: #/components/schemas/Setting
    // 问卷C端用到的配置
    web_settings?: string
    // 问卷C端用到的配置
    languages?: string
    // 问卷id-hash
    hash_code?: string
    // 是否删除
    is_delete?: integer
    // 删除时间
    deltime?: #/components/schemas/Timestamp
    // 备注
    remark?: string
    // 创建时间
    ctime?: #/components/schemas/Timestamp
    // 更新时间
    mtime?: #/components/schemas/Timestamp
    // 创建人
    creator?: string
    // 最近修改人
    editor?: string
    // isTimeLimit
    isTimeLimit?: boolean
    // 用户答卷数量
    allAnsweredUserCount?: integer
    // 状态
    status?: integer
    // 答卷统计
    fullValidUid?: integer
    questionStatisticsData?: string
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/publish/:id

#### Parameters(Query)

```ts
id?: integer
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    success?: boolean
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/recover-all-logical-destory

- Description  
恢复所有问卷

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/recover-logical-destory

- Description  
恢复问卷

#### RequestBody

- application/json

```ts
{
  list?: integer[]
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/recycle-list

- Description  
回收站-问卷列表

#### Parameters(Query)

```ts
page?: integer
```

```ts
page_size?: integer
```

```ts
_filter_id?: integer
```

```ts
_filter_name?: string
```

```ts
_sort_ctime?: string
```

```ts
_filter_status?: integer
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    list: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // clientid
      clientid?: string
      // 问卷名称
      name?: string
      // 问卷是否打开
      is_closed?: integer
      // 问卷是否暂停
      is_pause?: integer
      // 是否发布
      is_publish?: integer
      // 是否有修改未发布
      is_modify_unpublish?: integer
      // 是否开启过答题
      is_opened?: integer
      // 问卷开始时间
      stime?: #/components/schemas/Timestamp
      // 问卷结束时间
      etime?: #/components/schemas/Timestamp
      // 问卷类型
      type?: integer
      // 配置 schema
      schema?: string
      // 预览 schema
      preview_schema?: string
      // 问卷设置
      settings?: #/components/schemas/Setting
      // 问卷C端用到的配置
      web_settings?: string
      // 问卷C端用到的配置
      languages?: string
      // 问卷id-hash
      hash_code?: string
      // 是否删除
      is_delete?: integer
      // 删除时间
      deltime?: #/components/schemas/Timestamp
      // 备注
      remark?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 更新时间
      mtime?: #/components/schemas/Timestamp
      // 创建人
      creator?: string
      // 最近修改人
      editor?: string
      // isTimeLimit
      isTimeLimit?: boolean
      // 用户答卷数量
      allAnsweredUserCount?: integer
      // 状态
      status?: integer
      // 答卷统计
      fullValidUid?: integer
      questionStatisticsData?: string
    }[]
    totalCounts?: integer
    currentPage?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [PUT]/v1/survey/set-status/:id

- Description  
问卷暂停/开启作答

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    id?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/show/:surveyId

- Description  
问卷展示  
 todo

#### RequestBody

- application/json

```ts
// 问卷详情
{
  surveyId?: integer
  success?: boolean
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/statistics/detail/:surveyId

- Description  
问卷详情

#### RequestBody

- application/json

```ts
// 问卷详情
{
  surveyId?: integer
  success?: boolean
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/statistics/detail/input-method-list/:surveyId

#### RequestBody

- application/json

```ts
{
  // 当前页码，默认1
  page?: integer
  // 每页条数，默认10
  page_size?: integer
  surveyId?: integer
  requestConfig: {
    question_unique_key?: string
    config_props: {
      isQuestion?: boolean
      questionType?: string
      valueType?: string
      uniqueKey?: string
      questionId?: integer
      statisticsMethod?: string
      isAddress?: boolean
    }
  }
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    device_id?: string
    id?: integer
    ip?: string
    openid?: string
    option?: string
    question?: string
    role_id?: string
    survey_record_id?: integer
    text?: string
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [DELETE]/v1/survey/statistics/user-answer-record/delete/:surveyId

- Description  
答卷列表删除

#### RequestBody

- application/json

```ts
{
  delList?: integer[]
  survey_id?: integer
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 用户UID
      uid?: string
      // openid
      openid?: string
      // 角色ID
      role_id?: string
      // 设备ID
      device_id?: string
      // ip
      ip?: string
      // 记录是否有效
      is_valid?: integer
      // 是否删除
      is_delete?: integer
      // 答题开始时间
      begin_time?: #/components/schemas/Timestamp
      // 答题结束时间
      end_time?: #/components/schemas/Timestamp
      // 额外信息
      extra?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 答题时间
      second?: string
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/statistics/user-answer-record/detail/:surveyId/:id

- Description  
查看答卷-问卷详情

#### Parameters(Query)

```ts
id?: integer
```

```ts
surveyId?: integer
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    recordDetail?: string
    surveyConfig?: string
    userRecord?: string
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/statistics/user-answer-record/invalid-list/:surveyId

- Description  
无效答卷列表

#### Parameters(Query)

```ts
page?: integer
```

```ts
page_size?: integer
```

```ts
_filter_id?: integer
```

```ts
_filter_openid?: integer
```

```ts
_filter_role_id?: integer
```

```ts
_filter_ip?: string
```

```ts
_filter_device_id?: string
```

```ts
_sort_id?: string
```

```ts
_sort_end_time?: string
```

```ts
_sort_second?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    result: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 用户UID
      uid?: string
      // openid
      openid?: string
      // 角色ID
      role_id?: string
      // 设备ID
      device_id?: string
      // ip
      ip?: string
      // 记录是否有效
      is_valid?: integer
      // 是否删除
      is_delete?: integer
      // 答题开始时间
      begin_time?: #/components/schemas/Timestamp
      // 答题结束时间
      end_time?: #/components/schemas/Timestamp
      // 额外信息
      extra?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 答题时间
      second?: string
    }[]
    totalCounts?: integer
    currentPage?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/statistics/user-answer-record/list/:surveyId

- Description  
回收答卷列表

#### Parameters(Query)

```ts
page?: integer
```

```ts
page_size?: integer
```

```ts
_filter_id?: integer
```

```ts
_filter_openid?: integer
```

```ts
_filter_role_id?: integer
```

```ts
_filter_ip?: string
```

```ts
_filter_device_id?: string
```

```ts
_sort_id?: string
```

```ts
_sort_end_time?: string
```

```ts
_sort_second?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    result: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 用户UID
      uid?: string
      // openid
      openid?: string
      // 角色ID
      role_id?: string
      // 设备ID
      device_id?: string
      // ip
      ip?: string
      // 记录是否有效
      is_valid?: integer
      // 是否删除
      is_delete?: integer
      // 答题开始时间
      begin_time?: #/components/schemas/Timestamp
      // 答题结束时间
      end_time?: #/components/schemas/Timestamp
      // 额外信息
      extra?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 答题时间
      second?: string
    }[]
    totalCounts?: integer
    currentPage?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [PUT]/v1/survey/statistics/user-answer-record/set-invalid/:surveyId/:id

- Description  
标记答卷记录无效

#### RequestBody

- application/json

```ts
{
  id?: integer
  survey_id?: integer
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 用户UID
      uid?: string
      // openid
      openid?: string
      // 角色ID
      role_id?: string
      // 设备ID
      device_id?: string
      // ip
      ip?: string
      // 记录是否有效
      is_valid?: integer
      // 是否删除
      is_delete?: integer
      // 答题开始时间
      begin_time?: #/components/schemas/Timestamp
      // 答题结束时间
      end_time?: #/components/schemas/Timestamp
      // 额外信息
      extra?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 答题时间
      second?: string
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [PUT]/v1/survey/statistics/user-answer-record/set-valid/:surveyId/:id

- Description  
标记答卷记录有效

#### RequestBody

- application/json

```ts
{
  id?: integer
  survey_id?: integer
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 用户UID
      uid?: string
      // openid
      openid?: string
      // 角色ID
      role_id?: string
      // 设备ID
      device_id?: string
      // ip
      ip?: string
      // 记录是否有效
      is_valid?: integer
      // 是否删除
      is_delete?: integer
      // 答题开始时间
      begin_time?: #/components/schemas/Timestamp
      // 答题结束时间
      end_time?: #/components/schemas/Timestamp
      // 额外信息
      extra?: string
      // 创建时间
      ctime?: #/components/schemas/Timestamp
      // 答题时间
      second?: string
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/statistics/user-answer-record/survey-export/:surveyId

- Description  
创建导出任务

#### RequestBody

- application/json

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // 租户id
  clientid?: string
  // 问卷id
  survey_id?: integer
  // 文件名称
  name?: string
  // 文件类型
  file_type?: integer
  // 数据类型
  data_type?: integer
  // 数据有效性
  is_valid?: integer
  // url
  url?: string
  // 状态
  status?: integer
  // 完成时间范围 起始时间
  start_time?: string
  // 完成时间范围 结束时间
  end_time?: string
  // 创建时间
  create_time?: #/components/schemas/Timestamp
  // 更新时间
  update_time?: #/components/schemas/Timestamp
  // 完成时间
  complete_time?: #/components/schemas/Timestamp
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 租户id
      clientid?: string
      // 问卷id
      survey_id?: integer
      // 文件名称
      name?: string
      // 文件类型
      file_type?: integer
      // 数据类型
      data_type?: integer
      // 数据有效性
      is_valid?: integer
      // url
      url?: string
      // 状态
      status?: integer
      // 完成时间范围 起始时间
      start_time?: string
      // 完成时间范围 结束时间
      end_time?: string
      // 创建时间
      create_time?: #/components/schemas/Timestamp
      // 更新时间
      update_time?: #/components/schemas/Timestamp
      // 完成时间
      complete_time?: #/components/schemas/Timestamp
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/statistics/user-answer-record/survey-export/:surveyId/del

- Description  
删除导出任务

#### RequestBody

- application/json

```ts
{
  ids?: integer[]
  survey_id?: integer
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 租户id
      clientid?: string
      // 问卷id
      survey_id?: integer
      // 文件名称
      name?: string
      // 文件类型
      file_type?: integer
      // 数据类型
      data_type?: integer
      // 数据有效性
      is_valid?: integer
      // url
      url?: string
      // 状态
      status?: integer
      // 完成时间范围 起始时间
      start_time?: string
      // 完成时间范围 结束时间
      end_time?: string
      // 创建时间
      create_time?: #/components/schemas/Timestamp
      // 更新时间
      update_time?: #/components/schemas/Timestamp
      // 完成时间
      complete_time?: #/components/schemas/Timestamp
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/statistics/user-answer-record/survey-export/:surveyId/resetStatus

- Description  
重置导出任务状态

#### RequestBody

- application/json

```ts
// 详情
{
  id: integer
  survey_id: integer
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    ret?: integer
    msg?: string
    data: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 租户id
      clientid?: string
      // 问卷id
      survey_id?: integer
      // 文件名称
      name?: string
      // 文件类型
      file_type?: integer
      // 数据类型
      data_type?: integer
      // 数据有效性
      is_valid?: integer
      // url
      url?: string
      // 状态
      status?: integer
      // 完成时间范围 起始时间
      start_time?: string
      // 完成时间范围 结束时间
      end_time?: string
      // 创建时间
      create_time?: #/components/schemas/Timestamp
      // 更新时间
      update_time?: #/components/schemas/Timestamp
      // 完成时间
      complete_time?: #/components/schemas/Timestamp
    }
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [GET]/v1/survey/statistics/user-answer-record/survey-export/list/

- Description  
导出任务列表

#### Parameters(Query)

```ts
page?: integer
```

```ts
page_size?: integer
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    result: {
      // ID，自增主键，创建时此参数不需要传
      id?: integer
      // 租户id
      clientid?: string
      // 问卷id
      survey_id?: integer
      // 文件名称
      name?: string
      // 文件类型
      file_type?: integer
      // 数据类型
      data_type?: integer
      // 数据有效性
      is_valid?: integer
      // url
      url?: string
      // 状态
      status?: integer
      // 完成时间范围 起始时间
      start_time?: string
      // 完成时间范围 结束时间
      end_time?: string
      // 创建时间
      create_time?: #/components/schemas/Timestamp
      // 更新时间
      update_time?: #/components/schemas/Timestamp
      // 完成时间
      complete_time?: #/components/schemas/Timestamp
    }[]
    totalCounts?: integer
    currentPage?: integer
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [POST]/v1/survey/sync/:id

- Description  
同步问卷

#### RequestBody

- application/json

```ts
{
  id?: integer
  client_id?: string
}
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    // string ID = 1;               // 标识符	string CTime = 2; // 创建时间	string MTime = 3; // 修改时间	string Editor = 4;           // 编辑者	int32 IsDelete = 5;           // 删除标记	Schema PreviewSchema = 6;    // 预览模式	string HashCode = 7;         // 哈希码	int32 IsPause = 8;            // 暂停标记	int32 IsClosed = 9;           // 关闭标记	int32 IsOpened = 10;          // 打开标记	int32 IsModifyUnpublish = 11; // 修改未发布标记	string ClientId = 12;        // 客户端ID
    data?: #/components/schemas/SyncSurveyResponseData
    code?: integer
    success?: boolean
    timestamp?: string
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

***

### [PUT]/v1/survey/update/:id

#### Parameters(Query)

```ts
id?: integer
```

```ts
name?: string
```

```ts
setting.baseRuleConfig.loginType?: string
```

```ts
setting.baseRuleConfig.timeLimitConfig.isTimeLimit?: boolean
```

```ts
setting.baseRuleConfig.timeLimitConfig.stime?: string
```

```ts
setting.baseRuleConfig.timeLimitConfig.etime?: string
```

```ts
setting.baseRuleConfig.isEndPreview?: boolean
```

```ts
setting.baseRuleConfig.isGoOnAnswer?: boolean
```

```ts
setting.baseRuleConfig.answerTimesConfig.limitType?: integer
```

```ts
setting.baseRuleConfig.answerTimesConfig.times?: integer
```

```ts
setting.baseRuleConfig.languageList?: string[]
```

```ts
setting.baseRuleConfig.deliverList?: string[]
```

```ts
setting.giftConfig.isGiveOutByCms?: boolean
```

```ts
setting.giftConfig.giveOutType?: string
```

```ts
setting.giftConfig.preAwardConfig.id?: string
```

```ts
setting.giftConfig.redeemConfig?: string
```

```ts
setting.answerLimitConfig.limitType?: string
```

```ts
setting.zoneIds?: integer[]
```

```ts
setting.materialsConfig.autoLatestMaterial?: boolean
```

```ts
setting.materialsConfig.materialVersion?: string
```

```ts
setting.footerConfig.url?: string
```

```ts
setting.footerConfig.name?: string
```

```ts
setting.sourceConfig.cityUrl?: string
```

```ts
schema?: string
```

```ts
languages?: string
```

#### Responses

- 200 OK

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
  data: {
    success?: boolean
  }
}
```

- default Default error response

`application/json`

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```

## References

### #/components/schemas/Agreement

```ts
{
  image?: string
  text?: string
  link?: string
}
```

### #/components/schemas/AnswerLimitConfig

```ts
{
  limitType?: string
}
```

### #/components/schemas/AnswerTimesConfig

```ts
{
  limitType?: integer
  times?: integer
}
```

### #/components/schemas/BaseRuleConfig

```ts
{
  loginType?: string
  timeLimitConfig: {
    isTimeLimit?: boolean
    stime?: string
    etime?: string
  }
  isEndPreview?: boolean
  isGoOnAnswer?: boolean
  answerTimesConfig: {
    limitType?: integer
    times?: integer
  }
  languageList?: string[]
  deliverList?: string[]
}
```

### #/components/schemas/ConfigProps

```ts
{
  isQuestion?: boolean
  questionType?: string
  valueType?: string
  uniqueKey?: string
  questionId?: integer
  statisticsMethod?: string
  isAddress?: boolean
}
```

### #/components/schemas/DataItem

```ts
{
  value?: integer
  label?: string
}
```

### #/components/schemas/DelSurveyExportTaskReq

```ts
{
  ids?: integer[]
  survey_id?: integer
}
```

### #/components/schemas/Deliver

```ts
{
  default_language?: string
  region?: string
  official_website_host?: string
  survey_host?: string
}
```

### #/components/schemas/FooterConfig

```ts
{
  url?: string
  name?: string
}
```

### #/components/schemas/GetDeliverListResponse

```ts
// 投放列表
{
  deliver: {
    default_language?: string
    region?: string
    official_website_host?: string
    survey_host?: string
  }[]
}
```

### #/components/schemas/GetZoneListResponse

```ts
{
  data: {
    value?: integer
    label?: string
  }[]
}
```

### #/components/schemas/GiftConfig

```ts
{
  isGiveOutByCms?: boolean
  giveOutType?: string
  preAwardConfig: {
    id?: string
  }
  redeemConfig?: string
}
```

### #/components/schemas/InputMethodListRequest

```ts
{
  // 当前页码，默认1
  page?: integer
  // 每页条数，默认10
  page_size?: integer
  surveyId?: integer
  requestConfig: {
    question_unique_key?: string
    config_props: {
      isQuestion?: boolean
      questionType?: string
      valueType?: string
      uniqueKey?: string
      questionId?: integer
      statisticsMethod?: string
      isAddress?: boolean
    }
  }
}
```

### #/components/schemas/InputMethodListResponse

```ts
{
  device_id?: string
  id?: integer
  ip?: string
  openid?: string
  option?: string
  question?: string
  role_id?: string
  survey_record_id?: integer
  text?: string
}
```

### #/components/schemas/MaterialsConfig

```ts
{
  autoLatestMaterial?: boolean
  materialVersion?: string
}
```

### #/components/schemas/PreAwardConfig

```ts
{
  id?: string
}
```

### #/components/schemas/PublishSurveyResponse

```ts
{
  success?: boolean
}
```

### #/components/schemas/RecoverSurveyRecycleReq

```ts
{
  list?: integer[]
}
```

### #/components/schemas/RequestConfig

```ts
{
  question_unique_key?: string
  config_props: {
    isQuestion?: boolean
    questionType?: string
    valueType?: string
    uniqueKey?: string
    questionId?: integer
    statisticsMethod?: string
    isAddress?: boolean
  }
}
```

### #/components/schemas/Setting

```ts
// Settings message
{
  baseRuleConfig: {
    loginType?: string
    timeLimitConfig: {
      isTimeLimit?: boolean
      stime?: string
      etime?: string
    }
    isEndPreview?: boolean
    isGoOnAnswer?: boolean
    answerTimesConfig: {
      limitType?: integer
      times?: integer
    }
    languageList?: string[]
    deliverList?: string[]
  }
  giftConfig: {
    isGiveOutByCms?: boolean
    giveOutType?: string
    preAwardConfig: {
      id?: string
    }
    redeemConfig?: string
  }
  answerLimitConfig: {
    limitType?: string
  }
  zoneIds?: integer[]
  materialsConfig: {
    autoLatestMaterial?: boolean
    materialVersion?: string
  }
  footerConfig: {
    url?: string
    name?: string
  }
  sourceConfig: {
    cityUrl?: string
    agreements: {
      image?: string
      text?: string
      link?: string
    }[]
  }
}
```

### #/components/schemas/SourceConfig

```ts
{
  cityUrl?: string
  agreements: {
    image?: string
    text?: string
    link?: string
  }[]
}
```

### #/components/schemas/Survey

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // clientid
  clientid?: string
  // 问卷名称
  name?: string
  // 问卷是否打开
  is_closed?: integer
  // 问卷是否暂停
  is_pause?: integer
  // 是否发布
  is_publish?: integer
  // 是否有修改未发布
  is_modify_unpublish?: integer
  // 是否开启过答题
  is_opened?: integer
  // 问卷开始时间
  stime?: #/components/schemas/Timestamp
  // 问卷结束时间
  etime?: #/components/schemas/Timestamp
  // 问卷类型
  type?: integer
  // 配置 schema
  schema?: string
  // 预览 schema
  preview_schema?: string
  // 问卷设置
  settings?: #/components/schemas/Setting
  // 问卷C端用到的配置
  web_settings?: string
  // 问卷C端用到的配置
  languages?: string
  // 问卷id-hash
  hash_code?: string
  // 是否删除
  is_delete?: integer
  // 删除时间
  deltime?: #/components/schemas/Timestamp
  // 备注
  remark?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 更新时间
  mtime?: #/components/schemas/Timestamp
  // 创建人
  creator?: string
  // 最近修改人
  editor?: string
  // isTimeLimit
  isTimeLimit?: boolean
  // 用户答卷数量
  allAnsweredUserCount?: integer
  // 状态
  status?: integer
  // 答卷统计
  fullValidUid?: integer
  questionStatisticsData?: string
}
```

### #/components/schemas/SurveyDelReq

```ts
{
  delList?: integer[]
}
```

### #/components/schemas/SurveyDetailRequest

```ts
// 问卷详情
{
  surveyId?: integer
  success?: boolean
}
```

### #/components/schemas/SurveyExportTask

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // 租户id
  clientid?: string
  // 问卷id
  survey_id?: integer
  // 文件名称
  name?: string
  // 文件类型
  file_type?: integer
  // 数据类型
  data_type?: integer
  // 数据有效性
  is_valid?: integer
  // url
  url?: string
  // 状态
  status?: integer
  // 完成时间范围 起始时间
  start_time?: string
  // 完成时间范围 结束时间
  end_time?: string
  // 创建时间
  create_time?: #/components/schemas/Timestamp
  // 更新时间
  update_time?: #/components/schemas/Timestamp
  // 完成时间
  complete_time?: #/components/schemas/Timestamp
}
```

### #/components/schemas/SurveyExportTaskDetailsReq

```ts
// 详情
{
  id: integer
  survey_id: integer
}
```

### #/components/schemas/SurveyExportTaskDetailsRes

```ts
{
  ret?: integer
  msg?: string
  data: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // 租户id
    clientid?: string
    // 问卷id
    survey_id?: integer
    // 文件名称
    name?: string
    // 文件类型
    file_type?: integer
    // 数据类型
    data_type?: integer
    // 数据有效性
    is_valid?: integer
    // url
    url?: string
    // 状态
    status?: integer
    // 完成时间范围 起始时间
    start_time?: string
    // 完成时间范围 结束时间
    end_time?: string
    // 创建时间
    create_time?: #/components/schemas/Timestamp
    // 更新时间
    update_time?: #/components/schemas/Timestamp
    // 完成时间
    complete_time?: #/components/schemas/Timestamp
  }
}
```

### #/components/schemas/SurveyExportTaskListRes

```ts
{
  result: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // 租户id
    clientid?: string
    // 问卷id
    survey_id?: integer
    // 文件名称
    name?: string
    // 文件类型
    file_type?: integer
    // 数据类型
    data_type?: integer
    // 数据有效性
    is_valid?: integer
    // url
    url?: string
    // 状态
    status?: integer
    // 完成时间范围 起始时间
    start_time?: string
    // 完成时间范围 结束时间
    end_time?: string
    // 创建时间
    create_time?: #/components/schemas/Timestamp
    // 更新时间
    update_time?: #/components/schemas/Timestamp
    // 完成时间
    complete_time?: #/components/schemas/Timestamp
  }[]
  totalCounts?: integer
  currentPage?: integer
}
```

### #/components/schemas/SurveyListRes

```ts
{
  list: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // clientid
    clientid?: string
    // 问卷名称
    name?: string
    // 问卷是否打开
    is_closed?: integer
    // 问卷是否暂停
    is_pause?: integer
    // 是否发布
    is_publish?: integer
    // 是否有修改未发布
    is_modify_unpublish?: integer
    // 是否开启过答题
    is_opened?: integer
    // 问卷开始时间
    stime?: #/components/schemas/Timestamp
    // 问卷结束时间
    etime?: #/components/schemas/Timestamp
    // 问卷类型
    type?: integer
    // 配置 schema
    schema?: string
    // 预览 schema
    preview_schema?: string
    // 问卷设置
    settings?: #/components/schemas/Setting
    // 问卷C端用到的配置
    web_settings?: string
    // 问卷C端用到的配置
    languages?: string
    // 问卷id-hash
    hash_code?: string
    // 是否删除
    is_delete?: integer
    // 删除时间
    deltime?: #/components/schemas/Timestamp
    // 备注
    remark?: string
    // 创建时间
    ctime?: #/components/schemas/Timestamp
    // 更新时间
    mtime?: #/components/schemas/Timestamp
    // 创建人
    creator?: string
    // 最近修改人
    editor?: string
    // isTimeLimit
    isTimeLimit?: boolean
    // 用户答卷数量
    allAnsweredUserCount?: integer
    // 状态
    status?: integer
    // 答卷统计
    fullValidUid?: integer
    questionStatisticsData?: string
  }[]
  totalCounts?: integer
  currentPage?: integer
}
```

### #/components/schemas/SurveyRecord

```ts
{
  // ID，自增主键，创建时此参数不需要传
  id?: integer
  // 用户UID
  uid?: string
  // openid
  openid?: string
  // 角色ID
  role_id?: string
  // 设备ID
  device_id?: string
  // ip
  ip?: string
  // 记录是否有效
  is_valid?: integer
  // 是否删除
  is_delete?: integer
  // 答题开始时间
  begin_time?: #/components/schemas/Timestamp
  // 答题结束时间
  end_time?: #/components/schemas/Timestamp
  // 额外信息
  extra?: string
  // 创建时间
  ctime?: #/components/schemas/Timestamp
  // 答题时间
  second?: string
}
```

### #/components/schemas/SurveyRecordConfDetailsRes

```ts
{
  recordDetail?: string
  surveyConfig?: string
  userRecord?: string
}
```

### #/components/schemas/SurveyRecordDetailsRequest

```ts
{
  delList?: integer[]
  survey_id?: integer
}
```

### #/components/schemas/SurveyRecordDetailsRes

```ts
{
  ret?: integer
  msg?: string
  data: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // 用户UID
    uid?: string
    // openid
    openid?: string
    // 角色ID
    role_id?: string
    // 设备ID
    device_id?: string
    // ip
    ip?: string
    // 记录是否有效
    is_valid?: integer
    // 是否删除
    is_delete?: integer
    // 答题开始时间
    begin_time?: #/components/schemas/Timestamp
    // 答题结束时间
    end_time?: #/components/schemas/Timestamp
    // 额外信息
    extra?: string
    // 创建时间
    ctime?: #/components/schemas/Timestamp
    // 答题时间
    second?: string
  }
}
```

### #/components/schemas/SurveyRecordListRes

```ts
{
  result: {
    // ID，自增主键，创建时此参数不需要传
    id?: integer
    // 用户UID
    uid?: string
    // openid
    openid?: string
    // 角色ID
    role_id?: string
    // 设备ID
    device_id?: string
    // ip
    ip?: string
    // 记录是否有效
    is_valid?: integer
    // 是否删除
    is_delete?: integer
    // 答题开始时间
    begin_time?: #/components/schemas/Timestamp
    // 答题结束时间
    end_time?: #/components/schemas/Timestamp
    // 额外信息
    extra?: string
    // 创建时间
    ctime?: #/components/schemas/Timestamp
    // 答题时间
    second?: string
  }[]
  totalCounts?: integer
  currentPage?: integer
}
```

### #/components/schemas/SurveyRecordsReq

```ts
{
  id?: integer
  survey_id?: integer
}
```

### #/components/schemas/SurveyRes

```ts
{
  id?: integer
}
```

### #/components/schemas/SyncSurveyRequest

```ts
{
  id?: integer
  client_id?: string
}
```

### #/components/schemas/SyncSurveyResponse

```ts
{
  // string ID = 1;               // 标识符	string CTime = 2; // 创建时间	string MTime = 3; // 修改时间	string Editor = 4;           // 编辑者	int32 IsDelete = 5;           // 删除标记	Schema PreviewSchema = 6;    // 预览模式	string HashCode = 7;         // 哈希码	int32 IsPause = 8;            // 暂停标记	int32 IsClosed = 9;           // 关闭标记	int32 IsOpened = 10;          // 打开标记	int32 IsModifyUnpublish = 11; // 修改未发布标记	string ClientId = 12;        // 客户端ID
  data?: #/components/schemas/SyncSurveyResponseData
  code?: integer
  success?: boolean
  timestamp?: string
}
```

### #/components/schemas/SyncSurveyResponseData

```ts
{
  data?: integer
  code?: integer
  success?: boolean
  timestamp?: string
}
```

### #/components/schemas/TimeLimitConfig

```ts
{
  isTimeLimit?: boolean
  stime?: string
  etime?: string
}
```

### #/components/schemas/Timestamp

```ts
{
  "type": "string",
  "format": "date-time"
}
```

### #/components/schemas/UpdateSurveyResponse

```ts
{
  success?: boolean
}
```

### #/components/schemas/_failReturn

```ts
{
  code?: integer
  info?: string
  request_id?: string
}
```