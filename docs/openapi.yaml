# Generated with protoc-gen-openapi
# https://gitlab.papegames.com/fringe/protoc-gen-openapi

openapi: 3.0.3
info:
    title: SurveyService API
    description: This API represents survey service.
    version: 0.0.1
servers:
    - url: https://survey.papegames.com
paths:
    /v1/survey/clear-logical-destory:
        post:
            tags:
                - SurveyService
            description: 清空回收站
            operationId: 清空回收站
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ClearAllSurveyRecycleRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 回收站
    /v1/survey/copy:
        post:
            tags:
                - SurveyService
            description: 复制问卷
            operationId: 复制问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/create:
        post:
            tags:
                - SurveyService
            description: 问卷创建
            operationId: 创建问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateSurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/delete:
        post:
            tags:
                - SurveyService
            description: 彻底删除
            operationId: 彻底删除
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyDelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 回收站
    /v1/survey/export/headers:
        post:
            tags:
                - SurveyService
            operationId: SurveyExportHeaders
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyExportHeadersReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyExportHeadersRes'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/export/user-cluster-submit:
        post:
            tags:
                - SurveyService
            operationId: SurveyExportUserClusterSubmit
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyExportUserClusterSubmitReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/general/get-deliver-list:
        get:
            tags:
                - SurveyService
            description: 获取投放列表
            operationId: 获取投放列表
            parameters:
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GetDeliverListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 投放
    /v1/survey/general/get-zone-list:
        get:
            tags:
                - SurveyService
            description: 获取区服列表
            operationId: 获取区服列表
            parameters:
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GetZoneListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: biz
    /v1/survey/general/upload:
        post:
            tags:
                - SurveyService
            operationId: Upload
            parameters:
                - name: file
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/UploadResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/get-latest-survey-by-survey-id:
        get:
            tags:
                - SurveyService
            operationId: GetLatestSurveyBySurveyId
            parameters:
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: survey_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GetLatestSurveyBySurveyIdResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/create:
        post:
            tags:
                - SurveyService
            description: 问卷组 - 新建
            operationId: SurveyGroupCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/detail:
        post:
            tags:
                - SurveyService
            description: 问卷组 - 详情
            operationId: SurveyGroupDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupDetailReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/CmsSurveyGroupInfo'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/list:
        post:
            tags:
                - SurveyService
            description: 问卷组 - 列表
            operationId: SurveyGroupList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyGroupListRes'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/overwrite/send:
        post:
            tags:
                - SurveyService
            operationId: SurveyGroupOverwriteSend
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupOverwriteSendReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/overwrite/sync:
        post:
            tags:
                - SurveyService
            operationId: SurveyGroupOverwriteSync
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupOverwriteSyncReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/sub_update:
        post:
            tags:
                - SurveyService
            description: 问卷组 - 部分字段更新
            operationId: SurveyGroupSubUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupSubUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/group/update:
        post:
            tags:
                - SurveyService
            description: 问卷组 - 编辑
            operationId: SurveyGroupUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyGroupUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/health:
        get:
            tags:
                - SurveyService
            operationId: 健康检查
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: p0
            x-apifox-folder: 基础接口
    /v1/survey/imp:
        post:
            tags:
                - SurveyService
            description: 同步问卷写入接口
            operationId: 问卷同步写入
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ImpSurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/list:
        get:
            tags:
                - SurveyService
            description: 问卷列表
            operationId: 问卷列表
            parameters:
                - name: page
                  in: query
                  description: 当前页码，默认1
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  description: 每页条数，默认10
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: query
                  description: 答卷id
                  schema:
                    type: integer
                    format: int64
                - name: name
                  in: query
                  description: 答卷名称
                  schema:
                    type: string
                - name: sort_ctime
                  in: query
                  description: 创建时间排序
                  schema:
                    type: string
                - name: status
                  in: query
                  description: 状态
                  schema:
                    type: integer
                    format: int32
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: id_list
                  in: query
                  description: 问卷ID数组
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/logical-destory:
        post:
            tags:
                - SurveyService
            description: 问卷删除
            operationId: 问卷删除
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyDelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/overwrite/send:
        post:
            tags:
                - SurveyService
            operationId: SurveyOverwriteSend
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyOverwriteSendReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/overwrite/sync:
        post:
            tags:
                - SurveyService
            operationId: SurveyOverwriteSync
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyOverwriteSyncReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/preview:
        get:
            tags:
                - SurveyService
            description: 问卷预览
            operationId: 问卷预览
            parameters:
                - name: id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/Survey'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/publish:
        post:
            tags:
                - SurveyService
            operationId: 发布问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PublishSurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 问卷列表
    /v1/survey/recover-all-logical-destory:
        post:
            tags:
                - SurveyService
            description: 恢复所有问卷
            operationId: 恢复所有问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Survey'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 回收站
    /v1/survey/recover-logical-destory:
        post:
            tags:
                - SurveyService
            description: 恢复问卷
            operationId: 恢复问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RecoverSurveyRecycleReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 回收站
    /v1/survey/recycle-list:
        get:
            tags:
                - SurveyService
            description: 回收站-问卷列表
            operationId: 问卷列表
            parameters:
                - name: page
                  in: query
                  description: 当前页码，默认1
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  description: 每页条数，默认10
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: query
                  description: 答卷id
                  schema:
                    type: integer
                    format: int64
                - name: name
                  in: query
                  description: 答卷名称
                  schema:
                    type: string
                - name: sort_ctime
                  in: query
                  description: 创建时间排序
                  schema:
                    type: string
                - name: status
                  in: query
                  description: 状态
                  schema:
                    type: integer
                    format: int32
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: id_list
                  in: query
                  description: 问卷ID数组
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 回收站
    /v1/survey/rule/detail:
        get:
            tags:
                - SurveyService
            description: 问卷规则-问卷管理-问卷详情
            operationId: 问卷规则
            parameters:
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyStatisticsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/rule/update:
        post:
            tags:
                - SurveyService
            operationId: StatisticsUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/StatisticsUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/set-status:
        post:
            tags:
                - SurveyService
            description: 问卷暂停/开启作答
            operationId: 问卷暂停/开启作答
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveySetStatusRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/settings/get-pre-award-template-list:
        get:
            tags:
                - SurveyService
            description: 获取平台邮件模板列表
            operationId: GetPreAwardTemplateList
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/PreAwardTemplateResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/settings/get-valid-redeem-config-list:
        get:
            tags:
                - SurveyService
            description: 获取兑换码列表
            operationId: GetValidRedeemConfigList
            parameters:
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/ValidRedeemConfigResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/show:
        get:
            tags:
                - SurveyService
            description: 问卷展示
            operationId: 问卷展示
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/Survey'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/detail:
        post:
            tags:
                - SurveyService
            description: 问卷统计详情
            operationId: 回收概况-问卷统计
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/RawMessage'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 问卷统计
    /v1/survey/statistics/detail/input-method-list:
        post:
            tags:
                - SurveyService
            operationId: 回收概况
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyInputMethodListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/RawMessage'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 问卷列表
    /v1/survey/statistics/detail2:
        post:
            tags:
                - SurveyService
            description: 问卷统计详情 - v2
            operationId: 回收概况-问卷统计
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/RawMessage'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 问卷统计
    /v1/survey/statistics/list:
        get:
            tags:
                - SurveyService
            description: 问卷统计报告-列表
            operationId: 问卷规则
            parameters:
                - name: survey_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: client_id
                  in: query
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyDetailResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/delete:
        post:
            tags:
                - SurveyService
            description: 答卷列表删除
            operationId: 答卷列表删除
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRecordDetailsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/detail:
        get:
            tags:
                - SurveyService
            description: 查看答卷-问卷详情
            operationId: 查看答卷
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: survey_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyRecordConfDetailsRes'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/invalid-list:
        get:
            tags:
                - SurveyService
            description: 无效答卷列表
            operationId: 无效答卷列表
            parameters:
                - name: page
                  in: query
                  description: 当前页码，默认1
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  description: 每页条数，默认10
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: query
                  description: 答卷id
                  schema:
                    type: integer
                    format: int64
                - name: openid
                  in: query
                  description: 平台账号id
                  schema:
                    type: string
                - name: role_id
                  in: query
                  description: 玩家角色id
                  schema:
                    type: string
                - name: ip
                  in: query
                  description: IP
                  schema:
                    type: string
                - name: device_id
                  in: query
                  description: 设备id
                  schema:
                    type: string
                - name: sort_id
                  in: query
                  description: sort_id
                  schema:
                    type: string
                - name: sort_end_time
                  in: query
                  description: sort_end_time
                  schema:
                    type: string
                - name: sort_second
                  in: query
                  description: sort_second
                  schema:
                    type: string
                - name: survey_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyRecordListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/list:
        post:
            tags:
                - SurveyService
            description: 回收答卷列表
            operationId: 回收答卷列表
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRecordListV2Request'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyRecordListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/list-v2:
        post:
            tags:
                - SurveyService
            description: 回收答卷列表
            operationId: 回收答卷列表
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRecordListV2Request'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/RawMessage'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/set-invalid:
        post:
            tags:
                - SurveyService
            description: 标记答卷记录无效
            operationId: 标记答卷记录无效
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SetValidSurveyRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/set-valid:
        post:
            tags:
                - SurveyService
            description: 标记答卷记录有效
            operationId: 标记答卷记录有效
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyRecordsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/statistics/user-answer-record/survey-export/create:
        post:
            tags:
                - SurveyService
            description: 创建导出任务
            operationId: 创建导出任务
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyExportTask'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 导出
    /v1/survey/statistics/user-answer-record/survey-export/del:
        post:
            tags:
                - SurveyService
            description: 删除导出任务
            operationId: 删除导出任务
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DelSurveyExportTaskReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 导出
    /v1/survey/statistics/user-answer-record/survey-export/list:
        get:
            tags:
                - SurveyService
            description: 导出任务列表
            operationId: 导出任务列表
            parameters:
                - name: page
                  in: query
                  description: 当前页码，默认1
                  schema:
                    type: integer
                    format: int32
                - name: page_size
                  in: query
                  description: 每页条数，默认10
                  schema:
                    type: integer
                    format: int32
                - name: client_id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: survey_id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyExportTaskListResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 导出
    /v1/survey/statistics/user-answer-record/survey-export/resetStatus:
        post:
            tags:
                - SurveyService
            description: 重置导出任务状态
            operationId: 重置导出状态
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyExportTaskDetailsReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 导出
    /v1/survey/sync:
        post:
            tags:
                - SurveyService
            description: 同步问卷
            operationId: 同步问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SyncSurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 答卷列表
    /v1/survey/update:
        post:
            tags:
                - SurveyService
            operationId: 更新问卷
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateSurveyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
            level: P2
            x-apifox-folder: 问卷列表
    /v1/survey/user/check:
        get:
            tags:
                - SurveyService
            operationId: UserCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/user/get-info:
        get:
            tags:
                - SurveyService
            operationId: GetUserInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetUserInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/GetUserInfoResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/view/create:
        post:
            tags:
                - SurveyService
            description: 问卷筛选器创建
            operationId: SurveyViewCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyViewCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyViewCreateRes'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/view/delete:
        post:
            tags:
                - SurveyService
            description: 问卷筛选器删除
            operationId: SurveyViewDelete
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyViewDeleteReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/view/list:
        get:
            tags:
                - SurveyService
            description: 问卷筛选器列表
            operationId: SurveyViewList
            parameters:
                - name: survey_id
                  in: query
                  description: 答卷ID
                  schema:
                    type: integer
                    format: int64
                - name: kind
                  in: query
                  description: 视图类型，1：回收列表，2：交叉分析
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/SurveyViewListRes'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/survey/view/update:
        post:
            tags:
                - SurveyService
            description: 问卷筛选器编辑
            operationId: SurveyViewUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SurveyView'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
components:
    schemas:
        ClearAllSurveyRecycleRequest:
            required:
                - client_id
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
        Clientlist:
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                memo:
                    type: string
                default_language:
                    type: string
                group_id:
                    type: string
                group_name:
                    type: string
        CmsSurveyGroupInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: uint64
                clientid:
                    type: string
                name:
                    type: string
                is_publish:
                    type: integer
                    format: int32
                limit_type:
                    type: integer
                    format: int32
                type:
                    type: integer
                    format: int32
                settings:
                    type: string
                hash_code:
                    type: string
                is_delete:
                    type: integer
                    format: int32
                ctime:
                    type: string
                mtime:
                    type: string
                creator:
                    type: string
                editor:
                    type: string
            description: CmsSurveyGroupInfo
        ConfigProps:
            type: object
            properties:
                is_question:
                    type: boolean
                question_type:
                    type: string
                value_type:
                    type: string
                unique_key:
                    type: string
                question_id:
                    type: integer
                    format: int32
                statistics_method:
                    type: string
                is_address:
                    type: boolean
        CreateSurveyRequest:
            required:
                - client_id
                - name
                - settings
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                name:
                    type: string
                settings:
                    type: string
                stime:
                    type: string
                etime:
                    type: string
        DataItem:
            type: object
            properties:
                value:
                    type: integer
                    format: int32
                label:
                    type: string
        DelSurveyExportTaskReq:
            required:
                - ids
                - survey_id
                - client_id
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                survey_id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
        Deliver:
            type: object
            properties:
                default_language:
                    type: string
                region:
                    type: string
                official_website_host:
                    type: string
                survey_host:
                    type: string
        Desc:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        Detail:
            type: object
            properties:
                uniqueKey:
                    type: string
                count:
                    type: string
                selectCount:
                    type: string
                detail:
                    type: array
                    items:
                        $ref: '#/components/schemas/OptionDetail'
        DisplayLogic:
            type: object
            properties:
                currentQuestionId:
                    type: string
                rules:
                    type: array
                    items:
                        $ref: '#/components/schemas/Rule'
                relation:
                    type: string
        FooterContent:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        GetDeliverListResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Deliver'
            description: 投放列表
        GetLatestSurveyBySurveyIdResponse:
            required:
                - id
                - survey_id
                - name
                - schema
                - settings
                - web_settings
            type: object
            properties:
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                survey_id:
                    type: integer
                    format: int64
                name:
                    type: string
                schema:
                    type: string
                settings:
                    type: string
                web_settings:
                    type: string
        GetUserInfoRequest:
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
        GetUserInfoResponse:
            type: object
            properties:
                userInfo:
                    $ref: '#/components/schemas/Userinfo'
                clientList:
                    type: array
                    items:
                        $ref: '#/components/schemas/Clientlist'
                permission:
                    $ref: '#/components/schemas/Permission'
        GetZoneListResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/DataItem'
        HeaderContent:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        Image:
            type: object
            properties:
                languKey:
                    type: string
        ImpSurveyRequest:
            required:
                - clientid
            type: object
            properties:
                clientid:
                    type: string
                    description: client_id
                name:
                    type: string
                    description: 问卷名称
                stime:
                    type: string
                    description: 问卷开始时间
                etime:
                    type: string
                    description: 问卷结束时间
                schema:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: 配置 schema
                previewSchema:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: 预览 schema
                settings:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: 问卷设置
                webSettings:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: 问卷C端用到的配置
                languages:
                    allOf:
                        - $ref: '#/components/schemas/RawMessage'
                    description: 问卷C端用到的配置
                apiVersion:
                    type: integer
                    format: int32
                keyValue:
                    type: string
                font:
                    type: string
                creator:
                    type: string
                    description: 创建人
        Label:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        OptionDetail:
            type: object
            properties:
                value:
                    type: string
                label:
                    $ref: '#/components/schemas/Label'
                image_position:
                    type: integer
                    format: int32
                blank_fill:
                    type: string
                image:
                    $ref: '#/components/schemas/Image'
                count:
                    type: integer
                    format: int32
                proportion:
                    type: string
        OuterQuestionStatisticsData:
            type: object
            properties:
                valid_answer_total:
                    type: integer
                    format: int32
                valid_user_total:
                    type: integer
                    format: int32
                detail:
                    type: array
                    items:
                        $ref: '#/components/schemas/Detail'
        Permission:
            type: object
            properties:
                surveySummary_get:
                    type: boolean
                surveyManage_add:
                    type: boolean
                surveyManage_edit:
                    type: boolean
                surveySummary_export:
                    type: boolean
                surveyManage_delete:
                    type: boolean
                member_get:
                    type: boolean
                member_add:
                    type: boolean
                member_delete:
                    type: boolean
        PreAwardTemplate:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                client_id:
                    type: string
                sender:
                    type: string
                title:
                    type: string
                body:
                    type: string
                content:
                    type: string
                memo:
                    type: string
                editor:
                    type: string
                stime:
                    type: string
                etime:
                    type: string
                mtime:
                    type: string
                ctime:
                    type: string
                permanent:
                    type: integer
                    format: int32
                creator:
                    type: string
                key:
                    type: integer
                    format: int32
        PreAwardTemplateResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/PreAwardTemplate'
                total:
                    type: integer
                    format: int64
        PublishSurveyRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
        QuestionBaseConfig:
            type: object
            properties:
                questionTitle:
                    $ref: '#/components/schemas/Title'
                questionDesc:
                    $ref: '#/components/schemas/Desc'
                questionTip:
                    $ref: '#/components/schemas/Tip'
        QuestionComponentConfig:
            type: object
            properties:
                headerContent:
                    $ref: '#/components/schemas/HeaderContent'
                FooterContent:
                    $ref: '#/components/schemas/FooterContent'
                dimension:
                    type: integer
                    format: int32
                startValue:
                    type: integer
                    format: int32
                sort:
                    type: integer
                    format: int32
        QuestionList:
            type: object
            properties:
                componentName:
                    type: string
                componentTitle:
                    type: string
                questionBaseConfig:
                    $ref: '#/components/schemas/QuestionBaseConfig'
                configProps:
                    $ref: '#/components/schemas/ConfigProps'
                questionLogicalConfig:
                    $ref: '#/components/schemas/QuestionLogicalConfig'
                questionComponentConfig:
                    $ref: '#/components/schemas/QuestionComponentConfig'
                questionSelectConfig:
                    $ref: '#/components/schemas/QuestionSelectConfig'
                requestConfig:
                    $ref: '#/components/schemas/RequestConfig'
                wrapperIndex:
                    type: integer
                    format: int32
        QuestionLogicalConfig:
            type: object
            properties:
                conditionDesc:
                    type: string
                displayLogic:
                    $ref: '#/components/schemas/DisplayLogic'
        QuestionSelectConfig:
            type: object
            properties:
                maxSelected:
                    type: integer
                    format: int32
                selectOptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/SelectOption'
                selectItemLayout:
                    type: boolean
                selectItemRandomSort:
                    type: boolean
        QuestionStatisticsData:
            type: object
            properties:
                valid_answer_total:
                    type: integer
                    format: int64
                valid_user_total:
                    type: integer
                    format: uint32
        RawMessage:
            type: object
        RecoverSurveyRecycleReq:
            type: object
            properties:
                list:
                    type: array
                    items:
                        type: integer
                        format: int64
                client_id:
                    type: integer
                    format: int64
        RelatedContentConfig:
            type: object
            properties:
                relation:
                    type: string
                chooseType:
                    type: string
                content:
                    type: array
                    items:
                        type: integer
                        format: int32
        RequestConfig:
            type: object
            properties:
                question_unique_key:
                    type: string
                config_props:
                    $ref: '#/components/schemas/ConfigProps'
                option_value:
                    type: string
        Rule:
            type: object
            properties:
                key:
                    type: string
                id:
                    type: string
                relatedContentConfig:
                    $ref: '#/components/schemas/RelatedContentConfig'
        SelectOption:
            type: object
            properties:
                value:
                    type: string
        SetValidSurveyRecordRequest:
            type: object
            properties:
                survey_id:
                    type: integer
                    format: int64
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
        StatisticsUpdateRequest:
            required:
                - id
                - client_id
                - name
            type: object
            properties:
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
                name:
                    type: string
                settings:
                    type: string
                stime:
                    type: string
                etime:
                    type: string
        Survey:
            required:
                - client_id
            type: object
            properties:
                id:
                    type: integer
                    description: ID，自增主键，创建时此参数不需要传
                    format: int64
                client_id:
                    type: integer
                    description: client_id
                    format: int64
                name:
                    type: string
                    description: 问卷名称
                is_closed:
                    type: integer
                    description: 问卷是否打开
                    format: int32
                is_pause:
                    type: integer
                    description: 问卷是否暂停
                    format: int32
                is_publish:
                    type: integer
                    description: 是否发布
                    format: int32
                is_modify_unpublish:
                    type: integer
                    description: 是否有修改未发布
                    format: int32
                is_opened:
                    type: integer
                    description: 是否开启过答题
                    format: int32
                stime:
                    type: string
                    description: 问卷开始时间
                etime:
                    type: string
                    description: 问卷结束时间
                type:
                    type: integer
                    description: 问卷类型
                    format: int32
                schema:
                    type: string
                    description: 配置 schema
                preview_schema:
                    type: string
                    description: 预览 schema
                settings:
                    type: string
                    description: 问卷设置
                web_settings:
                    type: string
                    description: 问卷C端用到的配置
                languages:
                    type: string
                    description: 问卷C端用到的配置
                hash_code:
                    type: string
                    description: 问卷id-hash
                is_delete:
                    type: integer
                    description: 是否删除
                    format: int32
                deltime:
                    type: string
                    description: 删除时间
                key_value:
                    type: string
                font:
                    type: string
                remark:
                    type: string
                    description: 备注
                ctime:
                    readOnly: true
                    type: string
                    description: 创建时间
                mtime:
                    readOnly: true
                    type: string
                    description: 更新时间
                creator:
                    type: string
                    description: 创建人
                editor:
                    type: string
                    description: 最近修改人
                is_time_limit:
                    type: boolean
                    description: isTimeLimit
                all_answered_user_count:
                    type: integer
                    description: 用户答卷数量
                    format: int64
                status:
                    type: integer
                    description: 状态
                    format: int32
                full_valid_uid:
                    type: integer
                    description: 答卷统计
                    format: int64
                question_statistics_data:
                    $ref: '#/components/schemas/QuestionStatisticsData'
                web_path_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/WebPath'
        SurveyConfig:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
                name:
                    type: string
                is_closed:
                    type: integer
                    format: int32
                is_pause:
                    type: integer
                    format: int32
                is_publish:
                    type: integer
                    format: int32
                is_modify_unpublish:
                    type: integer
                    format: int32
                is_opened:
                    type: integer
                    format: int32
                stime:
                    type: string
                etime:
                    type: string
                type:
                    type: integer
                    format: int32
                schema:
                    type: string
                preview_schema:
                    type: string
                settings:
                    type: string
                web_settings:
                    type: string
                languages:
                    type: string
                hash_code:
                    type: string
                is_delete:
                    type: integer
                    format: int32
                deltime:
                    type: string
                remark:
                    type: string
                ctime:
                    type: string
                mtime:
                    type: string
                creator:
                    type: string
                editor:
                    type: string
                is_time_limit:
                    type: boolean
                web_path_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/WebPath'
                status:
                    type: integer
                    format: int32
                question_statistics_data:
                    $ref: '#/components/schemas/QuestionStatisticsData'
        SurveyDelRequest:
            required:
                - client_id
                - del_list
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                del_list:
                    type: array
                    items:
                        type: integer
                        format: int64
        SurveyDetailResponse:
            type: object
            properties:
                question_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/QuestionList'
                survey_config:
                    $ref: '#/components/schemas/SurveyConfig'
                question_statistics_data:
                    $ref: '#/components/schemas/OuterQuestionStatisticsData'
        SurveyExportHeadersReq:
            required:
                - client_id
                - survey_id
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                survey_id:
                    type: integer
                    format: int64
        SurveyExportHeadersRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyExportHeadersRes_Question'
                version:
                    type: string
        SurveyExportHeadersRes_Question:
            type: object
            properties:
                question_unique_key:
                    type: string
                question_title:
                    type: string
                statistics_method:
                    type: string
                question_type:
                    type: string
                select_mode:
                    type: string
                question_id:
                    type: integer
                    format: int32
                question_row_titles:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyExportHeadersRes_QuestionRowTitle'
                select_options:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyExportHeadersRes_SelectOptions'
        SurveyExportHeadersRes_QuestionRowTitle:
            type: object
            properties:
                row_unique_key:
                    type: string
                role_title:
                    type: string
        SurveyExportHeadersRes_SelectOptions:
            type: object
            properties:
                value:
                    type: string
                label:
                    type: string
                hasBlank:
                    type: boolean
        SurveyExportTask:
            required:
                - survey_id
            type: object
            properties:
                id:
                    type: integer
                    description: ID，自增主键，创建时此参数不需要传
                    format: int64
                client_id:
                    maxLength: 128
                    type: integer
                    description: 租户id
                    format: int64
                survey_id:
                    type: integer
                    description: 问卷id
                    format: int64
                name:
                    maxLength: 125
                    type: string
                    description: 文件名称
                file_type:
                    type: integer
                    description: 文件类型
                    format: int32
                data_type:
                    type: integer
                    description: 数据类型
                    format: int32
                is_valid:
                    type: integer
                    description: 数据有效性
                    format: int32
                url:
                    type: string
                    description: url
                status:
                    type: integer
                    description: 状态
                    format: int32
                start_time:
                    type: string
                    description: 完成时间范围 起始时间
                end_time:
                    type: string
                    description: 完成时间范围 结束时间
                view_content:
                    type: string
                    description: 筛选内容
                data_source:
                    type: integer
                    description: 数据源，0：全部，1：随机
                    format: int32
                data_num:
                    type: integer
                    description: 随机数量
                    format: int32
                extra:
                    type: string
                    description: 拓展字段
                create_time:
                    readOnly: true
                    type: string
                    description: 创建时间
                update_time:
                    type: string
                    description: 更新时间
                complete_time:
                    readOnly: true
                    type: string
                    description: 完成时间
        SurveyExportTaskDetailsReq:
            required:
                - id
                - survey_id
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                survey_id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
            description: 详情
        SurveyExportTaskListResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyExportTask'
                total:
                    type: integer
                    format: int64
        SurveyExportUserClusterSubmitReq:
            required:
                - client_id
                - user_strategy
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                user_strategy:
                    allOf:
                        - $ref: '#/components/schemas/UserStrategy'
                    description: 用户分群信息
        SurveyGroupCreateReq:
            required:
                - client_id
                - name
                - limit_type
                - type
                - settings
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                name:
                    type: string
                    description: 名称
                limit_type:
                    type: integer
                    description: 答题次数限制
                    format: int32
                type:
                    type: integer
                    description: 问卷组类型 1:ip 2:语言
                    format: int32
                settings:
                    type: string
                    description: 问卷组设置
        SurveyGroupDetailReq:
            required:
                - client_id
                - id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                id:
                    type: integer
                    description: 组ID
                    format: int64
        SurveyGroupListReq:
            required:
                - client_id
                - id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                id:
                    type: integer
                    description: 问卷组ID
                    format: int64
        SurveyGroupListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/CmsSurveyGroupInfo'
                    description: 问卷组列表
                total:
                    type: integer
                    description: 总数
                    format: int64
        SurveyGroupOverwriteSendReq:
            required:
                - client_id
                - survey_group_id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                survey_group_id:
                    type: integer
                    description: 问卷组ID
                    format: int64
        SurveyGroupOverwriteSyncReq:
            required:
                - client_id
                - survey_group
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                survey_group:
                    type: string
                    description: 问卷组
        SurveyGroupSubUpdateReq:
            required:
                - client_id
                - id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                id:
                    type: integer
                    description: 问卷组ID
                    format: int64
                type:
                    type: integer
                    description: 操作类型
                    format: enum
                value:
                    type: string
                    description: 要更新的值
        SurveyGroupUpdateReq:
            required:
                - client_id
                - id
                - name
                - limit_type
                - type
                - settings
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                id:
                    type: integer
                    description: 问卷组ID
                    format: int64
                name:
                    type: string
                    description: 名称
                limit_type:
                    type: integer
                    description: 答题次数限制
                    format: int32
                type:
                    type: integer
                    description: 问卷组类型 1:ip 2:语言
                    format: int32
                settings:
                    type: string
                    description: 问卷组设置
        SurveyInputMethodListRequest:
            required:
                - survey_id
                - client_id
            type: object
            properties:
                page:
                    type: integer
                    description: 当前页码，默认1
                    format: int32
                page_size:
                    type: integer
                    description: 每页条数，默认10
                    format: int32
                survey_id:
                    type: integer
                    format: int64
                request_config:
                    $ref: '#/components/schemas/RequestConfig'
                client_id:
                    type: integer
                    format: int64
                filter_null_value:
                    type: integer
                    description: 过滤空
                    format: int32
        SurveyListResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/Survey'
                total:
                    type: integer
                    format: int64
        SurveyOverwriteSendReq:
            required:
                - client_id
                - survey_id
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                survey_id:
                    type: integer
                    description: 问卷ID
                    format: int64
        SurveyOverwriteSyncReq:
            required:
                - client_id
                - survey
            type: object
            properties:
                client_id:
                    type: integer
                    description: 租户ID
                    format: int64
                survey:
                    type: string
                    description: 问卷
        SurveyRecord:
            type: object
            properties:
                id:
                    type: integer
                    description: ID，自增主键，创建时此参数不需要传
                    format: int64
                uid:
                    maxLength: 128
                    type: string
                    description: 用户UID
                openid:
                    maxLength: 125
                    type: string
                    description: openid
                role_id:
                    type: string
                    description: 角色ID
                device_id:
                    type: string
                    description: 设备ID
                ip:
                    type: string
                    description: ip
                is_valid:
                    type: integer
                    description: 记录是否有效
                    format: int32
                is_delete:
                    type: integer
                    description: 是否删除
                    format: int32
                begin_time:
                    type: string
                    description: 答题开始时间
                end_time:
                    type: string
                    description: 答题结束时间
                extra:
                    type: string
                    description: 额外信息
                ctime:
                    readOnly: true
                    type: string
                    description: 创建时间
                second:
                    type: string
                    description: 答题时间
        SurveyRecordConfDetailsRes:
            type: object
            properties:
                record_detail:
                    type: string
                survey_config:
                    type: string
                user_record:
                    type: string
        SurveyRecordDetailsRequest:
            type: object
            properties:
                del_list:
                    type: array
                    items:
                        type: integer
                        format: int64
                survey_id:
                    type: integer
                    format: int64
        SurveyRecordListResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyRecord'
                total:
                    type: integer
                    format: int64
        SurveyRecordListV2Request:
            required:
                - survey_id
                - client_id
            type: object
            properties:
                survey_id:
                    minimum: !!float 1
                    type: integer
                    description: 问卷ID
                    format: int64
                client_id:
                    type: integer
                    description: 客户端ID
                    format: int64
                view_content:
                    type: string
                    description: 筛选器内容
                page:
                    type: integer
                    description: "limit\tint32 limit = 4 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1]; 当前页码，默认1"
                    format: int32
                page_size:
                    type: integer
                    description: 每页条数，默认50
                    format: int32
        SurveyRecordsRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                survey_id:
                    type: integer
                    format: int64
        SurveyRequest:
            type: object
            properties:
                survey_id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
        SurveyResponse:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        SurveySetStatusRequest:
            required:
                - id
                - status
                - client_id
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int32
                client_id:
                    type: integer
                    format: int64
        SurveyStatisticsResponse:
            required:
                - name
                - settings
            type: object
            properties:
                name:
                    type: string
                settings:
                    type: string
                stime:
                    type: string
                etime:
                    type: string
        SurveyView:
            type: object
            properties:
                id:
                    type: integer
                    description: ID，自增主键，创建时此参数不需要传
                    format: int64
                survey_id:
                    type: integer
                    description: 问卷ID
                    format: int64
                name:
                    maxLength: 50
                    type: string
                    description: 筛选器名
                content:
                    type: string
                    description: 筛选器内容
                is_delete:
                    type: integer
                    description: 是否删除
                    format: int32
                kind:
                    type: integer
                    description: 视图类型
                    format: int32
                ctime:
                    readOnly: true
                    type: string
                    description: 创建时间
                mtime:
                    readOnly: true
                    type: string
                    description: 修改时间
                creator:
                    readOnly: true
                    type: string
                    description: 创建人
                editor:
                    readOnly: true
                    type: string
                    description: 最近修改人
        SurveyViewCreateReq:
            type: object
            properties:
                survey_id:
                    type: integer
                    description: 答卷ID
                    format: int64
                name:
                    type: string
                    description: 过滤器名称
                content:
                    type: string
                    description: 过滤期内容
                kind:
                    type: integer
                    description: 视图类型，1：回收列表，2：交叉分析
                    format: int32
        SurveyViewCreateRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        SurveyViewDeleteReq:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 要删除的筛选器ID列表
        SurveyViewListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/SurveyView'
                    description: 筛选器列表
        SyncSurveyRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                client_id:
                    type: integer
                    format: int64
                material_version:
                    type: string
        Tip:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        Title:
            type: object
            properties:
                value:
                    type: string
                languKey:
                    type: string
        UpdateSurveyRequest:
            required:
                - id
                - name
                - schema
                - languages
            type: object
            properties:
                id:
                    minimum: !!float 1
                    type: integer
                    format: int64
                name:
                    type: string
                schema:
                    type: string
                languages:
                    type: string
                web_settings:
                    type: string
                key_value:
                    type: string
                font:
                    type: string
        UploadResponse:
            type: object
            properties:
                url:
                    type: string
        UserCheckRequest:
            type: object
            properties:
                client_id:
                    type: integer
                    format: int64
                name:
                    type: string
        UserStrategy:
            required:
                - cluster_name
                - entity_name
            type: object
            properties:
                cluster_name:
                    type: string
                    description: 用户分群名称
                entity_name:
                    type: string
                    description: 用户分群基于的用户维度, 如vroleid, vopenid
                version:
                    type: string
                    description: 版本
        Userinfo:
            type: object
            properties:
                uid:
                    type: string
                name:
                    type: string
                nickname:
                    type: string
                phone:
                    type: string
                avatar:
                    type: string
                email:
                    type: string
                client_id:
                    type: integer
                    format: int64
        ValidRedeemConfig:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                client_id:
                    type: string
                redeem_id:
                    type: integer
                    format: int32
                redeem_head:
                    type: string
                redeem_type:
                    type: integer
                    format: int32
                is_custom:
                    type: integer
                    format: int32
                redeem_number:
                    type: integer
                    format: int32
                redeem_timefor1name:
                    type: integer
                    format: int32
                redeem_timefor1id:
                    type: integer
                    format: int32
                redeem_open_time:
                    type: string
                redeem_close_time:
                    type: string
                redeem_item:
                    type: string
                redeem_gift:
                    type: string
                redeem_channel:
                    type: string
                redeem_create_id:
                    type: string
                redeem_whitelist:
                    type: string
                extra:
                    type: string
                memo:
                    type: string
                mtime:
                    type: string
                ctime:
                    type: string
                redeem_auto:
                    type: integer
                    format: int32
                notify:
                    type: integer
                    format: int32
                redeem_head2:
                    type: string
        ValidRedeemConfigResponse:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/ValidRedeemConfig'
                total:
                    type: integer
                    format: int64
        WebPath:
            type: object
            properties:
                region:
                    type: string
                web_path:
                    type: string
        _failReturn:
            type: object
            properties:
                code:
                    example: 400
                    type: integer
                    format: int32
                info:
                    example: error
                    type: string
                request_id:
                    example: 16vHbfABAd
                    type: string
tags:
    - name: SurveyService
