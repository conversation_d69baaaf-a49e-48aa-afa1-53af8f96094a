#!/bin/bash

# This function converts a camelCase string to snake_case.
camel_to_snake() {
    echo "$1" | sed -r 's/([^A-Z])([A-Z])/\1_\L\2/g' | tr '[:upper:]' '[:lower:]'
}

# Find all Go files and rename them.
find . -type f -name '*.go' | while read -r file; do
    dir=$(dirname "$file")           # Get directory path
    base=$(basename "$file")         # Get file name
    snake=$(camel_to_snake "$base")  # Convert to snake_case

    if [[ "$base" != "$snake" ]]; then  # Check if renaming is needed
        mv -v "$dir/$base" "$dir/$snake"  # Rename the file
    fi
done
