#!/bin/bash

# 定义 clang-format 的配置样式
STYLE="{
    BasedOnStyle: Google,
    UseTab: Always,
    IndentWidth: 4,
    TabWidth: 4,
    ColumnLimit: 0,
    AllowShortBlocksOnASingleLine: false,
    Language: Proto
}"

# 查找当前目录及其子目录下所有的 .proto 文件并格式化
find . -name '*.proto' -exec sh -c '
    for proto_file do
        echo "Formatting $proto_file"
        clang-format -i --style='"'$STYLE'"' "$proto_file"
    done
' sh {} +

echo "All .proto files have been formatted."
