package model

import (
	"context"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"survey/config"
	"survey/database"
	"survey/util/base"
	"testing"
)

func TestMain(m *testing.M) {
	xconf.ReadInConfig()
	err := config.Startup()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}
	err = xlog.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	err = xdebug.StdConfig().Build()
	if err != nil {
		panic(err)
	}
	m.Run()
}

func TestGetGroupCount(t *testing.T) {
	Convey("test get group count", t, func() {
		ret, err := GetGroupCount(context.TODO(), "750vn2coe36", []string{"15670"})
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldEqual, 70)
	})
}

func TestGetSurveyStat(t *testing.T) {
	Convey("test get survey stat", t, func() {
		ret, err := GetSurveyStat(context.TODO(), "750vn2coe36", []string{"15670"})
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldEqual, 449)
	})
}

func TestGetRecordsWithDetail(t *testing.T) {
	Convey("test get records", t, func() {
		ret, err := GetRecordsWithDetails(context.TODO(), "v9tbsayv6d3", -1, nil, nil, 0, 40000, "")
		So(err, ShouldEqual, nil)
		// t.Log(ret)
		So(len(ret), ShouldEqual, 40000)
	})
}

func TestUpdateRecord(t *testing.T) {
	Convey("test update record", t, func() {
		err := UpdateRecord(context.TODO(), "9b7mt7dciap", 1, "is_valid", 1)
		So(err, ShouldEqual, nil)
	})
}

func TestDeleteRecord(t *testing.T) {
	Convey("test delete record", t, func() {
		err := DeleteRecord(context.TODO(), "9b7mt7dciap", []int64{1, 2, 3})
		So(err, ShouldEqual, nil)
	})
}

func TestGetRecordCount(t *testing.T) {
	Convey("test get record count", t, func() {
		viewWhereSql := " r.openid != ''"
		viewWhereSql = "" // 测试性能优化，不要条件
		ret, err := GetRecordCount(context.TODO(), "96abo424kcl", -1, nil, nil, viewWhereSql)
		So(err, ShouldEqual, nil)
		So(ret, ShouldEqual, 2)
	})
}

func TestGetSurveyInputMethodListCk(t *testing.T) {
	Convey("test TestGetSurveyInputMethodList", t, func() {
		whereStr := " and option = 'nyCAh18FMu' AND (option != '')"
		hashCode, _ := base.SurveyIdToHashCode(662)
		ret, err := GetSurveyInputMethodListCk(context.TODO(), hashCode, whereStr, 1, 10)
		t.Log("list:", ret)
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldBeGreaterThan, 1)
	})
}
