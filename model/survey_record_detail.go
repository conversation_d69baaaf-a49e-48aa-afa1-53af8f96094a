package model

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gorm.io/gorm"
	"survey/database"
	"survey/types"
	"survey/util/base"
	"time"
)

type SurveyRecordDetail struct {
	ID             int64     `gorm:"primaryKey"`
	UID            string    `gorm:"column:uid;type:varchar(128);not null"`
	RoleID         string    `gorm:"column:roleid;type:varchar(128);not null"`
	SurveyRecordID int64     `gorm:"column:survey_record_id;type:int(11);not null;default:0"`
	Question       string    `gorm:"column:question;type:varbinary(64);not null"`
	Option         string    `gorm:"column:option;type:varbinary(64);default:''"`
	Text           string    `gorm:"column:text;type:varchar(512);default:''"`
	CreatedAt      time.Time // 添加需要的时间字段
	UpdatedAt      time.Time // 添加需要的时间字段
}

func SurveyRecordDetailTable(hashcode string) func(tx *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Table(SurveyRecordDetailTableName(hashcode))
	}
}

func SurveyRecordDetailTableName(hashcode string) string {
	return fmt.Sprintf("%s%s", "survey_record_detail_", hashcode)
}

// GetSurveyRecordDetailsByRecordID retrieves survey record details for a given SurveyRecordID and hash code.
func GetSurveyRecordDetailsByRecordID(ctx context.Context, surveyRecordID int64, hashcode string, conditions map[string]interface{}) ([]SurveyRecordDetail, error) {
	db := database.Get()
	var details []SurveyRecordDetail

	query := db.Scopes(SurveyRecordDetailTable(hashcode)).Where("survey_record_id = ?", surveyRecordID)

	for key, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", key), value)
	}

	err := query.Find(&details).Error
	if err != nil {
		xlog.FromContext(ctx).Error("GetSurveyRecordDetailsByRecordID with error", xlog.Err(err))
		return nil, err
	}
	return details, nil
}

func GetSurveyDetailStatisticData(ctx context.Context, surveyID int64, notInRecordIDS []int64) ([]*types.SurveyDetailStatisticModel, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetFullValidOrDeleteSurveyRecordId base.SurveyIdToHashCode with error", xlog.Err(err))
		return nil, err
	}

	rdbQuery := db.WithContext(ctx).
		Scopes(SurveyRecordDetailTable(hashCode)).
		Select("question", "option", "COUNT(*) AS option_count").
		Where("`option` != ''")

	if len(notInRecordIDS) > 0 {
		rdbQuery.Not(notInRecordIDS)
	}

	var data = make([]*types.SurveyDetailStatisticModel, 0)
	result := rdbQuery.Group("question,`option`").Find(&data)

	if result.Error != nil {
		return nil, fmt.Errorf("GetFullValidOrDeleteSurveyRecordId failed to fetch valid survey record err:%+v survey: %v", result.Error, surveyID)
	}

	return data, nil
}

func GetSurveyDetailRecordGroupCount(ctx context.Context, surveyID int64, notInRecordIDS []int64) (map[string]int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetSurveyDetailRecordGroupCount base.SurveyIdToHashCode with error", xlog.Err(err))
		return nil, err
	}

	rdbQuery := db.WithContext(ctx).
		Scopes(SurveyRecordDetailTable(hashCode)).
		Select("question", "COUNT(DISTINCT survey_record_id) AS group_count").
		Where("`option` != ''")

	if len(notInRecordIDS) > 0 {
		rdbQuery.Not(notInRecordIDS)
	}

	type DataItem struct {
		Question   []byte
		GroupCount int64
	}

	var data = make([]*DataItem, 0)
	result := rdbQuery.Group("question").Find(&data)

	if result.Error != nil && !xgorm.RecordNotFound(result.Error) {
		return nil, fmt.Errorf("GetSurveyDetailRecordGroupCount failed to fetch valid survey record IDs: %v", result.Error)
	}

	var returnMap = make(map[string]int64)
	for _, item := range data {
		returnMap[string(item.Question)] = item.GroupCount
	}

	return returnMap, nil
}
