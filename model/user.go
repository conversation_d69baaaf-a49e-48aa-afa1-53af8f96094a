package model

import (
	"time"

	"survey/database"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

func init() { xgorm.RegisterMigrate(database.Get, new(UserInfo)) }

type UserInfo struct {
	Uid       int64  `gorm:"primaryKey;size:64;autoIncrement:false"`
	Phone     string `gorm:"size:64;uniqueIndex:uni_phone"`
	Email     string `gorm:"size:64;uniqueIndex:uni_email"`
	Password  string `gorm:"size:64"`
	Salt      string `gorm:"size:32"`
	Status    int    `gorm:"size:32"`
	CreatedAt time.Time
}

func GetUserInfoByUid(d *xgorm.DB, uid int64) (*UserInfo, error) {
	u := new(UserInfo)
	result := d.Where("uid = ?", uid).First(u)
	return u, result.Error
}
