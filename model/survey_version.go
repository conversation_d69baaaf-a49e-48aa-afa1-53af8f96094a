package model

import (
	"context"
	"survey/database"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type SurveyVersion struct {
	ID                 int64      `gorm:"primaryKey;autoIncrement;column:id"`
	SurveyID           int64      `gorm:"column:survey_id"`
	VersionID          time.Time  `gorm:"column:version_id;type:datetime;default:CURRENT_TIMESTAMP"`
	ClientID           string     `gorm:"column:clientid;type:varchar(128);not null"`
	Name               string     `gorm:"column:name;type:varchar(128);not null"`
	IsClosed           int        `gorm:"column:is_closed;default:0"`
	IsPause            int        `gorm:"column:is_pause;"`
	IsPublish          int        `gorm:"column:is_publish;type:tinyint(4);default:0"`
	IsModifyUnpublish  bool       `gorm:"column:is_modify_unpublish;type:tinyint(4);default:0"`
	IsOpened           int32      `gorm:"column:is_opened;type:tinyint(4);default:0"`
	StartTime          *time.Time `gorm:"column:stime;type:datetime"`
	EndTime            *time.Time `gorm:"column:etime;type:datetime"`
	Type               int        `gorm:"column:type"`
	Schema             []byte     `gorm:"column:schema;type:mediumblob"`
	PreviewSchema      []byte     `gorm:"column:preview_schema;type:mediumblob"`
	Settings           string     `gorm:"column:settings;type:text"`
	WebSettings        string     `gorm:"column:web_settings;type:text"`
	Languages          string     `gorm:"column:languages;type:text"`
	MultilingualSchema []byte     `gorm:"column:multilingual_schema;type:mediumblob"`
	HashCode           string     `gorm:"column:hash_code;type:varchar(64);default:''"`
	KeyValue           string     `gorm:"column:key_value;type:text;default:''"`
	Font               string     `gorm:"column:font;type:text;default:''"`
	Remark             string     `gorm:"column:remark;type:varchar(255);default:''"`
	IsDelete           int32      `gorm:"column:is_delete;type:tinyint(4);default:0"`
	DeleteTime         *time.Time `gorm:"column:deltime;type:datetime"`
	CreateTime         time.Time  `gorm:"column:ctime;type:datetime;default:CURRENT_TIMESTAMP"`
	ModifyTime         time.Time  `gorm:"column:mtime;type:datetime;autoUpdateTime"`
	Creator            string     `gorm:"column:creator;type:varchar(64);default:''"`
	Editor             string     `gorm:"column:editor;type:varchar(64);default:''"`
}

func (SurveyVersion) TableName() string {
	return "cms_survey_version"
}

func CreateFromCmsSurvey(ctx context.Context, cmsSurvey *CmsSurvey) error {
	db := database.Get()
	gLog := xlog.FromContext(ctx)

	newSurveyVersion := &SurveyVersion{
		SurveyID:          int64(cmsSurvey.ID),
		ClientID:          cmsSurvey.ClientId,
		Name:              cmsSurvey.Name,
		IsClosed:          int(cmsSurvey.IsClosed),
		IsPause:           int(cmsSurvey.IsPause),
		IsPublish:         int(cmsSurvey.IsPublish),
		IsModifyUnpublish: cmsSurvey.IsModifyUnpublish != 0,
		IsOpened:          cmsSurvey.IsOpened,
		StartTime:         cmsSurvey.Stime,
		EndTime:           cmsSurvey.Etime,
		Type:              int(cmsSurvey.Type),
		Schema:            cmsSurvey.Schema,
		PreviewSchema:     cmsSurvey.PreviewSchema,
		Settings:          cmsSurvey.Settings,
		WebSettings:       cmsSurvey.WebSettings,
		Languages:         cmsSurvey.Languages,
		HashCode:          cmsSurvey.HashCode,
		KeyValue:          cmsSurvey.KeyValue,
		Font:              cmsSurvey.Font,
		Remark:            cmsSurvey.Remark,
		IsDelete:          cmsSurvey.IsDelete,
		DeleteTime:        &cmsSurvey.Deltime,
		Creator:           cmsSurvey.Creator,
		Editor:            cmsSurvey.Editor,
		VersionID:         time.Now().Truncate(time.Second), // Update version_id to the current time, precise to seconds
	}

	err := db.Table("cms_survey_version").Create(newSurveyVersion).Error
	if err != nil {
		gLog.Error("Create with error", xlog.Err(err))
		return err
	}

	return nil
}

// 获取最新版本的问卷配置
func GetLatestSurveyBySurveyId(ctx context.Context, surveyId int64) (*SurveyVersion, error) {
	db := database.Get()
	result := &SurveyVersion{}
	err := db.Table("cms_survey_version").Where("survey_id = ? and is_publish = 1", surveyId).Order("version_id desc").First(result).Error

	if err != nil {
		return nil, err
	}

	return result, err
}
