package model

import (
	"context"
	"testing"
	"time"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestGetRecordFullValidUserInfo(t *testing.T) {
	Convey("test get survey stat", t, func() {
		var st = time.Now()
		count, err := MysqlGetRecordFullValidUserCount(context.TODO(), "750vn2coe36")
		dd := time.Since(st).Milliseconds()
		t.Log(dd)
		So(err, ShouldEqual, nil)
		So(count, ShouldEqual, 310703)
	})
}

func TestGetFullValidOrDeleteSurveyRecordId(t *testing.T) {
	Convey("test get survey stat", t, func() {
		var st = time.Now()
		ret, err := GetFullValidOrDeleteSurveyRecordId(context.TODO(), 668)
		dd := time.Since(st).Milliseconds()
		t.Log(dd)
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldEqual, 3)
	})
}

func TestGetExportSurveyRecordList(t *testing.T) {
	Convey("test GetExportSurveyRecordList", t, func() {
		var st = time.Now()
		viewWhere := `(r.id = 1) AND (timestampdiff(second,begin_time,end_time) > 1) AND (r.ctime = '20240820 10:31:22') AND (r.openid = 202669669) AND (r.role_id = 9013276242) AND (d.question = 'OKyW8jT_yz') AND (d.text LIKE '%abc%')`
		ret, err := GetExportSurveyRecordList(context.TODO(), 1, 2, "", "", 0, 4000, viewWhere)
		dd := time.Since(st).Milliseconds()
		t.Log(dd)
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldEqual, 3)
	})
}

func TestGetSurveyInputMethodCount(t *testing.T) {
	Convey("test TestGetSurveyInputMethodCount", t, func() {
		whereStr := " r.is_valid = 0 AND question = '0zZwwVb-FH' AND d.option = 'nyCAh18FMu' AND (d.option != '')"
		whereStr = "r.is_valid = 0 AND r.is_delete = 0 AND d.question = '0zZwwVb-FH' AND d.`text`!='' OR d.`text` != null "
		ret, err := GetSurveyInputMethodCount(context.TODO(), 662, whereStr)
		t.Log("count:", ret)
		So(err, ShouldEqual, nil)
		So(ret, ShouldBeGreaterThan, 1)
	})
}

func TestGetSurveyInputMethodList(t *testing.T) {
	Convey("test TestGetSurveyInputMethodList", t, func() {
		whereStr := " r.is_valid = 0 AND question = '0zZwwVb-FH' AND d.option = 'nyCAh18FMu' AND (d.option != '')"
		ret, err := GetSurveyInputMethodList(context.TODO(), 662, whereStr, 1, 10)
		t.Log("list:", ret)
		So(err, ShouldEqual, nil)
		So(len(ret), ShouldBeGreaterThan, 1)
	})
}
