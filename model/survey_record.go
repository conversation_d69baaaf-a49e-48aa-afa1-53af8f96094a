package model

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"survey/database"
	"survey/proto"
	"survey/types"
	"survey/util"
	"survey/util/base"
	"survey/util/errors"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SurveyRecord struct {
	ID        int64     `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:'主键ID'" json:"id"`
	UID       string    `gorm:"column:uid;type:varchar(128) NOT NULL;comment:'用户UID'" json:"uid"`
	RoleID    string    `gorm:"column:roleid;type:varchar(128) NOT NULL;comment:'用户roleid'" json:"roleid"`
	OpenID    string    `gorm:"column:openid;type:varchar(64) NOT NULL;comment:'openid'" json:"openid"`
	RoleID2   string    `gorm:"column:role_id;type:varchar(64) NOT NULL;comment:'角色ID'" json:"role_id"`
	DeviceID  string    `gorm:"column:device_id;type:varchar(128) NOT NULL;comment:'设备ID'" json:"device_id"`
	IP        string    `gorm:"column:ip;type:varchar(64) NOT NULL;comment:'ip'" json:"ip"`
	IsValid   int       `gorm:"column:is_valid;type:tinyint(4) NOT NULL;comment:'记录是否有效（0: 有效，1: 无效）'" json:"is_valid"`
	IsDeleted int       `gorm:"column:is_delete;type:tinyint(4) NOT NULL;comment:'记录是否删除（0: 未删除，1: 已删除）'" json:"is_delete"`
	BeginTime time.Time `gorm:"column:begin_time;type:datetime;NOT NULL;default:'';comment:'答题开始时间'" json:"begin_time"`
	EndTime   time.Time `gorm:"column:end_time;type:datetime;NOT NULL;default:'';comment:'答题结束时间'" json:"end_time"`
	Extra     string    `gorm:"column:extra;type:text NOT NULL;comment:'额外信息'" json:"extra"`
	Ctime     time.Time `gorm:"column:ctime;type:datetime;NOT NULL;default:CURRENT_TIMESTAMP(3);comment:'创建时间'" json:"ctime"`
	Second    string    `gorm:"-" json:"second"`
}

func SurveyRecordTable(hashcode string) func(tx *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Table(SurveyRecordTableName(hashcode))
	}
}

func SurveyRecordTableName(hashcode string) string {
	return fmt.Sprintf("%s%s", "survey_record_", hashcode)
}

func GetFullValidSurveyRecordIdCount(ctx context.Context, surveyID int64) (int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetFullValidSurveyRecordIdCount with error", xlog.Err(err))
		return 0, err
	}
	var ids int64
	result := db.WithContext(ctx).
		Table(fmt.Sprintf("survey_record_%s", hashCode)).
		Where("is_valid = ? AND is_delete = ?", 0, 0).
		Count(&ids)

	if result.Error != nil {
		return 0, fmt.Errorf("failed to fetch valid survey record IDs: %v", result.Error)
	}
	return ids, nil
}

func SurveyRecordCountData(hashCode string,
	XFilterId int64, XFilterOpenid string, XFilterRoleId string, XFilterIp string, XFilterDeviceId string,
	XStartTime string, XEndTime string, isValid int32) (int64, error) {
	var totalCount int64

	db := database.Get()
	db = db.Scopes(SurveyRecordTable(hashCode)).
		Select([]string{"id", "begin_time", "end_time", "openid", "role_id", "ip", "device_id", "TIMESTAMPDIFF(SECOND, begin_time, end_time) as second"}).
		Where("is_delete = ?", 0)

	if isValid < 2 {
		db = db.Where("is_valid = ?", isValid)
	}

	if XFilterId > 0 {
		db = db.Where("id = ?", XFilterId)
	}
	if XFilterOpenid != "" {
		db = db.Where("openid = ?", XFilterOpenid)
	}
	if XFilterRoleId != "" {
		db = db.Where("role_id = ?", XFilterRoleId)
	}
	if XFilterIp != "" {
		db = db.Where("ip = ?", XFilterIp)
	}
	if XFilterDeviceId != "" {
		db = db.Where("device_id = ?", XFilterDeviceId)
	}
	if XStartTime != "" {
		db = db.Where("end_time >= ?", XStartTime)
	}
	if XEndTime != "" {
		db = db.Where("end_time <= ?", XEndTime)
	}

	countDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		xlog.Error("countDB.Count with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return totalCount, nil
}

// 获取答卷记录列表数据
// todo 优化过滤参数，提取过滤参数到 params 或者上层预处理
// 减少 db 实例，使用链式调用或者引入 Session
func SurveyRecordListData(ctx context.Context, hashCode string, page int32, page_size int32,
	XFilterId int64, XFilterOpenid string, XFilterRoleId string, XFilterIp string, XFilterDeviceId string,
	XSortId string, XSortEndTime string, XSortSecond string, isValid int32) ([]*SurveyRecord, int64, error) {
	if page == 0 {
		page = 1
	}
	result := []*SurveyRecord{}
	var totalCount int64

	db := database.Get()
	db = db.Scopes(SurveyRecordTable(hashCode)).
		Select([]string{"id", "begin_time", "end_time", "openid", "role_id", "ip", "device_id", "TIMESTAMPDIFF(SECOND, begin_time, end_time) as second", "ctime"}).
		Where("is_delete = ?", 0).
		Where("is_valid = ?", isValid)

	if XFilterId > 0 {
		db = db.Where("id = ?", XFilterId)
	}
	if XFilterOpenid != "" {
		db = db.Where("openid = ?", XFilterOpenid)
	}
	if XFilterRoleId != "" {
		db = db.Where("role_id = ?", XFilterRoleId)
	}
	if XFilterIp != "" {
		db = db.Where("ip = ?", XFilterIp)
	}
	if XFilterDeviceId != "" {
		db = db.Where("device_id = ?", XFilterDeviceId)
	}
	if XSortId != "" {
		idOrder := fmt.Sprintf("%s%s", " id ", XSortId)
		db = db.Order(idOrder)
	}
	if XSortEndTime != "" {
		endTimeOrder := fmt.Sprintf("%s%s", " end_time ", XSortEndTime)
		db = db.Order(endTimeOrder)
	}
	if XSortSecond != "" {
		endSecondOrder := fmt.Sprintf("%s%s", " second ", XSortSecond)
		db = db.Order(endSecondOrder)
	}

	countDB := db
	itemsDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		xlog.Error("countDB.Count with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if page_size == 0 {
		page_size = 10
	}
	pageOffset := int((page - 1) * page_size)
	itemsDB = itemsDB.Offset(pageOffset).Limit(int(page_size)).Order("id desc")
	if err := itemsDB.Find(&result).Error; err != nil {
		xlog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, totalCount, nil
}

// 删除答卷记录
func DelSurveyRecordData(ctx context.Context, delList []int64, hashCode string) error {
	tableName := fmt.Sprintf("%s%s", "survey_record_", hashCode)
	db := database.Get()
	err := db.Table(tableName).Where("is_delete = ?", 0).Where("id in ?", delList).Update("is_delete", 1).Error
	return err
}

// 标记答卷有效
func SetValidSurveyRecordData(ctx context.Context, id int64, hashCode string) error {
	tableName := fmt.Sprintf("%s%s", "survey_record_", hashCode)
	db := database.Get()
	err := db.Table(tableName).Where("is_delete = ?", 0).Where("id = ?", id).Update("is_valid", 0).Error
	return err
}

// 标记答卷无效
func SetInvalidSurveyRecordData(ctx context.Context, ids []int64, hashCode string) error {
	tableName := fmt.Sprintf("%s%s", "survey_record_", hashCode)
	db := database.Get()
	err := db.Table(tableName).Where("is_delete = ?", 0).Where("id in ?", ids).Update("is_valid", 1).Error
	return err
}

// 无效答卷列表
func InValidSurveyRecordListData(ctx context.Context, hashCode string, page int32, page_size int32,
	XFilterId int64, XFilterOpenid string, XFilterRoleId string, XFilterIp string, XFilterDeviceId string,
	XSortId string, XSortEndTime string, XSortSecond string) ([]*SurveyRecord, int64, error) {
	if page == 0 {
		page = 1
	}
	result := []*SurveyRecord{}
	var totalCount int64

	tableName := fmt.Sprintf("%s%s", "survey_record_", hashCode)
	db := database.Get()
	db = db.Table(tableName).Select([]string{"id", "begin_time", "end_time", "openid", "role_id", "ip", "device_id", "TIMESTAMPDIFF(SECOND, begin_time, end_time) as second"}).Where("is_delete = ?", 0).Where("is_valid = ?", 0)

	if XFilterId > 0 {
		db = db.Where("id = ?", XFilterId)
	}
	if XFilterOpenid != "" {
		db = db.Where("openid = ?", XFilterOpenid)
	}
	if XFilterRoleId != "" {
		db = db.Where("role_id = ?", XFilterRoleId)
	}
	if XFilterIp != "" {
		db = db.Where("ip = ?", XFilterIp)
	}
	if XFilterDeviceId != "" {
		db = db.Where("device_id = ?", XFilterDeviceId)
	}
	if XSortId != "" {
		idOrder := fmt.Sprintf("%s%s", " id ", XSortId)
		db = db.Order(idOrder)
	}
	if XSortEndTime != "" {
		endTimeOrder := fmt.Sprintf("%s%s", " end_time ", XSortEndTime)
		db = db.Order(endTimeOrder)
	}
	if XSortSecond != "" {
		endSecondOrder := fmt.Sprintf("%s%s", " second ", XSortSecond)
		db = db.Order(endSecondOrder)
	}

	countDB := db
	itemsDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		xlog.Error("countDB.Count with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if page_size == 0 {
		page_size = 10
	}
	pageOffset := int((page - 1) * page_size)
	itemsDB = itemsDB.Offset(pageOffset).Limit(int(page_size)).Order("id desc")
	if err := itemsDB.Find(&result).Error; err != nil {
		xlog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return result, totalCount, nil
}

func GetSurveyRecordList(ctx context.Context, page int, page_size int, hashCode string) ([]SurveyRecord, error) {
	var records []SurveyRecord
	offset := (page - 1) * page_size

	gLog := xlog.FromContext(ctx)

	db := database.Get()

	err := db.Scopes(SurveyRecordTable(hashCode)).
		Order("ctime desc").
		Limit(page_size).Offset(offset).
		Find(&records).Error

	if err != nil {
		gLog.Error("GetSurveyRecordList with error", zap.Error(err))
		return nil, err
	}
	return records, nil
}

func GetSurveyRecordListWithoutPages(ctx context.Context, hashCode string) ([]SurveyRecord, error) {
	var records []SurveyRecord

	gLog := xlog.FromContext(ctx)

	db := database.Get()

	err := db.Scopes(SurveyRecordTable(hashCode)).
		Order("ctime desc").
		Find(&records).Error

	if err != nil {
		gLog.Error("GetSurveyRecordList with error", zap.Error(err))
		return nil, err
	}
	return records, nil
}

func GetFullValidOrDeleteSurveyRecordId(ctx context.Context, surveyID int64) ([]int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	var ids []int64
	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetFullValidOrDeleteSurveyRecordId base.SurveyIdToHashCode with error", xlog.Err(err))
		return ids, err
	}

	result := db.WithContext(ctx).
		Table(fmt.Sprintf("survey_record_%s", hashCode)).
		Where("is_valid = ? OR is_delete = ?", 1, 1).
		Pluck("id", &ids)

	if result.Error != nil {
		return ids, fmt.Errorf("GetFullValidOrDeleteSurveyRecordId failed to fetch valid survey record IDs: %v", result.Error)
	}

	return ids, nil
}

func MysqlGetRecordFullValidUserCount(ctx context.Context, survey string) (uint64, error) {
	db := database.Get()
	var count int64
	err := db.WithContext(ctx).
		Table(fmt.Sprintf("survey_record_%s", survey)).
		Select("count(DISTINCT concat(openid, roleid, device_id, ip))").
		Where("is_valid = ? OR is_delete = ?", 0, 0).
		Count(&count).Error

	if err != nil {
		return 0, err
	}
	return uint64(count), nil
}

func GetSurveyExportCount(ctx context.Context, surveyID int64, isValid int32, startTime, endTime string) (int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	var count int64

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetSurveyExportCount base.SurveyIdToHashCode with error", xlog.Err(err))
		return count, err
	}

	var queryObj = db.WithContext(ctx).
		Table(fmt.Sprintf("survey_record_%s", hashCode)).
		Where("is_delete = ?", 0)

	if isValid == 0 || isValid == 1 {
		queryObj.Where("is_valid=?", isValid)
	}

	if startTime != "" {
		queryObj.Where("begin_time >= ?", startTime)
	}

	if endTime != "" {
		queryObj.Where("end_time <= ?", endTime)
	}

	result := queryObj.Count(&count)

	if result.Error != nil {
		return count, fmt.Errorf("GetSurveyExportCount failed to fetch count err: %v", result.Error)
	}

	return count, nil
}

func GetExportSurveyRecordList(ctx context.Context, surveyID int64, isValid int32, startTime, endTime string, limitStart, limitSize int, viewWhere string) ([]*types.ExportSurveyRecordAndDetailItem, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetSurveyExportCount base.SurveyIdToHashCode with error", xlog.Err(err))
		return nil, err
	}

	//var sqlStr = "select r.id,r.uid,r.roleid,r.openid,r.role_id,r.device_id,r.ip,r.is_valid,r.begin_time,end_time,r.extra,r.ctime,d.record_question,d.record_option,d.record_text from"
	//sqlStr += "(SELECT survey_record_id,GROUP_CONCAT(question SEPARATOR ',|') AS record_question,GROUP_CONCAT(`option` SEPARATOR ',|') AS record_option,GROUP_CONCAT(`text` SEPARATOR ',|') AS record_text from `%s` GROUP BY survey_record_id) d "
	//sqlStr += " LEFT JOIN `%s` r ON r.id = d.survey_record_id where r.is_delete = 0 "

	var sqlStr = "select r.id,r.uid,r.roleid,r.openid,r.role_id,r.device_id,r.ip,r.is_valid,r.begin_time,end_time,r.extra,r.ctime, GROUP_CONCAT( question SEPARATOR ',|' ) AS record_question,GROUP_CONCAT( `option` SEPARATOR ',|' ) AS record_option, GROUP_CONCAT( `text` SEPARATOR ',|' ) AS record_text from"
	sqlStr += " %s d LEFT JOIN %s r ON r.id = d.survey_record_id where r.is_delete = 0 "

	var queryObj = db.WithContext(ctx)

	sqlStr = fmt.Sprintf(sqlStr, SurveyRecordDetailTableName(hashCode), SurveyRecordTableName(hashCode))
	if isValid == 0 || isValid == 1 {
		sqlStr += fmt.Sprintf(" and r.is_valid = %d", isValid)
	}

	if startTime != "" {
		sqlStr += fmt.Sprintf(" and r.ctime >= '%s'", startTime)
	}

	if endTime != "" {
		sqlStr += fmt.Sprintf(" and r.ctime <= '%s'", endTime)
	}

	if len(viewWhere) > 0 {
		sqlStr += " and " + viewWhere
	}

	sqlStr += " GROUP BY survey_record_id "
	sqlStr += fmt.Sprintf(" limit %d,%d", limitStart, limitSize)

	//gLog.Info("sql:-=-=-=-=-=-=- ", xlog.String("111 ", sqlStr))

	var list = make([]*types.ExportSurveyRecordAndDetailItem, 0)
	result := queryObj.Raw(sqlStr).Find(&list)
	if result.Error != nil {
		return list, fmt.Errorf("GetExportSurveyRecordList failed to fetch list err: %v", result.Error)
	}

	// 时间特殊处理
	for _, item := range list {
		item.BeginTime, _ = util.RFC3339ToDatetime(item.BeginTime)
		item.EndTime, _ = util.RFC3339ToDatetime(item.EndTime)
	}

	return list, nil
}

func GetExportSurveyRecordListCount(ctx context.Context, surveyID int64, isValid int32, startTime, endTime string, viewWhere string) (int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		gLog.Error("GetExportSurveyRecordListCount base.SurveyIdToHashCode with error", xlog.Err(err))
		return 0, err
	}

	var sqlStr = "select count(1) from"
	//sqlStr += " %s d LEFT JOIN %s r ON r.id = d.survey_record_id where r.is_delete = 0 "
	sqlStr += " %s r where r.is_delete = 0 "

	var queryObj = db.WithContext(ctx)

	sqlStr = fmt.Sprintf(sqlStr, SurveyRecordTableName(hashCode))
	if isValid == 0 || isValid == 1 {
		sqlStr += fmt.Sprintf(" and r.is_valid = %d", isValid)
	}

	if startTime != "" {
		sqlStr += fmt.Sprintf(" and r.end_time >= '%s'", startTime)
	}

	if endTime != "" {
		sqlStr += fmt.Sprintf(" and r.end_time <= '%s'", endTime)
	}

	var count int64
	result := queryObj.Raw(sqlStr).Scan(&count)
	if result.Error != nil {
		return 0, fmt.Errorf("GetExportSurveyRecordListCount failed to fetch list err: %v", result.Error)
	}

	return count, nil
}

func GetSurveyInputMethodCount(ctx context.Context, surveyID int64, whereStr string) (int64, error) {
	var count int64
	baseQuery, err := getSurveyInputMethodBaseQuery(ctx, surveyID, whereStr)
	if err != nil {
		return count, err
	}

	result := baseQuery.Count(&count)

	if result.Error != nil && !xgorm.RecordNotFound(result.Error) {
		return count, fmt.Errorf("GetSurveyInputMethodCount failed to fetch list err: %v", result.Error)
	}

	return count, nil
}

func GetSurveyInputMethodList(ctx context.Context, surveyID int64, whereStr string, page, pageSize int32) ([]*proto.InputMethodListResponseData, error) {
	var list = make([]*proto.InputMethodListResponseData, 0)

	baseQuery, err := getSurveyInputMethodBaseQuery(ctx, surveyID, whereStr)
	if err != nil {
		return list, err
	}

	var limitStart = (page - 1) * pageSize
	result := baseQuery.Limit(int(pageSize)).Offset(int(limitStart)).Find(&list)
	if result.Error != nil && !xgorm.RecordNotFound(result.Error) {
		return list, fmt.Errorf("GetSurveyInputMethodList failed to fetch list err: %v", result.Error)
	}

	return list, nil
}

func getSurveyInputMethodBaseQuery(ctx context.Context, surveyID int64, whereStr string) (*gorm.DB, error) {
	var db = database.Get()

	hashCode, err := base.SurveyIdToHashCode(surveyID)
	if err != nil {
		return nil, err
	}

	var baseQuery = db.WithContext(ctx).Select(
		"d.id as id",
		"d.survey_record_id as survey_record_id",
		"d.question as question",
		"d.`option` as `option`",
		"d.`text` as `text`",
		"r.openid as openid",
		"r.role_id as role_id",
		"r.ip as ip",
		"r.device_id as device_id",
	).
		Table(SurveyRecordDetailTableName(hashCode) + " as d").
		Joins("left join " + SurveyRecordTableName(hashCode) + " r on r.id = d.survey_record_id").
		Where(whereStr)

	return baseQuery, nil
}
