package model

import (
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"survey/constants"
	"survey/proto"
	"testing"
)

func TestCreateSurveyView(t *testing.T) {
	Convey("test TestCreateSurveyView", t, func() {
		content := `{
  "operator": "&&",
  "conditions": [
    {
      "operator": "==",
      "lhs": "d.question",
      "rhs": "'OKyW8jT_yz'"
    },
    {
      "operator": "wildcard",
      "lhs": "d.text",
      "rhs": "'*abc*'"
    }
  ]
}`
		id, err := CreateSurveyView(1, "筛选器", content, 1, "蒋鹏")
		t.Log("insertID:", id)
		So(err, ShouldEqual, nil)
	})
}

func TestGetSurveyViewList(t *testing.T) {
	Convey("test GetSurveyViewList", t, func() {
		req := &proto.SurveyViewListReq{
			SurveyId: 1,
			Kind:     constants.Survey_View_Kind_List,
		}
		list, err := GetSurveyViewList(req, constants.Survey_View_Not_Delete)
		t.Log("list:", list)
		So(err, ShouldEqual, nil)
		So(list, ShouldNotBeEmpty)
	})
}

func TestGetSurveyViewInfoBySurveyIdAndName(t *testing.T) {
	Convey("test GetSurveyViewInfoBySurveyIdAndName", t, func() {
		data, err := GetSurveyViewInfoBySurveyIdAndName(1, "筛选器", 0)
		t.Log("data:", data)
		So(err, ShouldEqual, nil)
	})
}

func TestUpdateSurveyView(t *testing.T) {
	Convey("TestUpdateSurveyView", t, func() {

		content := `{
  "operator": "&&",
  "conditions": [
    {
      "operator": "==",
      "lhs": "d.question",
      "rhs": "'OKyW8jT_yz_111'"
    },
    {
      "operator": "wildcard",
      "lhs": "d.text",
      "rhs": "'*abc*'"
    }
  ]
}`

		data, err := UpdateSurveyView(1, "筛选器_编辑", content, "jp")
		t.Log("data:", data)
		So(err, ShouldEqual, nil)
	})
}
