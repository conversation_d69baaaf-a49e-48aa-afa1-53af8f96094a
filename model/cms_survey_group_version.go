package model

import (
	"survey/database"
)

const TableNameCmsSurveyGroupVersion = "cms_survey_group_version"

// CmsSurveyGroupVersion mapped from table <cms_survey_group_version>
type CmsSurveyGroupVersion struct {
	ID        uint64 `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	GroupID   int64  `gorm:"column:group_id;type:int;not null;index:group_id,priority:1;comment:group_id" json:"group_id"`      // group_id
	ClientId  string `gorm:"column:clientid;type:varchar(128);not null;comment:clientid" json:"clientid"`                       // clientid
	Name      string `gorm:"column:name;type:varchar(128);not null;comment:问卷组名称" json:"name"`                                  // 问卷组名称
	IsPublish int32  `gorm:"column:is_publish;type:tinyint;not null;comment:是否发布（0: 未发布，1: 已发布）" json:"is_publish"`             // 是否发布（0: 未发布，1: 已发布）
	LimitType int32  `gorm:"column:limit_type;type:tinyint;not null;comment:1:openid 限制一次 2:roleid限制一次，3无限制" json:"limit_type"` // 1:openid 限制一次 2:roleid限制一次，3无限制
	Type      int32  `gorm:"column:type;type:int;comment:问卷组类型 1:ip 2:语言" json:"type"`                                          // 问卷组类型 1:ip 2:语言
	Settings  string `gorm:"column:settings;type:text;comment:问卷组设置" json:"settings"`                                           // 问卷组设置
	HashCode  string `gorm:"column:hash_code;type:varchar(64);index:hash_code,priority:1;comment:问卷组id-hash" json:"hash_code"`  // 问卷组id-hash
	IsDelete  int32  `gorm:"column:is_delete;type:tinyint;not null;comment:是否删除（0: 未删除，1: 已删除）" json:"is_delete"`               // 是否删除（0: 未删除，1: 已删除）
	Ctime     string `gorm:"column:ctime;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"ctime"`           // 创建时间
	Mtime     string `gorm:"column:mtime;type:datetime;comment:最近修改时间" json:"mtime"`                                            // 最近修改时间
	Creator   string `gorm:"column:creator;type:varchar(64);comment:创建人" json:"creator"`                                        // 创建人
	Editor    string `gorm:"column:editor;type:varchar(64);comment:最近修改人" json:"editor"`                                        // 最近修改人
}

// TableName CmsSurveyGroupVersion's table name
func (*CmsSurveyGroupVersion) TableName() string {
	return TableNameCmsSurveyGroupVersion
}

func CreateCmsSurveyGroupVersion(data CmsSurveyGroupVersion) (uint64, error) {
	db := database.Get()
	res := db.Table(TableNameCmsSurveyGroupVersion).Create(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return data.ID, nil
}
