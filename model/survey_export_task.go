package model

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"strings"
	"survey/config"
	"survey/database"
	"survey/util/errors"
	"time"
)

// 测试
//const TableNameSurveyRecordExportLog = "survey_record_export_queue_test"

type SurveyRecordExportLog struct {
	Id           int64     `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:'主键ID'" json:"id"`
	ClientId     string    `gorm:"column:clientid;type:varchar(128) NOT NULL;comment:'租户ID'" json:"client_id"`
	SurveyId     int64     `gorm:"column:survey_id;type:int(11) NOT NULL;comment:'问卷id'" json:"survey_id"`
	Name         string    `gorm:"column:name;type:varchar(125) NOT NULL;comment:'文件名称'" json:"name"`
	FileType     int32     `gorm:"column:file_type;type:tinyint(4) NOT NULL;comment:'文件类型 0: CSV 1: EXCEL'" json:"file_type"`
	DataType     int32     `gorm:"column:data_type;type:tinyint(4) NOT NULL;comment:'数据类型 0: 答案编码 1: 答案文本'" json:"data_type"`
	IsValid      int32     `gorm:"column:is_valid;type:tinyint(4) NOT NULL;comment:'数据有效性（0: 有效 1: 无效 2:全部）'" json:"is_valid"`
	Url          string    `gorm:"column:url;type:varchar(255) NOT NULL;comment:'url'" json:"url"`
	Status       int32     `gorm:"column:status;type:tinyint(4) NOT NULL;comment:'0: 等待，未处理 1: 处理中 2: 完成 3: 失败'" json:"status"`
	StartTime    time.Time `gorm:"column:start_time;type:datetime;NOT NULL;default:'';comment:'完成时间范围 起始时间'" json:"start_time"`
	EndTime      time.Time `gorm:"column:end_time;type:datetime;NOT NULL;default:'';comment:'完成时间范围 结束时间'" json:"end_time"`
	ViewContent  string    `gorm:"column:view_content;type:string;NOT NULL;comment:'筛选内容'" json:"view_content"`
	DataSource   int32     `gorm:"column:data_source;type:tinyint(4);NOT NULL;comment:'数据源，0：全部，1：随机'" json:"data_source"`
	DataNum      int32     `gorm:"column:data_num;type:tinyint(4);NOT NULL;comment:'随机数量'" json:"data_num"`
	Extra        string    `gorm:"column:extra;type:json;NOT NULL;comment:'拓展信息'" json:"extra"`
	CreateTime   time.Time `gorm:"column:create_time;type:datetime;NOT NULL;default:CURRENT_TIMESTAMP(3);comment:'创建时间'" json:"create_time"`
	UpdateTime   time.Time `gorm:"column:update_time;type:datetime;NOT NULL;default:CURRENT_TIMESTAMP(3);comment:'最近修改时间'" json:"update_time"`
	CompleteTime time.Time `gorm:"column:complete_time;type:datetime;NOT NULL;default:'';comment:'完成时间'" json:"complete_time"`
}

// 获取导出任务列表数据
func SurveyExportTaskListData(ctx context.Context, clientId string, surveyId int64, page int32, pageSize int32) ([]*SurveyRecordExportLog, int64, error) {
	logger := xlog.FromContext(ctx)
	db := database.Get()
	if page == 0 {
		page = 1
	}
	var result []*SurveyRecordExportLog
	var totalCount int64

	db = db.Table(GetSurveyRecordExportLogTableName()).Where("clientid = ?", clientId).Where("survey_id=?", surveyId)
	countDB := db
	itemsDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		logger.Error("countDB.Count with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	if pageSize == 0 {
		pageSize = 10
	}
	pageOffset := int((page - 1) * pageSize)
	itemsDB = itemsDB.Offset(pageOffset).Limit(int(pageSize)).Order("id desc")
	if err := itemsDB.Find(&result).Error; err != nil {
		logger.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	return result, totalCount, nil
}

// 创建导出任务
func CreateSurveyExportTaskData(
	clientId string,
	surveyId int64,
	name string,
	fileType int32,
	dataType int32,
	isValid int32,
	startTime string,
	endTime string,
	viewContent string,
	dataSource int32,
	dataNum int32,
	extra string,
) error {
	db := database.Get()
	data := SurveyRecordExportLog{}
	data.ClientId = clientId
	data.SurveyId = surveyId
	data.Name = name
	data.FileType = fileType
	data.DataType = dataType
	data.IsValid = isValid
	data.ViewContent = viewContent
	data.DataSource = dataSource
	data.DataNum = dataNum
	data.Extra = extra
	var err error
	if startTime != "" {
		data.StartTime, err = time.ParseInLocation(time.DateTime, startTime, time.Local)
		if err != nil {
			return err
		}
	}
	if endTime != "" {
		data.EndTime, err = time.ParseInLocation(time.DateTime, endTime, time.Local)
		if err != nil {
			return err
		}
	}
	err = db.Table(GetSurveyRecordExportLogTableName()).Create(&data).Error
	return err
}

// 删除导出任务
func DelSurveyExportTaskData(ctx context.Context, clientId string, surveyId int64, ids []int64) error {
	db := database.Get()
	data := make(map[string]interface{})
	data["clientid"] = clientId
	data["survey_id"] = surveyId

	err := db.Table(GetSurveyRecordExportLogTableName()).Where("clientid = ?", clientId).Where("survey_id = ?", surveyId).Where("id in ?", ids).Delete(data).Error
	return err
}

// 重置导出任务状态
func ResetSurveyExportTaskStatusData(ctx context.Context, clientId string, surveyId int64, id int64) error {
	db := database.Get()
	err := db.Table(GetSurveyRecordExportLogTableName()).Where("id = ?", id).Where("clientid = ?", clientId).Where("survey_id = ?", surveyId).Update("status", 0).Error
	return err
}

func GetExportTaskLogListByStatus(status int64, page, size int) []*SurveyRecordExportLog {
	db := database.Get()

	var data = make([]*SurveyRecordExportLog, 0)
	if page < 0 {
		page = 1
	}

	var start = (page - 1) * size
	db.Table(GetSurveyRecordExportLogTableName()).Where("status=?", status).Order("id asc").Offset(start).Limit(size).Find(&data)
	return data
}

func GetOneExportTaskLogByStatus(clientId string, status int64) *SurveyRecordExportLog {
	db := database.Get()
	var data = new(SurveyRecordExportLog)
	db.Table(GetSurveyRecordExportLogTableName()).Where("clientid = ?", clientId).Where("status=?", status).First(&data)
	return data
}

func GetOneWaitingHandleSurveyExportTaskLog() *SurveyRecordExportLog {
	db := database.Get()
	var data = new(SurveyRecordExportLog)
	db.Table(GetSurveyRecordExportLogTableName()).Where("status=?", 0).Order("id ASC").First(&data) //TODO: for update 后面用wida数据源，这里就不写了
	return data
}

func UpdatesSurveyExportLogByID(ctx context.Context, id int64, changes map[string]interface{}) error {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	if _, ok := changes["id"]; ok {
		delete(changes, "id")
	}

	for field, value := range changes {
		if v, ok := value.(time.Time); ok && v.IsZero() {
			delete(changes, field)
		}
	}

	result := db.Table(GetSurveyRecordExportLogTableName()).Where("id = ?", id).Updates(changes)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		gLog.Warn("no record updated", zap.Int64("survey_id", id))
		return nil
	}
	return nil
}

func GetSurveyRecordExportLogTableName() string {
	var tableName = strings.TrimSpace(config.Get().SurveyRecordExportQueue)
	if tableName == "" {
		tableName = "survey_record_export_queue"
	}
	return tableName
}
