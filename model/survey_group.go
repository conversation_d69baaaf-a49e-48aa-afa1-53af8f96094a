package model

import (
	"context"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"gorm.io/gorm/clause"
	"survey/constants"
	"survey/database"
	"survey/proto"
	"survey/util/base"
	"survey/util/errors"
)

const TableNameSurveyGroup = "cms_survey_group"

// 创建问卷组
func CreateSurveyGroup(clientId int64, name string, limitType, typeVal int32, settings string, creator string) (uint64, error) {
	db := database.Get()
	data := proto.CmsSurveyGroupInfo{
		Clientid:  xcast.ToString(clientId),
		Name:      name,
		LimitType: limitType,
		Type:      typeVal,
		Settings:  settings,
		Ctime:     base.GetNowTime(),
		Mtime:     base.GetNowTime(),
		Creator:   creator,
		Editor:    creator,
	}
	res := db.Table(TableNameSurveyGroup).Create(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return data.Id, nil
}

func GetSurveyGroupDetail(clientId int64, id int64) (*proto.CmsSurveyGroupInfo, error) {
	db := database.Get()
	var data = proto.CmsSurveyGroupInfo{}
	res := db.Table(TableNameSurveyGroup).Where("clientid =? and id = ?", clientId, id).First(&data)
	if res.Error != nil {
		return nil, res.Error
	}
	return &data, nil
}

// 获取问卷组- 根据clientId 和 name (未删除的)
func GetSurveyGroupByClientIdAndName(clientId int64, name string, exceptID int64) (*proto.CmsSurveyGroupInfo, error) {
	db := database.Get()
	var data = proto.CmsSurveyGroupInfo{}

	baseQuery := db.Table(TableNameSurveyGroup).Where("clientid = ? and name = ? and is_delete = ?", clientId, name, constants.Not_Delete)

	if exceptID > 0 {
		baseQuery = baseQuery.Where("id != ?", exceptID)
	}

	res := baseQuery.First(&data)

	if res.Error != nil {
		return nil, res.Error
	}
	return &data, nil
}

func UpdateSurveyGroup(clientID, id int64, name string, limitType, typeVal int32, settings string, editor string) (int64, error) {
	db := database.Get()
	data := map[string]interface{}{
		"name":       name,
		"limit_type": limitType,
		"type":       typeVal,
		"settings":   settings,
		"is_publish": 0,
		"mtime":      base.GetNowTime(),
		"editor":     editor,
	}
	res := db.Table(TableNameSurveyGroup).Where("clientid = ? and id = ?", clientID, id).Updates(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

func GetSurveyGroupList(request *proto.SurveyGroupListReq, isDelete int32) ([]*proto.CmsSurveyGroupInfo, error) {
	db := database.Get()
	var data []*proto.CmsSurveyGroupInfo

	queryObj := db.Table(TableNameSurveyGroup).Where("clientid = ? and is_delete = ?", request.ClientId, isDelete)

	if request.Id > 0 {
		queryObj.Where("id = ?", request.Id)
	}

	res := queryObj.Find(&data)
	if res.Error != nil {
		return nil, res.Error
	}
	return data, nil
}

func SurveyGroupSubUpdate(clientId int64, id int64, updateMap map[string]interface{}) (int64, error) {
	db := database.Get()
	res := db.Table(TableNameSurveyGroup).Where("clientid =? and id = ?", clientId, id).Updates(&updateMap)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

func SurveyGroupCreateOrUpdate(ctx context.Context, surveyGroup *proto.CmsSurveyGroupInfo) (uint64, error) {
	db := database.Get()

	err := db.Table(TableNameSurveyGroup).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		UpdateAll: true,
	}).Create(surveyGroup).Error

	if err != nil {
		return 0, errors.Wrap(errors.ErrDBCreate, err)
	}
	return surveyGroup.Id, nil
}
