package model

import (
	"survey/database"
	"time"
)

const TableNameSurveyGroupRecord = "survey_group_record"

// SurveyGroupRecord mapped from table <survey_group_record>
type SurveyGroupRecord struct {
	ID       int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Openid   string    `gorm:"column:openid;type:varchar(64);not null;comment:openid" json:"openid"`                                     // openid
	RoleID   string    `gorm:"column:role_id;type:varchar(64);not null;index:idx_role_id,priority:1;comment:角色ID" json:"role_id"`        // 角色ID
	DeviceID string    `gorm:"column:device_id;type:varchar(128);not null;index:idx_device_id,priority:1;comment:设备ID" json:"device_id"` // 设备ID
	IP       string    `gorm:"column:ip;type:varchar(64);not null;index:idx_ip,priority:1;comment:ip" json:"ip"`                         // ip
	Ctime    time.Time `gorm:"column:ctime;type:datetime;not null;index:idx_ctime,priority:1;comment:创建时间" json:"ctime"`                 // 创建时间
}

// TableName SurveyGroupRecord's table name
func (*SurveyGroupRecord) TableName() string {
	return TableNameSurveyGroupRecord
}

func CreateSurveyGroupRecordTable(hashCode string) error {
	db := database.Get()

	// 检测表存在性
	recordName := "survey_group_record_" + hashCode
	if db.Migrator().HasTable(recordName) {
		return nil
	}

	// 创建表, 并重命名为对应表名
	err := db.Set("gorm:table_options", "ENGINE=InnoDB").Migrator().CreateTable(&SurveyGroupRecord{})
	if err != nil {
		return err
	}
	err = db.Migrator().RenameTable(TableNameSurveyGroupRecord, recordName)
	return err
}
