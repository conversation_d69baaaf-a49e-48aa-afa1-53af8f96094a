package model

import (
	"context"
	"fmt"
	"survey/constants"
	"survey/database"
	"survey/proto"
	"survey/util"
	"survey/util/base"
	"survey/util/errors"
	"survey/util/userpkg"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CmsSurvey struct {
	ID                int64      `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:'主键ID'" json:"id"`
	ClientId          string     `gorm:"column:clientid;type:varchar(128) NOT NULL;comment:'租户ID'" json:"client_id"`
	Name              string     `gorm:"column:name;type:varchar(128);NOT NULL;default:'';comment:'问卷名称'" json:"name"`
	IsClosed          int32      `gorm:"column:is_closed;type:int(8) unsigned;NOT NULL;default:0;comment:'问卷是否打开：（0: 打开，1: 关闭）'" json:"isClosed"`
	IsPause           int32      `gorm:"column:is_pause;type:int(8) unsigned;NOT NULL;default:1;comment:'问卷是否暂停：（0: 不暂停，1: 暂停）'" json:"isPause"` //提示：is_pause 是0时候，insert会获取tag:default的默认值1
	IsPublish         int32      `gorm:"column:is_publish;type:tinyint(4);NOT NULL;default:0;comment:'场景id'" json:"isPublish"`
	IsModifyUnpublish int32      `gorm:"column:is_modify_unpublish;type:tinyint(4) unsigned;NOT NULL;default:0;comment:'触发类型(1上线立即触发 2系统定时触发)'" json:"isModifyUnpublish"`
	IsOpened          int32      `gorm:"column:is_opened;type:tinyint(4);NOT NULL;comment:'是否开启过答题（0: 未开启过，1: 开启过）'" json:"isOpened"`
	Stime             *time.Time `gorm:"column:stime;type:datetime; NULL;default:NULL;comment:'问卷开始时间'" json:"stime"`
	Etime             *time.Time `gorm:"column:etime;type:datetime; NULL;default:NULL;comment:'问卷结束时间'" json:"etime"`
	Type              int32      `gorm:"column:type;type:int(8);default:NULL;comment:'问卷类型'" json:"type"`
	Schema            []byte     `gorm:"column:schema;type:mediumblob;NOT NULL;default:'';comment:'配置 schema'" json:"schema"`
	PreviewSchema     []byte     `gorm:"column:preview_schema;type:mediumblob;NOT NULL;comment:'预览 schema'" json:"previewSchema"`
	Settings          string     `gorm:"column:settings;type:text;comment:'问卷设置'" json:"settings"`
	WebSettings       string     `gorm:"column:web_settings;type:text;comment:'问卷C端用到的配置'" json:"webSettings"`
	Languages         string     `gorm:"column:languages;type:text;comment:'语言包'" json:"languages"`
	HashCode          string     `gorm:"column:hash_code;type:varchar(64);NOT NULL;default:'';comment:'问卷id-hash'" json:"hashCode"`
	IsDelete          int32      `gorm:"column:is_delete;type:tinyint(4);NOT NULL;default:0;comment:'是否删除（0: 未删除，1: 已删除）'" json:"isDelete"`
	Deltime           time.Time  `gorm:"column:deltime;type:datetime;NOT NULL;default:1970-01-01 16:00:01.000;comment:'删除时间'" json:"deltime"`
	KeyValue          string     `gorm:"column:key_value;type:text;NOT NULL;default:'';comment:'中文/英文文案'" json:"key_value"`
	Font              string     `gorm:"column:font;type:text;NOT NULL;default:'';comment:'字体ttf 的oss链接'" json:"font"`
	Remark            string     `gorm:"column:remark;type:varchar(255);NOT NULL;default:'';comment:'备注'" json:"remark"`
	Ctime             time.Time  `gorm:"column:ctime;type:datetime;NOT NULL;default:CURRENT_TIMESTAMP(3);comment:'创建时间'" json:"ctime"`
	Mtime             time.Time  `gorm:"column:mtime;type:datetime;NOT NULL;default:CURRENT_TIMESTAMP(3);comment:'最近修改时间'" json:"mtime"`
	Creator           string     `gorm:"column:creator;type:varchar(64);NOT NULL;default:'';comment:'创建人'" json:"creator"`
	Editor            string     `gorm:"column:editor;type:varchar(64);NOT NULL;default:'';comment:'最近修改人'" json:"editor"`
	ApiVersion        int32      `gorm:"column:api_version;type:tinyint(4);NOT NULL;comment:'接口版本'" json:"api_version"`
}

type Setting struct {
	BaseRuleConfig    *BaseRuleConfig    `json:"baseRuleConfig,omitempty"`
	GiftConfig        *GiftConfig        `json:"giftConfig,omitempty"`
	AnswerLimitConfig *AnswerLimitConfig `json:"answerLimitConfig,omitempty"`
	ZoneIds           []int32            `json:"zoneIds,omitempty"`
	MaterialsConfig   *MaterialsConfig   `json:"materialsConfig,omitempty"`
	FooterConfig      *FooterConfig      `json:"footerConfig,omitempty"`
	SourceConfig      *SourceConfig      `json:"sourceConfig,omitempty"`
}
type AnswerLimitConfig struct {
	LimitType string `json:"limitType,omitempty"`
}

type MaterialsConfig struct {
	AutoLatestMaterial bool   `json:"autoLatestMaterial,omitempty"`
	MaterialVersion    string `json:"materialVersion,omitempty"`
}

type BaseRuleConfig struct {
	ShowQuestionSerialNumber     bool            `json:"showQuestionSerialNumber,omitempty"`
	AnswerQuestionProcessCanBack bool            `json:"answerQuestionProcessCanBack,omitempty"`
	TimeLimitConfig              TimeLimitConfig `json:"timeLimitConfig,omitempty"`
	ShowLegalPage                bool            `json:"showLegalPage,omitempty"`
	LoginType                    string          `json:"loginType,omitempty"`
}
type AnswerTimesConfig struct {
	LimitType int32 `json:"limitType,omitempty"`
	Times     int32 `json:"times,omitempty"`
}

type GiftConfig struct {
	IsGiveOutByCms bool                `json:"isGiveOutByCms,omitempty"`
	GiveOutType    string              `json:"giveOutType,omitempty"`
	PreAwardConfig *PreAwardConfig     `json:"preAwardConfig,omitempty"`
	RedeemConfig   *proto.RedeemConfig `json:"redeemConfig,omitempty"`
}

type PreAwardConfig struct {
	Id string `json:"id,omitempty"`
}

type TimeLimitConfig struct {
	IsTimeLimit bool   `json:"isTimeLimit,omitempty"`
	Stime       string `json:"stime,omitempty"`
	Etime       string `json:"etime,omitempty"`
}

type FooterConfig struct {
	Url  string `json:"url,omitempty"`
	Name string `json:"name,omitempty"`
}

type SourceConfig struct {
	CityUrl    string       `json:"cityUrl,omitempty"`
	Agreements []*Agreement `json:"agreements,omitempty"`
}

type Agreement struct {
	Image string `json:"image,omitempty"`
	Text  string `json:"text,omitempty"`
	Link  string `json:"link,omitempty"`
}

func GetSurveyListData(ctx context.Context, clientId string, status int32, idList []int64, name string, sortCtime string, pageSize, page int32) ([]*CmsSurvey, int64, error) {
	db := database.Get()
	gLog := xlog.FromContext(ctx)
	result := []*CmsSurvey{}
	var totalCount int64

	db = db.Table("cms_survey").Where("is_delete = ?", 0).Where("clientid = ?", clientId)

	// todo 放到 control 和 service 过滤筛选数据
	if len(idList) > 0 {
		db = db.Where("id in (?)", idList)
	}
	if name != "" {
		db = db.Where("name like ?", fmt.Sprintf("%%%s%%", name))
	}
	db = db.Where("api_version >=200")

	// 状态
	// 1: 不是在 关闭，暂停
	// 2: 未设置开始结束时间
	// 3: 大于开始时间，小于结束时间
	// 4: 大于开始时间，结束时间为空
	// 5: 小于结束时间，开始时间为空
	// 6: 问卷已发布
	// 99: 初始化
	if status > 0 {
		now := base.GetNowTime()
		if status == 1 { // NOT_START
			db = db.Where("stime > ?", now)
		}
		if status == 2 { // OVER
			db = db.Where("etime < ?", now)
		}
		if status == 3 { // PROCESSING
			db = db.Where("`schema` is NOT NULL").
				Where("is_pause = ? and is_closed = ?", 0, 0).
				Where("(ISNULL(stime) AND ISNULL(etime)) Or (stime <= ? AND etime >= ?) Or (stime <= ? AND ISNULL(etime)) Or (etime >= ? AND ISNULL(stime))", now, now, now, now)
		}
		if status == 4 { // PAUSE
			db = db.Where("`schema` is NOT NULL").
				Where("is_pause = ? and is_closed = ?", 1, 0).
				Where("(ISNULL(stime) AND ISNULL(etime)) Or (stime <= ? AND etime >= ?) Or (stime <= ? AND ISNULL(etime)) Or (etime >= ? AND ISNULL(stime))", now, now, now, now)
		}
		if status == 5 { // CLOSED
			db = db.Where("is_closed = ?", 1)
		}
		if status == 6 { // published
			db = db.Where("is_publish = ?", 1)
		}
		if status == 99 { // INIT
			db = db.Where("`schema`  is NULL")
		}
	}

	if sortCtime != "" {
		idOrder := fmt.Sprintf("%s%s", " ctime ", sortCtime)
		db = db.Order(idOrder)
	}
	countDB := db
	itemsDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		xlog.Error("countDB.Count with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	if pageSize == 0 {
		pageSize = 10
	}

	pageOffset := int((page - 1) * pageSize)

	itemsDB = itemsDB.Offset(pageOffset).Limit(int(pageSize)).Order("id desc")
	if err := itemsDB.Find(&result).Error; err != nil {
		gLog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	//gLog.Info("GetSurveyListData", zap.Any("surveyId", surveyId), zap.Any("xFilterStatus", xFilterStatus), zap.Any("name", name), zap.Any("eTime", eTime), zap.Any("sTime", sTime), zap.Any("isClosed", isClosed))
	return result, totalCount, nil

}

// 状态
// 0 未知
// 1: 不是在 关闭，暂停
// 2: 未设置开始结束时间
// 3: 大于开始时间，小于结束时间
// 4: 大于开始时间，结束时间为空
// 5: 小于结束时间，开始时间为空
// 6: 删除
// 99: 初始化

// 获取问卷状态
func GetSurveyStatus(data *CmsSurvey) constants.SurveyStatus {
	now := time.Now()
	schema, err := base.UnCompress(data.Schema)
	if err != nil {
		xlog.Error("UnCompress with error", xlog.Err(err))
		schema = ""
	}
	if len(schema) == 0 {
		return constants.SurveyStatusInit
	}
	if data.IsDelete == 1 {
		return constants.SurveyStatusDeleted
	}
	if data.IsClosed == 1 {
		return constants.SurveyStatusClose
	}
	// 判断是否在时间范围内 > 2006-01-02 15:04:05
	if data.IsPause == 1 &&
		((util.TimeIsZero(data.Stime) && util.TimeIsZero(data.Etime)) ||
			(!util.TimeIsZero(data.Stime) && !util.TimeIsZero(data.Etime) && now.After(*data.Stime) && now.Before(*data.Etime)) ||
			(!util.TimeIsZero(data.Stime) && util.TimeIsZero(data.Etime) && now.After(*data.Stime)) ||
			(util.TimeIsZero(data.Stime) && !util.TimeIsZero(data.Etime) && now.Before(*data.Etime)) ||
			(!util.TimeIsZero(data.Stime) && now.Before(*data.Stime))) {
		return constants.SurveyStatusPause
	}
	if data.IsPause == 0 &&
		((util.TimeIsZero(data.Stime) && util.TimeIsZero(data.Etime)) ||
			(!util.TimeIsZero(data.Stime) && !util.TimeIsZero(data.Etime) && now.After(*data.Stime) && now.Before(*data.Etime)) ||
			(!util.TimeIsZero(data.Stime) && util.TimeIsZero(data.Etime) && now.After(*data.Stime)) ||
			(util.TimeIsZero(data.Stime) && !util.TimeIsZero(data.Etime) && now.Before(*data.Etime))) {
		return constants.SurveyStatusProcessing
	}
	if !util.TimeIsZero(data.Stime) && now.Before(*data.Stime) {
		return constants.SurveyStatusNotStart
	}
	//
	if !util.TimeIsZero(data.Etime) && now.After(*data.Etime) {
		return constants.SurveyStatusOver
	}
	return constants.SurveyStatusUnknown
}

// 创建问卷数据
func CreateSurveyData(ctx context.Context, ClientId string, name string, webSettings, settings string, stime string, etime string, creator string) (int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	survey := CmsSurvey{}

	survey.ClientId = ClientId
	survey.Name = name
	survey.Settings = settings
	survey.WebSettings = webSettings
	survey.IsPause = 1
	survey.ApiVersion = 200

	//settingsMap := base.JSONToMap(string(settings))
	var err error
	s, err := time.ParseInLocation(time.DateTime, stime, time.Local)
	if stime != "" && err == nil {
		survey.Stime = &s
	} else {
		survey.Stime = nil
	}

	e, err := time.ParseInLocation(time.DateTime, etime, time.Local)
	if etime != "" && err == nil {
		survey.Etime = &e
	} else {
		survey.Etime = nil
	}

	survey.Creator = creator
	err = db.Table("cms_survey").Create(&survey).Error
	if err != nil {
		gLog.Error("itemsDB.Create with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBCreate, err)
	}
	err = CreateTable(survey.ID)
	if err != nil {
		gLog.Error("CreateSurveyRecordTable with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBCreate, err)
	}
	return survey.ID, nil
}

func StatisticsUpdate(ctx context.Context, id int64, ClientId string, name string, webSettings, settings string, stime string, etime string, editor string) error {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	var count int64
	err := db.Table("cms_survey").Where("id = ?", id).Where("clientid = ?", ClientId).Count(&count).Error
	if err != nil {
		return nil
	} else if count == 0 {
		return gorm.ErrRecordNotFound
	}

	survey := CmsSurvey{}
	survey.Name = name
	survey.Settings = settings
	survey.WebSettings = webSettings
	survey.IsModifyUnpublish = 1

	s, err := time.ParseInLocation(time.DateTime, stime, time.Local)
	if stime != "" && err == nil {
		survey.Stime = &s
	} else {
		survey.Stime = nil
	}

	e, err := time.ParseInLocation(time.DateTime, etime, time.Local)
	if etime != "" && err == nil {
		survey.Etime = &e
	} else {
		survey.Etime = nil
	}

	survey.Editor = editor

	var updateField []string
	if settings == "" { // 如果setting为空，只更新name，editor
		updateField = []string{"name", "editor"}
	} else {
		updateField = []string{"name", "settings", "web_settings", "is_modify_unpublish", "stime", "etime", "editor"}
	}

	err = db.Table("cms_survey").Select(updateField).Where("id = ?", id).Updates(&survey).Error

	if err != nil {
		gLog.Error("itemsDB update with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBCreate, err)
	}
	return nil
}

// 创建答题记录表和答题记录详情表
func CreateTable(surveyId int64) error {
	db := database.Get()
	hashCode, _ := base.SurveyIdToHashCode(surveyId)
	tableRecordName := fmt.Sprintf("%s%s", "survey_record_", hashCode)

	recordSql := fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `uid` varchar(128) NOT NULL COMMENT '用户UID',\n  `roleid` varchar(128) NOT NULL COMMENT '用户roleid',\n  `openid` varchar(64) NOT NULL DEFAULT '' COMMENT 'openid',\n  `role_id` varchar(64) NOT NULL DEFAULT '' COMMENT '角色ID',\n  `device_id` varchar(128) NOT NULL DEFAULT '' COMMENT '设备ID',\n  `ip` varchar(64) NOT NULL DEFAULT '' COMMENT 'ip',\n  `is_valid` tinyint(4) NOT NULL DEFAULT '0' COMMENT '记录是否有效（0: 有效，1: 无效）',\n  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '记录是否删除（0: 未删除，1: 已删除）',\n  `begin_time` datetime NOT NULL COMMENT '答题开始时间',\n  `end_time` datetime NOT NULL COMMENT '答题结束时间',\n  `extra` text NOT NULL COMMENT '额外信息',\n  `ctime` datetime NOT NULL COMMENT '创建时间',\n  PRIMARY KEY (`id`),\n  KEY `idx_openid_role_id` (`openid`,`role_id`),\n  KEY `idx_role_id` (`role_id`),\n  KEY `idx_device_id` (`device_id`),\n  KEY `idx_ip` (`ip`),\n  KEY `idx_ctime` (`ctime`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;", tableRecordName)
	// 执行SQL语句
	if err := db.Exec(recordSql).Error; err != nil {
		return err
	}
	tableRecordDetailName := fmt.Sprintf("%s%s", "survey_record_detail_", hashCode)
	recordDetailSql := fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s  (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `uid` varchar(128) NOT NULL COMMENT '用户UID',\n  `roleid` varchar(128) NOT NULL COMMENT '用户roleid',\n  `survey_record_id` int(11) NOT NULL DEFAULT '0' COMMENT '问卷记录ID',\n  `question` varbinary(64) NOT NULL COMMENT '问卷题目ID',\n  `option` varbinary(64) DEFAULT '' COMMENT '选择类题目用户选项',\n  `text` varchar(1500) DEFAULT '' COMMENT '输入类题目/自定义选项类题目，用户输入内容',\n  PRIMARY KEY (`id`),\n  KEY `idx_survey_record_id` (`survey_record_id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;", tableRecordDetailName)
	if err := db.Exec(recordDetailSql).Error; err != nil {
		return err
	}
	return nil
}

// 格式化settings
func FormatSettings(clientid string, settingsMap map[string]interface{}) map[string]interface{} {
	value, ok := base.GlobalMap["1008"]
	if ok {
		clientInfo, err := base.JSONToMap(value)
		if err != nil {
			xlog.Error("JSONToMap with error", xlog.Err(err))
			return settingsMap
		}
		settingsMap["footerConfig"] = clientInfo["umd"]
		settingsMap["sourceConfig"] = clientInfo["sourceConfig"]
	}
	return settingsMap
}

// 问卷预览
func SurveyPreviewData(ctx context.Context, surveyId int64, clientId string) (*CmsSurvey, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	data := CmsSurvey{}
	err := db.Table("cms_survey").Where("id = ?", surveyId).Where("clientid = ?", clientId).First(&data).Error
	if err != nil {
		gLog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, err
	}
	return &data, nil
}

// 答卷记录详情
// todo 过滤筛选数据到上层处理，model 层减少数据聚合
func SurveyRecordDetailData(ctx context.Context, surveyId int64, id int64) (*CmsSurvey, *SurveyRecord, []*SurveyRecordDetail, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	hashCode, err := base.SurveyIdToHashCode(surveyId)
	if err != nil {
		gLog.Error("SurveyIdToHashCode with error", xlog.Err(err))
		return nil, nil, nil, errors.Wrap(errors.ErrSurveyIdToHashCodeFailed, err)
	}
	cmsSurvey := &CmsSurvey{}
	err = db.Table("cms_survey").Where("id = ?", surveyId).First(&cmsSurvey).Error

	if err != nil {
		gLog.Error("itemsDB.cmsSurvey.Find with error", xlog.Err(err))
		return nil, nil, nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	recordTableName := fmt.Sprintf("survey_record_%s", hashCode)
	surveyRecord := &SurveyRecord{}
	err = db.Table(recordTableName).Where("id = ?", id).First(&surveyRecord).Error
	if err != nil && err.Error() != "record not found" {
		gLog.Error("itemsDB.surveyRecord.Find with error", xlog.Err(err))
		return nil, nil, nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	} else if err != nil && err.Error() == "record not found" {
		return nil, nil, nil, errors.Wrap(errors.ErrNotFound, err)
	}
	var surveyRecordDetails []*SurveyRecordDetail
	err = db.Scopes(SurveyRecordDetailTable(hashCode)).Where("survey_record_id = ?", id).Find(&surveyRecordDetails).Error
	if err != nil {
		gLog.Error("itemsDB.surveyRecordDetail.Find with error", xlog.Err(err))
		return nil, nil, nil, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	return cmsSurvey, surveyRecord, surveyRecordDetails, nil
}

// 删除问卷
func DeleteSurveyData(ctx context.Context, db *gorm.DB, query *proto.SurveyDelRequest) error {
	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserAndNow failed", xlog.Err(err))
		return err
	}
	data := make(map[string]interface{})
	data["is_delete"] = 1
	data["deltime"] = time.Now().Format("2006-01-02 15:04:05")
	data["editor"] = baseInfo.Username
	err = db.Table("cms_survey").Where("id in (?)", query.DelList).Updates(data).Error
	return err
}

// 复制问卷
func CopySurveyData(ctx context.Context, surveyId int64, clientId string) (int64, error) {
	userInfo, err := userpkg.GetUserInfoFromCtx(ctx)
	if err != nil {
		xlog.FromContext(ctx).Error("GetUserInfoFromCtx failed", xlog.Err(err))
		return 0, err
	}
	db := database.Get()
	var info struct {
		Id int64
	}
	var count int64
	err = db.Table("cms_survey").Where("id = ?", surveyId).Where("clientid = ?", clientId).Where("is_delete = ?", 0).Count(&count).Error
	if err != nil {
		return 0, err
	} else if count == 0 {
		return 0, gorm.ErrRecordNotFound
	}

	err = db.Table("cms_survey").Select("MAX(id) as id").Find(&info).Error
	if err != nil {
		xlog.FromContext(ctx).Error("itemsDB.Create with error", xlog.Err(err))
		return 0, err
	}
	info.Id = info.Id + 2
	sql := "INSERT INTO cms_survey(`id`,`clientid`,`name`,`stime`,`etime`,`type`,`schema`,`preview_schema`,`settings`,`web_settings`,`languages`,`remark`,`creator`,`api_version`,`font`,`key_value`) " +
		"SELECT ?, `clientid`,CONCAT(`name`,'【复制】'),`stime`,`etime`,`type`,`schema`,`preview_schema`,`settings`,`web_settings`,`languages`,`remark`,?,`api_version`,`font`,`key_value` " +
		"FROM cms_survey WHERE id = ? AND clientid=? AND is_delete = 0"
	orm := db.Table("cms_survey").Exec(sql, info.Id, userInfo.Username, surveyId, clientId)
	if orm.Error != nil {
		xlog.FromContext(ctx).Error("itemsDB.Create with error", xlog.Err(orm.Error))
		return 0, orm.Error
	}

	if err = CreateTable(info.Id); err != nil {
		xlog.FromContext(ctx).Error("CreateTable with error", xlog.Err(err))
		return 0, err
	}
	return info.Id, nil
}

func GetSurveyById(ctx context.Context, surveyId int64) (*CmsSurvey, error) {
	db := database.Get()
	result := &CmsSurvey{}
	err := db.Table("cms_survey").Where("id = ?", surveyId).First(result).Error
	return result, err
}

func ExistSurveyById(ctx context.Context, clientId string, surveyId int64) (bool, error) {
	db := database.Get()
	var count int64
	err := db.Table("cms_survey").Where("id = ?", surveyId).Where("clientid=?", clientId).Count(&count).Error
	return count > 0, err
}

func GetSurveySettingsById(ctx context.Context, clientId string, surveyId int64) (*CmsSurvey, error) {
	db := database.Get()
	result := &CmsSurvey{}
	err := db.Table("cms_survey").Select("name, settings, stime, etime").Where("id = ?", surveyId).Where("clientid = ?", clientId).First(result).Error
	return result, err
}

// 设置问卷状态
func SetStatusSurveyData(ctx context.Context, ClientId string, surveyId int64, status int32) error {
	gLog := xlog.FromContext(ctx)

	db := database.Get()
	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		xlog.Error("GetUserAndNow failed", xlog.Err(err))
		return err
	}
	data := make(map[string]interface{})
	hashCode, err := base.SurveyIdToHashCode(surveyId)
	if err != nil {
		gLog.Error("itemsDB.cmsSurvey.Find with error", xlog.Err(err))
		return err
	}
	if status == 1 {
		data["is_pause"] = 0
	} else {
		data["is_pause"] = 1
	}
	data["hash_code"] = hashCode
	data["is_opened"] = 1
	data["editor"] = baseInfo.Username
	err = db.Table("cms_survey").Where("id = ?", surveyId).Where("clientid = ?", ClientId).Updates(data).Error
	return err
}

// 发布问卷
func SurveyPublishData(ctx context.Context, surveyId int64) (bool, error) {
	db := database.Get()
	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		xlog.Error("GetUserAndNow failed", xlog.Err(err))
		return false, err
	}
	data := make(map[string]interface{})
	data["is_publish"] = 1
	data["editor"] = baseInfo.Username
	err = db.Table("cms_survey").Where("id = ?", surveyId).Updates(data).Error
	if err != nil {
		xlog.Error("publish survey failed", xlog.Err(err))
		return false, err
	}
	// 创建问卷版本
	result := &proto.Survey{}
	err = db.Table("cms_survey").Where("id = ?", surveyId).First(result).Error
	if err != nil {
		xlog.Error("find survey failed", xlog.Err(err))
		return false, err
	}
	if result.Schema != "" {
		// todo
	}
	err = db.Table("cms_survey").Create(result).Error
	if err != nil {
		xlog.Error("create survey version failed", xlog.Err(err))
		return false, err
	}
	return true, err
}

func UpdatesSurveyDataById(ctx context.Context, surveyId int64, name string, scheme []byte, language string, webSettings string, keyValue string, font string) error {
	db := database.Get()
	gLog := xlog.FromContext(ctx)

	// todo check is field nil for optional

	data := make(map[string]interface{})
	data["languages"] = language
	data["name"] = name
	data["schema"] = scheme
	if webSettings != "" {
		data["web_settings"] = webSettings
	}

	data["is_modify_unpublish"] = 1
	data["key_value"] = keyValue
	data["font"] = font
	err := db.Table("cms_survey").Where("id = ?", surveyId).Updates(&data).Error
	if err != nil {
		gLog.Error("UpdateSurveyData with error", zap.Error(err))
		return err
	}
	return nil
}

func UpdatesSurveyByID(ctx context.Context, id int64, changes map[string]interface{}) error {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	if _, ok := changes["id"]; ok {
		delete(changes, "id")
	}

	for field, value := range changes {
		if v, ok := value.(time.Time); ok && v.IsZero() {
			delete(changes, field)
		}
	}

	result := db.Model(&CmsSurvey{}).Where("id = ?", id).Updates(changes)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		gLog.Warn("no record updated", zap.Int64("survey_id", id))
		return nil
	}
	return nil
}

func InsertSurvey(ctx context.Context, survey *CmsSurvey) (int64, error) {
	db := database.Get()
	err := db.Table("cms_survey").Create(survey).Error
	if err != nil {
		return 0, err
	}
	err = CreateTable(survey.ID)
	if err != nil {
		return 0, err
	}
	return survey.ID, nil
}

// 创建问卷数据
func SurveySave(ctx context.Context, survey *CmsSurvey) (int64, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	err := db.Table("cms_survey").Save(survey).Error

	if err != nil {
		gLog.Error("itemsDB.Create with error", xlog.Err(err))
		return 0, errors.Wrap(errors.ErrDBCreate, err)
	}

	hashCode, _ := base.SurveyIdToHashCode(survey.ID)
	if !db.Migrator().HasTable("survey_record_" + hashCode) {
		err = CreateTable(survey.ID)
		if err != nil {
			gLog.Error("CreateSurveyOne with error", xlog.Err(err))
			return 0, errors.Wrap(errors.ErrDBCreate, err)
		}
	}

	return survey.ID, nil
}

// 问卷 CmsSurvey is_pause 默认值问题
func SurveyPauseDefault(ctx context.Context, surveyID int64, isPause int32) error {
	gLog := xlog.FromContext(ctx)
	db := database.Get()

	err := db.Table("cms_survey").Where("id = ?", surveyID).Update("is_pause", isPause).Error

	if err != nil {
		gLog.Error("SurveyPauseDefault itemsDB.update with error", xlog.Err(err))
		return errors.Wrap(errors.ErrDBCreate, err)
	}

	return nil
}

func GetSurveyByID(ctx context.Context, surveyId int64) (*CmsSurvey, error) {
	gLog := xlog.FromContext(ctx)
	db := database.Get()
	data := CmsSurvey{}
	err := db.Table("cms_survey").Where("id = ?", surveyId).First(&data).Error
	if err != nil {
		gLog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, err
	}
	return &data, nil
}
