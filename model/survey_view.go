package model

import (
	"survey/constants"
	"survey/database"
	"survey/proto"
	"survey/util/base"
)

const TableNameSurveyView = "survey_view"

// 创建筛选器视图
func CreateSurveyView(surveyId int64, name string, content string, kind int32, creator string) (int64, error) {
	db := database.Get()
	data := proto.SurveyView{
		SurveyId: surveyId,
		Name:     name,
		Content:  content,
		Kind:     kind,
		Ctime:    base.GetNowTime(),
		Mtime:    base.GetNowTime(),
		Creator:  creator,
		Editor:   creator,
	}
	res := db.Table(TableNameSurveyView).Create(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return data.Id, nil
}

// 获取筛选器列表
func GetSurveyViewList(request *proto.SurveyViewListReq, isDelete int32) ([]*proto.SurveyView, error) {
	db := database.Get()
	var data []*proto.SurveyView

	queryObj := db.Table(TableNameSurveyView).Where("survey_id = ? and is_delete = ?", request.SurveyId, isDelete)

	if request.Kind > 0 {
		queryObj.Where("kind = ?", request.Kind)
	}

	res := queryObj.Find(&data)
	if res.Error != nil {
		return nil, res.Error
	}
	return data, nil
}

// 获取筛选器- 根据ID
func GetSurveyViewInfoByID(id int64) (*proto.SurveyView, error) {
	db := database.Get()
	var data = proto.SurveyView{}
	res := db.Table(TableNameSurveyView).Where("id = ?", id).First(&data)
	if res.Error != nil {
		return nil, res.Error
	}
	return &data, nil
}

// 获取筛选器- 根据surveyID 和 name (未删除的)
func GetSurveyViewInfoBySurveyIdAndName(surveyId int64, name string, exceptID int64) (*proto.SurveyView, error) {
	db := database.Get()
	var data = proto.SurveyView{}

	baseQuery := db.Table(TableNameSurveyView).Where("survey_id = ? and name = ? and is_delete = ?", surveyId, name, constants.Survey_View_Not_Delete)

	if exceptID > 0 {
		baseQuery = baseQuery.Where("id != ?", exceptID)
	}

	res := baseQuery.First(&data)

	if res.Error != nil {
		return nil, res.Error
	}
	return &data, nil
}

// 编辑筛选器视图
func UpdateSurveyView(id int64, name string, content string, editor string) (int64, error) {
	db := database.Get()
	data := map[string]interface{}{
		"name":    name,
		"content": content,
		"mtime":   base.GetNowTime(),
		"editor":  editor,
	}
	res := db.Table(TableNameSurveyView).Where("id = ?", id).Updates(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

// 删除筛选器视图
func DeleteSurveyView(id int64, editor string) (int64, error) {
	db := database.Get()
	data := map[string]interface{}{
		"is_delete": 1,
		"mtime":     base.GetNowTime(),
		"editor":    editor,
	}
	res := db.Table(TableNameSurveyView).Where("id = ?", id).Updates(&data)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}
