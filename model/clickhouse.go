package model

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcast"
	"strings"
	"survey/config"
	"survey/database"
	"survey/proto"
	"survey/types"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

const (
	TableRecordOds       = "record_ods"
	TableRecordDetailOds = "record_detail_ods"
)

type GroupCountRet struct {
	Question   string `json:"question"`
	GroupCount uint64 `json:"group_count"`
}

type SurveyStatRet struct {
	Question    string `json:"question"`
	Option      string `json:"option"`
	OptionCount uint64 `json:"option_count"`
}

type RecordsRet struct {
	SurveyRecordId uint32    `ch:"survey_record_id"`
	Uid            string    `ch:"uid"`
	Roleid         string    `ch:"roleid"`
	Openid         string    `ch:"openid"`
	DeviceId       string    `ch:"device_id"`
	Ip             string    `ch:"ip"`
	IsValid        uint32    `ch:"is_valid"`
	BeginTime      time.Time `ch:"begin_time"`
	EndTime        time.Time `ch:"end_time"`
	Ctime          time.Time `ch:"ctime"`
	Questions      []string  `ch:"questions"`
	Options        []string  `ch:"options"`
	Texts          []string  `ch:"texts"`
}

func GetGroupCount(ctx context.Context, survey string, excludeRecordIds []string) (map[string]int64, error) {
	db := database.GetClickhouse()
	query := fmt.Sprintf(`
		SELECT question,COUNT(DISTINCT survey_record_id) AS group_count FROM 
		%s WHERE survey_id='%s' and option != ''
	`, TableRecordDetailOds, survey)
	if len(excludeRecordIds) > 0 {
		query += fmt.Sprintf(" AND survey_record_id NOT IN (%s)",
			strings.Join(excludeRecordIds, ","))
	}
	query += " GROUP BY question;"
	var groupCountRet = make(map[string]int64)
	rows, err := db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	for rows.Next() {
		var (
			question   string
			groupCount uint64
		)
		if err := rows.Scan(&question, &groupCount); err != nil {
			return nil, err
		}
		groupCountRet[question] = int64(groupCount)
	}
	if err != nil {
		return nil, fmt.Errorf("ScanStruct failed: %v", err)
	}
	return groupCountRet, nil
}

func GetSurveyStat(ctx context.Context, survey string, excludeRecordIds []string) ([]*types.SurveyDetailStatisticModel, error) {
	db := database.GetClickhouse()
	query := fmt.Sprintf(`
		SELECT question,option,count() AS option_count FROM %s WHERE survey_id='%s' and option != '' 
	`, TableRecordDetailOds, survey)
	if len(excludeRecordIds) > 0 {
		query += fmt.Sprintf(" AND survey_record_id NOT IN (%s)",
			strings.Join(excludeRecordIds, ","))
	}
	query += " GROUP BY question, option;"
	var statRet []*types.SurveyDetailStatisticModel
	rows, err := db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	for rows.Next() {
		var gr = new(types.SurveyDetailStatisticModel)
		var questionStr, optionStr string
		if err := rows.Scan(&questionStr, &optionStr, &gr.OptionCount); err != nil {
			return nil, err
		}
		gr.Question = []byte(questionStr)
		gr.Option = []byte(optionStr)
		statRet = append(statRet, gr)
	}
	return statRet, nil
}

func GetRecords(ctx context.Context, survey string, valid int32,
	startTime *time.Time, endTime *time.Time, offset, limit uint32, viewWhere string) ([]*types.ExportSurveyRecordAndDetailItem, error) {
	db := database.GetClickhouse()
	query := `SELECT 
    r.survey_record_id as survey_record_id, 
    r.uid as uid,
    r.roleid as roleid,
    r.openid as openid,
    r.extra as extra,
    r.roleid as roleid,
    r.device_id as device_id,
    r.ip as ip,
    r.is_valid as is_valid,
    formatDateTime(r.begin_time, '%Y-%m-%d %R:%S') as begin_time,
    formatDateTime(r.end_time, '%Y-%m-%d %R:%S') as end_time,
    formatDateTime(r.ctime, '%Y-%m-%d %R:%S') as ctime,
    r.location as location,
    r.record_ext as record_ext
    FROM {tb_record} r 
	WHERE r.survey_id = '{field_survey}' and r.is_delete = 0 {field_view_where}
	`

	// 过滤器
	if len(viewWhere) > 0 {
		viewWhere = " and " + viewWhere
	}

	r := strings.NewReplacer("{tb_record}", TableRecordOds, "{field_survey}", survey, "{field_view_where}", viewWhere)
	query = r.Replace(query)

	if valid == 1 || valid == 0 {
		query += fmt.Sprintf(" AND r.is_valid = %d", valid)
	}
	if startTime != nil {
		query += fmt.Sprintf(` and r.ctime >= '%s' `, startTime.Format(time.DateTime))
	}
	if endTime != nil {
		query += fmt.Sprintf(` and r.ctime <= '%s' `, endTime.Format(time.DateTime))
	}
	query += fmt.Sprintf(" order by r.survey_id , r.survey_record_id asc LIMIT %d,%d", offset, limit)

	if !config.Get().ExportTaskConf.NoPrintCkSQL {
		xlog.FromContext(ctx).Info("Export GetRecords", xlog.String("sql", query))
	}

	var recordsRet []*types.ExportSurveyRecordAndDetailItem
	rows, err := db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	for rows.Next() {
		var record types.ExportSurveyRecordAndDetailItem
		err = rows.ScanStruct(&record)
		if err != nil {
			return nil, fmt.Errorf("ScanStruct: %v", err)
		}
		recordsRet = append(recordsRet, &record)
	}
	return recordsRet, nil
}

func GetDetails(ctx context.Context, survey string, minSurveyID, maxSurveyID uint32, existsIDMap map[uint32]struct{}) (map[uint32]*types.ExportSurveyRecordAndDetailItem, error) {
	db := database.GetClickhouse()
	query := `SELECT survey_record_id,
       				 groupArray(300)(question) AS questions,
       				 groupArray(300)(option) AS options,
       				 groupArray(300)(text) AS texts
        from {tb_detail}  where survey_id ='{field_survey}' and survey_record_id >= {surveyIDStart} and survey_record_id <= {surveyIDEnd}
    	GROUP BY survey_id,survey_record_id`

	r := strings.NewReplacer(
		"{tb_detail}", TableRecordDetailOds,
		"{field_survey}", survey,
		"{surveyIDStart}", xcast.ToString(minSurveyID),
		"{surveyIDEnd}", xcast.ToString(maxSurveyID),
	)
	query = r.Replace(query)

	if !config.Get().ExportTaskConf.NoPrintCkSQL {
		xlog.FromContext(ctx).Info("Export GetDetails", xlog.String("sql", query))
	}

	var recordsMap = make(map[uint32]*types.ExportSurveyRecordAndDetailItem)
	rows, err := db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	for rows.Next() {
		var record types.ExportSurveyRecordAndDetailItem
		err = rows.ScanStruct(&record)
		if err != nil {
			return nil, fmt.Errorf("ScanStruct: %v", err)
		}

		// 不存在，跳过
		if _, ok := existsIDMap[record.ID]; !ok {
			continue
		}

		recordsMap[record.ID] = &record
	}
	return recordsMap, nil
}

func GetRecordsWithDetails(ctx context.Context, survey string, valid int32,
	startTime *time.Time, endTime *time.Time, offset, limit uint32, viewWhere string) ([]*types.ExportSurveyRecordAndDetailItem, error) {

	// 获取问卷记录
	records, err := GetRecords(ctx, survey, valid, startTime, endTime, offset, limit, viewWhere)
	if err != nil {
		return nil, err
	}

	// 没数据了
	if len(records) < 1 {
		return records, nil
	}

	var (
		minSurveyID uint32
		maxSurveyID uint32
	)

	var existsIDMap = make(map[uint32]struct{})
	for _, record := range records {
		existsIDMap[record.ID] = struct{}{}
		if minSurveyID == 0 {
			minSurveyID = record.ID
		} else if minSurveyID > record.ID {
			minSurveyID = record.ID
		}
		if record.ID > maxSurveyID {
			maxSurveyID = record.ID
		}
	}

	//获取详情
	detailsMap, err := GetDetails(ctx, survey, minSurveyID, maxSurveyID, existsIDMap)
	if err != nil {
		return nil, err
	}

	if detailsMap == nil {
		return nil, fmt.Errorf("GetRecordsWithDetails has no details, survey:%+v min:%d max:%d", survey, minSurveyID, maxSurveyID)
	}

	for i, record := range records {
		if detail := detailsMap[record.ID]; detail != nil {
			records[i].Questions = detail.Questions
			records[i].Options = detail.Options
			records[i].Texts = detail.Texts
		} else {
			xlog.FromContext(ctx).Info("GetRecordsWithDetails has no detail recordID:%d", xlog.Uint32("recordId", record.ID))
		}
	}

	return records, err
}

func UpdateRecord(ctx context.Context, surveyId string, recordId int64,
	columnName string, columnValue uint32) error {
	db := database.GetClickhouse()
	query := fmt.Sprintf("ALTER TABLE %s UPDATE %s = ? WHERE survey_id = ? and survey_record_id = ?", TableRecordOds, columnName)
	err := db.Exec(ctx, query, columnValue, surveyId, recordId)
	if err != nil {
		return err
	}
	return nil
}

func SetRecordInvalid(ctx context.Context, surveyId string, recordId []int64) error {
	db := database.GetClickhouse()
	if len(recordId) == 0 {
		return nil
	}
	query := fmt.Sprintf("ALTER TABLE %s UPDATE is_valid = 1 WHERE survey_id = ? and survey_record_id in ?", TableRecordOds)
	err := db.Exec(ctx, query, surveyId, recordId)
	if err != nil {
		return err
	}
	return nil
}

func DeleteRecord(ctx context.Context, surveyId string, recordId []int64) error {
	db := database.GetClickhouse()
	if len(recordId) == 0 {
		return nil
	}
	query := fmt.Sprintf("ALTER TABLE %s UPDATE is_delete = 1 WHERE survey_id = ? and survey_record_id in ?", TableRecordOds)
	err := db.Exec(ctx, query, surveyId, recordId)
	if err != nil {
		return err
	}
	return nil
}

func GetRecordCount(ctx context.Context, survey string, valid int32, startTime *time.Time, endTime *time.Time, viewWhereSql string) (uint64, error) {
	db := database.GetClickhouse()

	query := `
SELECT
    COUNT() 
FROM (
	SELECT
		r.survey_record_id
	FROM
		{tb_record} r
		INNER JOIN {tb_detail} d ON r.survey_id = '{field_survey}' and d.survey_id = '{field_survey}' and r.survey_record_id = d.survey_record_id 
	WHERE
		r.survey_id = '{field_survey}' {field_view_where}
	GROUP BY
		r.survey_record_id 
	) c 
INNER JOIN {tb_record} b ON b.survey_id = '{field_survey}' and b.survey_record_id = c.survey_record_id 
WHERE 
	b.survey_id = '{field_survey}' 
	AND b.is_delete = 0
`

	if valid == 1 || valid == 0 {
		query += fmt.Sprintf(" AND b.is_valid = %d", valid)
	}
	if startTime != nil {
		query += fmt.Sprintf(` and b.end_time >= '%s' `, startTime.Format(time.DateTime))
	}
	if endTime != nil {
		query += fmt.Sprintf(` and b.end_time <= '%s' `, endTime.Format(time.DateTime))
	}

	if len(viewWhereSql) > 0 {
		viewWhereSql = " and " + viewWhereSql
	}

	r := strings.NewReplacer("{tb_record}", TableRecordOds, "{tb_detail}", TableRecordDetailOds, "{field_view_where}", viewWhereSql, "{field_survey}", survey)
	query = r.Replace(query)

	var (
		n  uint64
		st = time.Now()
	)
	err := db.QueryRow(ctx, query).Scan(&n)

	xlog.FromContext(ctx).Info("GetRecordCount query duration time is", xlog.String(":", time.Since(st).String()))

	if err != nil {
		return 0, err
	}
	return n, nil
}

func GetRecordCountNew(ctx context.Context, survey string, valid int32, startTime *time.Time, endTime *time.Time, viewWhereSql string) (uint64, error) {
	db := database.GetClickhouse()
	query := `SELECT count() from {tb_record} r WHERE r.survey_id = '{field_survey}' and r.is_delete = 0`
	if valid == 1 || valid == 0 {
		query += fmt.Sprintf(" AND r.is_valid = %d", valid)
	}
	if startTime != nil {
		query += fmt.Sprintf(` and r.ctime >= '%s' `, startTime.Format(time.DateTime))
	}
	if endTime != nil {
		query += fmt.Sprintf(` and r.ctime <= '%s' `, endTime.Format(time.DateTime))
	}

	if len(viewWhereSql) > 0 {
		viewWhereSql = " and " + viewWhereSql
		query += " {field_view_where} "
	}

	r := strings.NewReplacer("{tb_record}", TableRecordOds, "{field_view_where}", viewWhereSql, "{field_survey}", survey)
	query = r.Replace(query)

	if !config.Get().ExportTaskConf.NoPrintCkSQL {
		xlog.FromContext(ctx).Info("Export GetRecordCountNew", xlog.String("sql", query))
	}

	var n uint64
	err := db.QueryRow(ctx, query).Scan(&n)
	if err != nil {
		return 0, err
	}
	return n, nil
}

func GetRecordFullValidUserCount(ctx context.Context, survey string) (uint64, error) {
	db := database.GetClickhouse()
	query := `SELECT count(DISTINCT concat(openid, roleid, device_id, ip)) from ? WHERE survey_id = ? and is_delete = 0 and is_valid = 0`
	var n uint64
	err := db.QueryRow(ctx, query, TableRecordOds, survey).Scan(&n)
	if err != nil {
		return 0, err
	}
	return n, nil
}

func GetRecordFullInvalidRecordIds(ctx context.Context, survey string) ([]int64, error) {
	db := database.GetClickhouse()
	query := `SELECT DISTINCT survey_record_id from ? WHERE survey_id = ? and (is_delete != 0 OR is_valid != 0)`
	rows, err := db.Query(ctx, query, TableRecordOds, survey)
	var list = make([]int64, 0)
	if err != nil {
		return list, err
	}

	for rows.Next() {
		var recordId uint32
		err := rows.Scan(&recordId)
		if err != nil {
			return nil, err
		}
		list = append(list, int64(recordId))
	}

	return list, nil
}

func GetSurveyInputMethodListCk(ctx context.Context, surveyID string, whereStr string, page, pageSize int32) ([]*proto.InputMethodListResponseData, error) {
	var list = make([]*proto.InputMethodListResponseData, 0)
	var db = database.GetClickhouse()
	var limitStart = (page - 1) * pageSize
	query := "select survey_record_id AS id,survey_record_id,question,`option`,`text` from ? where survey_id = ? " + whereStr + " limit ?,?"

	rows, err := db.Query(ctx, query, TableRecordDetailOds, surveyID, limitStart, pageSize)
	if err != nil {
		return list, fmt.Errorf("GetSurveyInputMethodListCk failed to fetch list err: %v", err)
	}

	// 查record
	var surveyRecordIds []int64
	var dataMap = make(map[int64]*proto.InputMethodListResponseData)
	for rows.Next() {
		var data proto.InputMethodListResponseData
		var (
			id       uint32
			sid      uint32
			question string
			option   string
			text     string
		)
		err := rows.Scan(&id, &sid, &question, &option, &text)
		if err != nil {
			return nil, err
		}
		data = proto.InputMethodListResponseData{
			Id:             int64(id),
			Option:         option,
			Question:       question,
			SurveyRecordId: int64(sid),
			Text:           text,
		}
		list = append(list, &data)
		surveyRecordIds = append(surveyRecordIds, data.SurveyRecordId)
		dataMap[data.SurveyRecordId] = &data
	}

	if len(surveyRecordIds) > 0 {
		query2 := "select survey_record_id,openid,roleid AS role_id,ip,device_id from ? where survey_id = ? and survey_record_id in ?"
		r, err := db.Query(ctx, query2, TableRecordOds, surveyID, surveyRecordIds)

		if err != nil {
			return list, fmt.Errorf("GetSurveyInputMethodList res2 failed to fetch list err: %v", err)
		}

		for r.Next() {
			var data proto.InputMethodListResponseData
			var sid uint32
			err := r.Scan(&sid, &data.Openid, &data.RoleId, &data.Ip, &data.DeviceId)
			if err != nil {
				return nil, err
			}
			data.SurveyRecordId = int64(sid)
			if dataMap[data.SurveyRecordId] != nil {
				dataMap[data.SurveyRecordId].Openid = data.Openid
				dataMap[data.SurveyRecordId].RoleId = data.RoleId
				dataMap[data.SurveyRecordId].Ip = data.Ip
				dataMap[data.SurveyRecordId].DeviceId = data.DeviceId
			}
		}
	}

	return list, nil
}

func GetSurveyInputMethodCountCK(ctx context.Context, surveyID string, whereStr string) (uint64, error) {
	db := database.GetClickhouse()
	query := `SELECT count(distinct survey_record_id) from ? d WHERE survey_id = ? ` + whereStr
	var n uint64
	err := db.QueryRow(ctx, query, TableRecordDetailOds, surveyID).Scan(&n)
	if err != nil {
		return 0, err
	}
	return n, nil
}
