package model

import (
	"context"
	"fmt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gorm.io/gorm"
	"survey/database"
	"survey/proto"
	"survey/util/base"
	"survey/util/errors"
)

func SurveyRecycleListData(ctx context.Context, clientId string, id int64, name string, sortCtime string, pageSize, page int32) ([]*CmsSurvey, int64, error) {
	db := database.Get()
	gLog := xlog.FromContext(ctx)
	result := []*CmsSurvey{}
	var totalCount int64

	db = db.Table("cms_survey").Where("is_delete = ?", 1).Where("clientid = ?", clientId)

	if id > 0 {
		db = db.Where("id = ?", id)
	}
	if name != "" {
		db = db.Where("name like ?", "%"+name+"%")
	}

	if sortCtime != "" {
		idOrder := fmt.Sprintf("%s%s", " ctime ", sortCtime)
		db = db.Order(idOrder)
	}
	countDB := db
	itemsDB := db

	if err := countDB.Count(&totalCount).Error; err != nil {
		xlog.Error("countDB.Count with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}
	if pageSize == 0 {
		pageSize = 10
	}

	pageOffset := int((page - 1) * pageSize)

	itemsDB = itemsDB.Offset(pageOffset).Limit(int(pageSize)).Order("id desc")
	if err := itemsDB.Find(&result).Error; err != nil {
		gLog.Error("itemsDB.Find with error", xlog.Err(err))
		return nil, 0, errors.Wrap(errors.ErrDBOperateFailed, err)
	}

	//gLog.Info("GetSurveyListData", zap.Any("surveyId", surveyId), zap.Any("xFilterStatus", xFilterStatus), zap.Any("name", name), zap.Any("eTime", eTime), zap.Any("sTime", sTime), zap.Any("isClosed", isClosed))
	return result, totalCount, nil
}

// 彻底删除问卷
func DeleteSurveyRecycleData(ctx context.Context, db *gorm.DB, query *proto.SurveyDelRequest) error {
	data := make(map[string]interface{})
	data["id"] = query.DelList
	err := db.Table("cms_survey").Where("id in (?)", query.DelList).
		Where("clientid = ?", query.ClientId).Delete(data).Error
	return err
}

// 恢复问卷
func RecoverSurveyRecycleData(ctx context.Context, clientId string, list []int64) error {
	baseInfo, err := base.GetUserAndNow(ctx)
	if err != nil {
		xlog.Error("GetUserAndNow failed", xlog.Err(err))
		return err
	}

	data := make(map[string]interface{})
	data["is_delete"] = 0
	data["editor"] = baseInfo.Username
	db := database.Get()
	err = db.Table("cms_survey").Where("clientid = ?", clientId).Where("id in (?)", list).Updates(data).Error
	return err
}

// 清空回收问卷
func ClearAllSurveyRecycleData(ctx context.Context, db *gorm.DB, query *proto.ClearAllSurveyRecycleRequest) error {
	data := make(map[string]interface{})
	err := db.Table("cms_survey").
		Where("clientid=?", query.ClientId).
		Where("is_delete = ?", 1).Delete(data).Error
	return err
}

// 恢复所有问卷
func RecoverAllSurveyRecycleData(ctx context.Context, db *gorm.DB, query *proto.Survey) error {
	err := db.Table("cms_survey").
		Where("is_delete = ?", 1).
		Where("clientid = ?", query.ClientId).
		Update("is_delete", 0).Error
	return err
}
