package main

import (
	"gitlab.papegames.com/fringe/sparrow"
	"survey/broker"
	_ "survey/model"
	"survey/service"
	"survey/worker"

	aweStartup "gitlab.papegames.com/awe/awe_bff/startup"
	"survey/config"
	"survey/database"
	"survey/server"
)

func main() {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		database.Startup,
		broker.Startup,
		service.Startup,
		server.Startup,
		worker.Startup,
		aweStartup.StartupConsul,
	).Server(server.Get()).Worker(worker.Get()).Launch()
}
