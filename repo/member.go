package repo

import (
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gitlab.papegames.com/fringe/pkg/http"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"net/url"
	"survey/config"
	"survey/proto"
	"survey/util/userpkg"
)

const (
	memberApiLists      = "/member/list"
	memberApiCreate     = "/member/create"
	memberApiShow       = "/member/show"
	memberApiUpdate     = "/member/update"
	memberApiDelete     = "/member/delete"
	memberApiClientList = "/member/client_list"
)

func NewMemberRepo() *MemberRepo {
	return &MemberRepo{}
}

type MemberRepo struct {
}

func (m MemberRepo) GetClientList(ctx context.Context) ([]*proto.Clientlist, error) {
	user, err := userpkg.GetUserInfoFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	params := make(url.Values)
	params.Set("username", user.Username)

	body, err := m.done(ctx, memberApiClientList, params)
	if err != nil {
		return nil, err
	}
	clientListRes := make([]*proto.Clientlist, 0)
	if err := sonic.Unmarshal(body, &clientListRes); err != nil {
		return nil, err
	}
	return clientListRes, nil

}

func (m MemberRepo) done(ctx context.Context, u string, params url.Values) ([]byte, error) {
	req, err := http.NewRequest(ctx)
	if err != nil {
		return nil, err
	}
	req.SetHeader("Content-Type", "application/x-www-form-urlencoded")
	req.SetHeader("Accept", "application/json")
	req.SetHeader("authorization", "0f3c66f5020e40bcdf3f3120f685c190")

	u = fmt.Sprintf("%s%s", config.Get().AuthUrl, u)

	params.Set("app_id", "12363")
	params.Set("client_id", "")
	req.SetQueryParamsFromValues(params)
	res, err := req.Get(u)
	if err != nil {
		return nil, err
	}

	xlog.FromContext(ctx).Info(" request url", xlog.String("url", u), xlog.Any("params", params), xlog.Int("status", res.StatusCode()), xlog.ByteString("body", res.Body()))

	if !res.IsSuccess() {
		return nil, fmt.Errorf("pigeon request %s failed, status code: %d", u, res.StatusCode())
	}

	return res.Body(), nil
}
