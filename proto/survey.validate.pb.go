// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey.proto

package proto

func (x *HealthRequest) Validate() error {
	return nil
}

func (x *GetUserInfoRequest) Validate() error {
	return nil
}

func (x *GetUserInfoResponse) Validate() error {
	if v, ok := interface{}(x.GetUserInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:   "UserInfo",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	for _, item := range x.GetClientList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserInfoResponseValidationError{
					field:   "ClientList",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	if v, ok := interface{}(x.Get<PERSON>ermission()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoResponseValidationError{
				field:   "Permission",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Userinfo) Validate() error {
	return nil
}

func (x *Clientlist) Validate() error {
	return nil
}

func (x *Permission) Validate() error {
	return nil
}

func (x *JwtUserInfoV1) Validate() error {
	if v, ok := interface{}(x.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return JwtUserInfoV1ValidationError{
				field:   "Data",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *JwtUserInfoV1_Data) Validate() error {
	return nil
}

func (x *UserCheckRequest) Validate() error {
	return nil
}

func (x *UserCheckResponse) Validate() error {
	return nil
}

func (x *ClearAllSurveyRecycleRequest) Validate() error {
	return nil
}

func (x *CreateSurveyRequest) Validate() error {
	if len(x.GetName()) == 0 {
		return CreateSurveyRequestValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSettings()) == 0 {
		return CreateSurveyRequestValidationError{
			field:   "Settings",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *StatisticsUpdateRequest) Validate() error {
	if x.GetId() < 1 {
		return StatisticsUpdateRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	if len(x.GetName()) == 0 {
		return StatisticsUpdateRequestValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyStatisticsRequest) Validate() error {
	return nil
}

func (x *SurveyStatisticsResponse) Validate() error {
	if len(x.GetName()) == 0 {
		return SurveyStatisticsResponseValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSettings()) == 0 {
		return SurveyStatisticsResponseValidationError{
			field:   "Settings",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *GetLatestSurveyBySurveyIdRequest) Validate() error {
	return nil
}

func (x *GetLatestSurveyBySurveyIdResponse) Validate() error {
	if x.GetId() < 1 {
		return GetLatestSurveyBySurveyIdResponseValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	if len(x.GetName()) == 0 {
		return GetLatestSurveyBySurveyIdResponseValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSchema()) == 0 {
		return GetLatestSurveyBySurveyIdResponseValidationError{
			field:   "Schema",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSettings()) == 0 {
		return GetLatestSurveyBySurveyIdResponseValidationError{
			field:   "Settings",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetWebSettings()) == 0 {
		return GetLatestSurveyBySurveyIdResponseValidationError{
			field:   "WebSettings",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *UploadRequest) Validate() error {
	return nil
}

func (x *UploadResponse) Validate() error {
	return nil
}

type HealthRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e HealthRequestValidationError) Field() string { return e.field }

func (e HealthRequestValidationError) Reason() string { return e.reason }

func (e HealthRequestValidationError) Message() string { return e.message }

func (e HealthRequestValidationError) Cause() error { return e.cause }

func (e HealthRequestValidationError) ErrorName() string { return "HealthRequestValidationError" }

func (e HealthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid HealthRequest." + e.field + ": " + e.message + cause
}

type GetUserInfoRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetUserInfoRequestValidationError) Field() string { return e.field }

func (e GetUserInfoRequestValidationError) Reason() string { return e.reason }

func (e GetUserInfoRequestValidationError) Message() string { return e.message }

func (e GetUserInfoRequestValidationError) Cause() error { return e.cause }

func (e GetUserInfoRequestValidationError) ErrorName() string {
	return "GetUserInfoRequestValidationError"
}

func (e GetUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetUserInfoRequest." + e.field + ": " + e.message + cause
}

type GetUserInfoResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetUserInfoResponseValidationError) Field() string { return e.field }

func (e GetUserInfoResponseValidationError) Reason() string { return e.reason }

func (e GetUserInfoResponseValidationError) Message() string { return e.message }

func (e GetUserInfoResponseValidationError) Cause() error { return e.cause }

func (e GetUserInfoResponseValidationError) ErrorName() string {
	return "GetUserInfoResponseValidationError"
}

func (e GetUserInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetUserInfoResponse." + e.field + ": " + e.message + cause
}

type UserinfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UserinfoValidationError) Field() string { return e.field }

func (e UserinfoValidationError) Reason() string { return e.reason }

func (e UserinfoValidationError) Message() string { return e.message }

func (e UserinfoValidationError) Cause() error { return e.cause }

func (e UserinfoValidationError) ErrorName() string { return "UserinfoValidationError" }

func (e UserinfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Userinfo." + e.field + ": " + e.message + cause
}

type ClientlistValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ClientlistValidationError) Field() string { return e.field }

func (e ClientlistValidationError) Reason() string { return e.reason }

func (e ClientlistValidationError) Message() string { return e.message }

func (e ClientlistValidationError) Cause() error { return e.cause }

func (e ClientlistValidationError) ErrorName() string { return "ClientlistValidationError" }

func (e ClientlistValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Clientlist." + e.field + ": " + e.message + cause
}

type PermissionValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PermissionValidationError) Field() string { return e.field }

func (e PermissionValidationError) Reason() string { return e.reason }

func (e PermissionValidationError) Message() string { return e.message }

func (e PermissionValidationError) Cause() error { return e.cause }

func (e PermissionValidationError) ErrorName() string { return "PermissionValidationError" }

func (e PermissionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Permission." + e.field + ": " + e.message + cause
}

type JwtUserInfoV1ValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e JwtUserInfoV1ValidationError) Field() string { return e.field }

func (e JwtUserInfoV1ValidationError) Reason() string { return e.reason }

func (e JwtUserInfoV1ValidationError) Message() string { return e.message }

func (e JwtUserInfoV1ValidationError) Cause() error { return e.cause }

func (e JwtUserInfoV1ValidationError) ErrorName() string { return "JwtUserInfoV1ValidationError" }

func (e JwtUserInfoV1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid JwtUserInfoV1." + e.field + ": " + e.message + cause
}

type JwtUserInfoV1_DataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e JwtUserInfoV1_DataValidationError) Field() string { return e.field }

func (e JwtUserInfoV1_DataValidationError) Reason() string { return e.reason }

func (e JwtUserInfoV1_DataValidationError) Message() string { return e.message }

func (e JwtUserInfoV1_DataValidationError) Cause() error { return e.cause }

func (e JwtUserInfoV1_DataValidationError) ErrorName() string {
	return "JwtUserInfoV1_DataValidationError"
}

func (e JwtUserInfoV1_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid JwtUserInfoV1_Data." + e.field + ": " + e.message + cause
}

type UserCheckRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UserCheckRequestValidationError) Field() string { return e.field }

func (e UserCheckRequestValidationError) Reason() string { return e.reason }

func (e UserCheckRequestValidationError) Message() string { return e.message }

func (e UserCheckRequestValidationError) Cause() error { return e.cause }

func (e UserCheckRequestValidationError) ErrorName() string { return "UserCheckRequestValidationError" }

func (e UserCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UserCheckRequest." + e.field + ": " + e.message + cause
}

type UserCheckResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UserCheckResponseValidationError) Field() string { return e.field }

func (e UserCheckResponseValidationError) Reason() string { return e.reason }

func (e UserCheckResponseValidationError) Message() string { return e.message }

func (e UserCheckResponseValidationError) Cause() error { return e.cause }

func (e UserCheckResponseValidationError) ErrorName() string {
	return "UserCheckResponseValidationError"
}

func (e UserCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UserCheckResponse." + e.field + ": " + e.message + cause
}

type ClearAllSurveyRecycleRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ClearAllSurveyRecycleRequestValidationError) Field() string { return e.field }

func (e ClearAllSurveyRecycleRequestValidationError) Reason() string { return e.reason }

func (e ClearAllSurveyRecycleRequestValidationError) Message() string { return e.message }

func (e ClearAllSurveyRecycleRequestValidationError) Cause() error { return e.cause }

func (e ClearAllSurveyRecycleRequestValidationError) ErrorName() string {
	return "ClearAllSurveyRecycleRequestValidationError"
}

func (e ClearAllSurveyRecycleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ClearAllSurveyRecycleRequest." + e.field + ": " + e.message + cause
}

type CreateSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CreateSurveyRequestValidationError) Field() string { return e.field }

func (e CreateSurveyRequestValidationError) Reason() string { return e.reason }

func (e CreateSurveyRequestValidationError) Message() string { return e.message }

func (e CreateSurveyRequestValidationError) Cause() error { return e.cause }

func (e CreateSurveyRequestValidationError) ErrorName() string {
	return "CreateSurveyRequestValidationError"
}

func (e CreateSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CreateSurveyRequest." + e.field + ": " + e.message + cause
}

type StatisticsUpdateRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e StatisticsUpdateRequestValidationError) Field() string { return e.field }

func (e StatisticsUpdateRequestValidationError) Reason() string { return e.reason }

func (e StatisticsUpdateRequestValidationError) Message() string { return e.message }

func (e StatisticsUpdateRequestValidationError) Cause() error { return e.cause }

func (e StatisticsUpdateRequestValidationError) ErrorName() string {
	return "StatisticsUpdateRequestValidationError"
}

func (e StatisticsUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid StatisticsUpdateRequest." + e.field + ": " + e.message + cause
}

type SurveyStatisticsRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyStatisticsRequestValidationError) Field() string { return e.field }

func (e SurveyStatisticsRequestValidationError) Reason() string { return e.reason }

func (e SurveyStatisticsRequestValidationError) Message() string { return e.message }

func (e SurveyStatisticsRequestValidationError) Cause() error { return e.cause }

func (e SurveyStatisticsRequestValidationError) ErrorName() string {
	return "SurveyStatisticsRequestValidationError"
}

func (e SurveyStatisticsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyStatisticsRequest." + e.field + ": " + e.message + cause
}

type SurveyStatisticsResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyStatisticsResponseValidationError) Field() string { return e.field }

func (e SurveyStatisticsResponseValidationError) Reason() string { return e.reason }

func (e SurveyStatisticsResponseValidationError) Message() string { return e.message }

func (e SurveyStatisticsResponseValidationError) Cause() error { return e.cause }

func (e SurveyStatisticsResponseValidationError) ErrorName() string {
	return "SurveyStatisticsResponseValidationError"
}

func (e SurveyStatisticsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyStatisticsResponse." + e.field + ": " + e.message + cause
}

type GetLatestSurveyBySurveyIdRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetLatestSurveyBySurveyIdRequestValidationError) Field() string { return e.field }

func (e GetLatestSurveyBySurveyIdRequestValidationError) Reason() string { return e.reason }

func (e GetLatestSurveyBySurveyIdRequestValidationError) Message() string { return e.message }

func (e GetLatestSurveyBySurveyIdRequestValidationError) Cause() error { return e.cause }

func (e GetLatestSurveyBySurveyIdRequestValidationError) ErrorName() string {
	return "GetLatestSurveyBySurveyIdRequestValidationError"
}

func (e GetLatestSurveyBySurveyIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetLatestSurveyBySurveyIdRequest." + e.field + ": " + e.message + cause
}

type GetLatestSurveyBySurveyIdResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetLatestSurveyBySurveyIdResponseValidationError) Field() string { return e.field }

func (e GetLatestSurveyBySurveyIdResponseValidationError) Reason() string { return e.reason }

func (e GetLatestSurveyBySurveyIdResponseValidationError) Message() string { return e.message }

func (e GetLatestSurveyBySurveyIdResponseValidationError) Cause() error { return e.cause }

func (e GetLatestSurveyBySurveyIdResponseValidationError) ErrorName() string {
	return "GetLatestSurveyBySurveyIdResponseValidationError"
}

func (e GetLatestSurveyBySurveyIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetLatestSurveyBySurveyIdResponse." + e.field + ": " + e.message + cause
}

type UploadRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UploadRequestValidationError) Field() string { return e.field }

func (e UploadRequestValidationError) Reason() string { return e.reason }

func (e UploadRequestValidationError) Message() string { return e.message }

func (e UploadRequestValidationError) Cause() error { return e.cause }

func (e UploadRequestValidationError) ErrorName() string { return "UploadRequestValidationError" }

func (e UploadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UploadRequest." + e.field + ": " + e.message + cause
}

type UploadResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UploadResponseValidationError) Field() string { return e.field }

func (e UploadResponseValidationError) Reason() string { return e.reason }

func (e UploadResponseValidationError) Message() string { return e.message }

func (e UploadResponseValidationError) Cause() error { return e.cause }

func (e UploadResponseValidationError) ErrorName() string { return "UploadResponseValidationError" }

func (e UploadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UploadResponse." + e.field + ": " + e.message + cause
}
