// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthRequest) Reset() {
	*x = HealthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthRequest) ProtoMessage() {}

func (x *HealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthRequest.ProtoReflect.Descriptor instead.
func (*HealthRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{0}
}

type GetUserInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *GetUserInfoRequest) Reset() {
	*x = GetUserInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoRequest) ProtoMessage() {}

func (x *GetUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserInfoRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type GetUserInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo   *Userinfo     `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
	ClientList []*Clientlist `protobuf:"bytes,2,rep,name=clientList,proto3" json:"clientList,omitempty"`
	Permission *Permission   `protobuf:"bytes,3,opt,name=permission,proto3" json:"permission,omitempty"`
}

func (x *GetUserInfoResponse) Reset() {
	*x = GetUserInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResponse) ProtoMessage() {}

func (x *GetUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResponse.ProtoReflect.Descriptor instead.
func (*GetUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserInfoResponse) GetUserInfo() *Userinfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetUserInfoResponse) GetClientList() []*Clientlist {
	if x != nil {
		return x.ClientList
	}
	return nil
}

func (x *GetUserInfoResponse) GetPermission() *Permission {
	if x != nil {
		return x.Permission
	}
	return nil
}

type Userinfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Phone    string `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Avatar   string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Email    string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	ClientId int64  `protobuf:"varint,8,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *Userinfo) Reset() {
	*x = Userinfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Userinfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Userinfo) ProtoMessage() {}

func (x *Userinfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Userinfo.ProtoReflect.Descriptor instead.
func (*Userinfo) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{3}
}

func (x *Userinfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Userinfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Userinfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *Userinfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *Userinfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Userinfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Userinfo) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type Clientlist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId        int64  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Memo            string `protobuf:"bytes,2,opt,name=memo,proto3" json:"memo,omitempty"`
	DefaultLanguage string `protobuf:"bytes,3,opt,name=default_language,json=defaultLanguage,proto3" json:"default_language,omitempty"`
	GroupId         string `protobuf:"bytes,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupName       string `protobuf:"bytes,5,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
}

func (x *Clientlist) Reset() {
	*x = Clientlist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Clientlist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Clientlist) ProtoMessage() {}

func (x *Clientlist) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Clientlist.ProtoReflect.Descriptor instead.
func (*Clientlist) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{4}
}

func (x *Clientlist) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *Clientlist) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *Clientlist) GetDefaultLanguage() string {
	if x != nil {
		return x.DefaultLanguage
	}
	return ""
}

func (x *Clientlist) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Clientlist) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

type Permission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveySummaryGet    bool `protobuf:"varint,1,opt,name=surveySummary_get,json=surveySummaryGet,proto3" json:"surveySummary_get,omitempty"`
	SurveyManageAdd     bool `protobuf:"varint,2,opt,name=surveyManage_add,json=surveyManageAdd,proto3" json:"surveyManage_add,omitempty"`
	SurveyManageEdit    bool `protobuf:"varint,3,opt,name=surveyManage_edit,json=surveyManageEdit,proto3" json:"surveyManage_edit,omitempty"`
	SurveySummaryExport bool `protobuf:"varint,4,opt,name=surveySummary_export,json=surveySummaryExport,proto3" json:"surveySummary_export,omitempty"`
	SurveyManageDelete  bool `protobuf:"varint,5,opt,name=surveyManage_delete,json=surveyManageDelete,proto3" json:"surveyManage_delete,omitempty"`
	MemberGet           bool `protobuf:"varint,6,opt,name=member_get,json=memberGet,proto3" json:"member_get,omitempty"`
	MemberAdd           bool `protobuf:"varint,7,opt,name=member_add,json=memberAdd,proto3" json:"member_add,omitempty"`
	MemberDelete        bool `protobuf:"varint,8,opt,name=member_delete,json=memberDelete,proto3" json:"member_delete,omitempty"`
}

func (x *Permission) Reset() {
	*x = Permission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Permission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Permission) ProtoMessage() {}

func (x *Permission) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Permission.ProtoReflect.Descriptor instead.
func (*Permission) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{5}
}

func (x *Permission) GetSurveySummaryGet() bool {
	if x != nil {
		return x.SurveySummaryGet
	}
	return false
}

func (x *Permission) GetSurveyManageAdd() bool {
	if x != nil {
		return x.SurveyManageAdd
	}
	return false
}

func (x *Permission) GetSurveyManageEdit() bool {
	if x != nil {
		return x.SurveyManageEdit
	}
	return false
}

func (x *Permission) GetSurveySummaryExport() bool {
	if x != nil {
		return x.SurveySummaryExport
	}
	return false
}

func (x *Permission) GetSurveyManageDelete() bool {
	if x != nil {
		return x.SurveyManageDelete
	}
	return false
}

func (x *Permission) GetMemberGet() bool {
	if x != nil {
		return x.MemberGet
	}
	return false
}

func (x *Permission) GetMemberAdd() bool {
	if x != nil {
		return x.MemberAdd
	}
	return false
}

func (x *Permission) GetMemberDelete() bool {
	if x != nil {
		return x.MemberDelete
	}
	return false
}

type JwtUserInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exp  uint32              `protobuf:"varint,1,opt,name=exp,proto3" json:"exp,omitempty"`
	Nbf  uint32              `protobuf:"varint,2,opt,name=nbf,proto3" json:"nbf,omitempty"`
	Iat  uint32              `protobuf:"varint,3,opt,name=iat,proto3" json:"iat,omitempty"`
	Iss  string              `protobuf:"bytes,4,opt,name=iss,proto3" json:"iss,omitempty"`
	Id   uint32              `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	Sub  string              `protobuf:"bytes,6,opt,name=sub,proto3" json:"sub,omitempty"`
	Data *JwtUserInfoV1_Data `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *JwtUserInfoV1) Reset() {
	*x = JwtUserInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JwtUserInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtUserInfoV1) ProtoMessage() {}

func (x *JwtUserInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtUserInfoV1.ProtoReflect.Descriptor instead.
func (*JwtUserInfoV1) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{6}
}

func (x *JwtUserInfoV1) GetExp() uint32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *JwtUserInfoV1) GetNbf() uint32 {
	if x != nil {
		return x.Nbf
	}
	return 0
}

func (x *JwtUserInfoV1) GetIat() uint32 {
	if x != nil {
		return x.Iat
	}
	return 0
}

func (x *JwtUserInfoV1) GetIss() string {
	if x != nil {
		return x.Iss
	}
	return ""
}

func (x *JwtUserInfoV1) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *JwtUserInfoV1) GetSub() string {
	if x != nil {
		return x.Sub
	}
	return ""
}

func (x *JwtUserInfoV1) GetData() *JwtUserInfoV1_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UserCheckRequest) Reset() {
	*x = UserCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckRequest) ProtoMessage() {}

func (x *UserCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckRequest.ProtoReflect.Descriptor instead.
func (*UserCheckRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{7}
}

func (x *UserCheckRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *UserCheckRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UserCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UserCheckResponse) Reset() {
	*x = UserCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckResponse) ProtoMessage() {}

func (x *UserCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckResponse.ProtoReflect.Descriptor instead.
func (*UserCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{8}
}

type ClearAllSurveyRecycleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *ClearAllSurveyRecycleRequest) Reset() {
	*x = ClearAllSurveyRecycleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearAllSurveyRecycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearAllSurveyRecycleRequest) ProtoMessage() {}

func (x *ClearAllSurveyRecycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearAllSurveyRecycleRequest.ProtoReflect.Descriptor instead.
func (*ClearAllSurveyRecycleRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{9}
}

func (x *ClearAllSurveyRecycleRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type CreateSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Settings string `protobuf:"bytes,3,opt,name=settings,proto3" json:"settings,omitempty"`
	Stime    string `protobuf:"bytes,4,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime    string `protobuf:"bytes,5,opt,name=etime,proto3" json:"etime,omitempty"`
}

func (x *CreateSurveyRequest) Reset() {
	*x = CreateSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSurveyRequest) ProtoMessage() {}

func (x *CreateSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSurveyRequest.ProtoReflect.Descriptor instead.
func (*CreateSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{10}
}

func (x *CreateSurveyRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *CreateSurveyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSurveyRequest) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *CreateSurveyRequest) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *CreateSurveyRequest) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

type StatisticsUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,6,opt,name=id,proto3" json:"id,omitempty"`
	ClientId int64  `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Settings string `protobuf:"bytes,3,opt,name=settings,proto3" json:"settings,omitempty"` // 可以只修改name
	Stime    string `protobuf:"bytes,4,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime    string `protobuf:"bytes,5,opt,name=etime,proto3" json:"etime,omitempty"`
}

func (x *StatisticsUpdateRequest) Reset() {
	*x = StatisticsUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticsUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticsUpdateRequest) ProtoMessage() {}

func (x *StatisticsUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticsUpdateRequest.ProtoReflect.Descriptor instead.
func (*StatisticsUpdateRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{11}
}

func (x *StatisticsUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StatisticsUpdateRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *StatisticsUpdateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StatisticsUpdateRequest) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *StatisticsUpdateRequest) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *StatisticsUpdateRequest) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

type SurveyStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Id       int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SurveyStatisticsRequest) Reset() {
	*x = SurveyStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyStatisticsRequest) ProtoMessage() {}

func (x *SurveyStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyStatisticsRequest.ProtoReflect.Descriptor instead.
func (*SurveyStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{12}
}

func (x *SurveyStatisticsRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyStatisticsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SurveyStatisticsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Settings string `protobuf:"bytes,3,opt,name=settings,proto3" json:"settings,omitempty"`
	Stime    string `protobuf:"bytes,4,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime    string `protobuf:"bytes,5,opt,name=etime,proto3" json:"etime,omitempty"`
}

func (x *SurveyStatisticsResponse) Reset() {
	*x = SurveyStatisticsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyStatisticsResponse) ProtoMessage() {}

func (x *SurveyStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyStatisticsResponse.ProtoReflect.Descriptor instead.
func (*SurveyStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{13}
}

func (x *SurveyStatisticsResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyStatisticsResponse) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *SurveyStatisticsResponse) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *SurveyStatisticsResponse) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

type GetLatestSurveyBySurveyIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *GetLatestSurveyBySurveyIdRequest) Reset() {
	*x = GetLatestSurveyBySurveyIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestSurveyBySurveyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestSurveyBySurveyIdRequest) ProtoMessage() {}

func (x *GetLatestSurveyBySurveyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestSurveyBySurveyIdRequest.ProtoReflect.Descriptor instead.
func (*GetLatestSurveyBySurveyIdRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{14}
}

func (x *GetLatestSurveyBySurveyIdRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *GetLatestSurveyBySurveyIdRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type GetLatestSurveyBySurveyIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SurveyId    int64  `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Schema      string `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	Settings    string `protobuf:"bytes,5,opt,name=settings,proto3" json:"settings,omitempty"`
	WebSettings string `protobuf:"bytes,6,opt,name=web_settings,json=webSettings,proto3" json:"web_settings,omitempty"`
}

func (x *GetLatestSurveyBySurveyIdResponse) Reset() {
	*x = GetLatestSurveyBySurveyIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestSurveyBySurveyIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestSurveyBySurveyIdResponse) ProtoMessage() {}

func (x *GetLatestSurveyBySurveyIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestSurveyBySurveyIdResponse.ProtoReflect.Descriptor instead.
func (*GetLatestSurveyBySurveyIdResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{15}
}

func (x *GetLatestSurveyBySurveyIdResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetLatestSurveyBySurveyIdResponse) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *GetLatestSurveyBySurveyIdResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetLatestSurveyBySurveyIdResponse) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *GetLatestSurveyBySurveyIdResponse) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *GetLatestSurveyBySurveyIdResponse) GetWebSettings() string {
	if x != nil {
		return x.WebSettings
	}
	return ""
}

type UploadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
}

func (x *UploadRequest) Reset() {
	*x = UploadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadRequest) ProtoMessage() {}

func (x *UploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadRequest.ProtoReflect.Descriptor instead.
func (*UploadRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{16}
}

func (x *UploadRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

type UploadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *UploadResponse) Reset() {
	*x = UploadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadResponse) ProtoMessage() {}

func (x *UploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadResponse.ProtoReflect.Descriptor instead.
func (*UploadResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{17}
}

func (x *UploadResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type JwtUserInfoV1_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      uint32 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PopsUserId  string `protobuf:"bytes,2,opt,name=pops_user_id,json=popsUserId,proto3" json:"pops_user_id,omitempty"`
	Username    string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname    string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Email       string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	IsSuperuser bool   `protobuf:"varint,6,opt,name=is_superuser,json=isSuperuser,proto3" json:"is_superuser,omitempty"`
}

func (x *JwtUserInfoV1_Data) Reset() {
	*x = JwtUserInfoV1_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JwtUserInfoV1_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtUserInfoV1_Data) ProtoMessage() {}

func (x *JwtUserInfoV1_Data) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtUserInfoV1_Data.ProtoReflect.Descriptor instead.
func (*JwtUserInfoV1_Data) Descriptor() ([]byte, []int) {
	return file_proto_survey_proto_rawDescGZIP(), []int{6, 0}
}

func (x *JwtUserInfoV1_Data) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *JwtUserInfoV1_Data) GetPopsUserId() string {
	if x != nil {
		return x.PopsUserId
	}
	return ""
}

func (x *JwtUserInfoV1_Data) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *JwtUserInfoV1_Data) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *JwtUserInfoV1_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *JwtUserInfoV1_Data) GetIsSuperuser() bool {
	if x != nil {
		return x.IsSuperuser
	}
	return false
}

var File_proto_survey_proto protoreflect.FileDescriptor

var file_proto_survey_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1a,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x0f, 0x0a, 0x0d, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x31, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xe1, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x69,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a,
	0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xad, 0x01, 0x0a, 0x08, 0x55, 0x73,
	0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x0a, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd8,
	0x02, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a,
	0x11, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x67,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x47, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x41, 0x64, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x45, 0x64,
	0x69, 0x74, 0x12, 0x31, 0x0a, 0x14, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x13, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x47, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0xf0, 0x02, 0x0a, 0x0d, 0x4a, 0x77,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x78, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x62, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x62, 0x66, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x61,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x75, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x73, 0x75, 0x62, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4a,
	0x77, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xb2, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x6f, 0x70,
	0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x6f, 0x70, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x73, 0x75, 0x70, 0x65, 0x72, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x75, 0x73, 0x65, 0x72, 0x22, 0x43, 0x0a, 0x10,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x13, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x41, 0x0a, 0x1c, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41,
	0x6c, 0x6c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x13, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xc0, 0x01, 0x0a,
	0x17, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47, 0x09, 0x69, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2,
	0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0x46, 0x0a, 0x17, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x68, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42,
	0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0xeb, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x79, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47,
	0x09, 0x69, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x27, 0x0a, 0x0c, 0x77,
	0x65, 0x62, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x22, 0x23, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x22, 0x0a, 0x0e, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x32, 0xe4, 0x58,
	0x0a, 0x0d, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0xa6, 0x01, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x27, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x5c, 0xba, 0x47, 0x40, 0x2a,
	0x0c, 0xe5, 0x81, 0xa5, 0xe5, 0xba, 0xb7, 0xe6, 0xa3, 0x80, 0xe6, 0x9f, 0xa5, 0x6a, 0x0d, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x70, 0x30, 0x6a, 0x21, 0x0a, 0x0f,
	0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12,
	0x0e, 0x12, 0x0c, 0xe5, 0x9f, 0xba, 0xe7, 0xa1, 0x80, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x97, 0x02, 0x0a, 0x14, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x35, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x8f, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40, 0x2a,
	0x12, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02,
	0x50, 0x32, 0x6a, 0x1b, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x08, 0x12, 0x06, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x3d, 0x12, 0x3b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xf0, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x92, 0x01, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a, 0x12, 0xe5,
	0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32,
	0x6a, 0x1b, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x12, 0x08, 0x12, 0x06, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x42, 0x3a, 0x01, 0x2a, 0x22, 0x3d, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0xf0, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x8f, 0x01, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e,
	0xba, 0x47, 0x40, 0x2a, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe5, 0xaf, 0xbc, 0xe5, 0x87,
	0xba, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1b, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66,
	0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x08, 0x12, 0x06, 0xe5, 0xaf, 0xbc,
	0xe5, 0x87, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x3a, 0x01, 0x2a, 0x22, 0x3a, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x65, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x2f, 0x64, 0x65, 0x6c, 0x12, 0x84, 0x02, 0x0a, 0x1b, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x97, 0x01, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba,
	0x47, 0x40, 0x2a, 0x12, 0xe9, 0x87, 0x8d, 0xe7, 0xbd, 0xae, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1b, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f,
	0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x08, 0x12, 0x06, 0xe5, 0xaf, 0xbc, 0xe5,
	0x87, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x47, 0x3a, 0x01, 0x2a, 0x22, 0x42, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x65, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x2f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x88, 0x02, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x01,
	0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x46, 0x2a, 0x12, 0xe5, 0x9b,
	0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a,
	0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x3a, 0x01, 0x2a, 0x22, 0x2d, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xf5, 0x01, 0x0a, 0x12, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x32, 0x12, 0x33, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x8d, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47,
	0x46, 0x2a, 0x12, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04,
	0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78,
	0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d,
	0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x3a, 0x01, 0x2a,
	0x22, 0x30, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2d,
	0x76, 0x32, 0x12, 0xed, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x34, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x8c, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba,
	0x47, 0x46, 0x2a, 0x12, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f,
	0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5,
	0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x3a, 0x01,
	0x2a, 0x22, 0x2f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0xf5, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x95, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47,
	0x4c, 0x2a, 0x18, 0xe6, 0xa0, 0x87, 0xe8, 0xae, 0xb0, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe8,
	0xae, 0xb0, 0xe5, 0xbd, 0x95, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0x6a, 0x0d, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d,
	0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12,
	0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a, 0x22, 0x32, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x2f, 0x73, 0x65, 0x74, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x80, 0x02, 0x0a, 0x16, 0x53,
	0x65, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x35, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x97, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba,
	0x47, 0x4c, 0x2a, 0x18, 0xe6, 0xa0, 0x87, 0xe8, 0xae, 0xb0, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7,
	0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0x6a, 0x0d, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78,
	0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e,
	0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x39, 0x3a, 0x01, 0x2a, 0x22, 0x34, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x2f, 0x73, 0x65, 0x74, 0x2d, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x92, 0x02,
	0x0a, 0x17, 0x49, 0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x8f, 0x01, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x46, 0x2a,
	0x12, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02,
	0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x12, 0x35, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x2d, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xc5, 0x01, 0x0a, 0x0d, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x2a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71,
	0x1a, 0x20, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x22, 0x66, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40,
	0x2a, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe9, 0xa2, 0x84, 0xe8, 0xa7, 0x88, 0x6a, 0x0d,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a,
	0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x86, 0x02, 0x0a, 0x12, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x34, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x22, 0x83, 0x01,
	0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe6, 0x9f,
	0xa5, 0xe7, 0x9c, 0x8b, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61,
	0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c,
	0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x31, 0x12, 0x2f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x2d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0xcc, 0x01, 0x0a, 0x0a, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x63, 0xda,
	0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe9, 0x97, 0xae,
	0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70,
	0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7,
	0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xcf, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x66, 0xda, 0x41,
	0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f,
	0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5,
	0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01,
	0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0xe5, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x6a, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x99, 0x6a, 0x0d, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78,
	0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e,
	0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xe8, 0x01, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6e, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe8, 0xa7, 0x84,
	0xe5, 0x88, 0x99, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02,
	0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xc0, 0x01, 0x0a, 0x0a, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x22, 0x63, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0xb1, 0x95, 0xe7, 0xa4,
	0xba, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32,
	0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x68, 0x6f, 0x77, 0x12, 0xc2, 0x01, 0x0a, 0x0c, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x2a, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x6f,
	0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe9, 0x97, 0xae, 0xe5,
	0x8d, 0xb7, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69,
	0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad,
	0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f,
	0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x2d, 0x64, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0xc5, 0x01, 0x0a, 0x0a, 0x43, 0x6f, 0x70, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x27,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x64, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe5,
	0xa4, 0x8d, 0xe5, 0x88, 0xb6, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a, 0x0d, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d,
	0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12,
	0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2f, 0x63, 0x6f, 0x70, 0x79, 0x12, 0xb8, 0x01, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x66, 0xda, 0x41, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f,
	0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5,
	0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01,
	0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x79,
	0x6e, 0x63, 0x12, 0xbb, 0x01, 0x0a, 0x09, 0x49, 0x6d, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x12, 0x2a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x49, 0x6d, 0x70, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x6b, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47,
	0x46, 0x2a, 0x12, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0x90, 0x8c, 0xe6, 0xad, 0xa5, 0xe5,
	0x86, 0x99, 0xe5, 0x85, 0xa5, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04,
	0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78,
	0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d,
	0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a,
	0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x69, 0x6d, 0x70,
	0x12, 0xd3, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x12, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x77, 0xda,
	0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x4d, 0x2a, 0x19, 0xe9, 0x97, 0xae, 0xe5, 0x8d,
	0xb7, 0xe6, 0x9a, 0x82, 0xe5, 0x81, 0x9c, 0x2f, 0xe5, 0xbc, 0x80, 0xe5, 0x90, 0xaf, 0xe4, 0xbd,
	0x9c, 0xe7, 0xad, 0x94, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12,
	0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe7, 0xad, 0x94, 0xe5, 0x8d, 0xb7,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22,
	0x15, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x65, 0x74, 0x2d,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xd8, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x68, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0xba, 0x47, 0x3d, 0x2a, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02,
	0x50, 0x32, 0x6a, 0x1e, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x12, 0x09, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7,
	0xab, 0x99, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2d, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0xbd, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x2a, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x63, 0xda, 0x41,
	0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x3d, 0x2a, 0x0c, 0xe5, 0xbd, 0xbb, 0xe5, 0xba, 0x95,
	0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1e, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f,
	0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x12, 0x09, 0xe5, 0x9b, 0x9e, 0xe6,
	0x94, 0xb6, 0xe7, 0xab, 0x99, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0xd6, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x74, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x3d,
	0x2a, 0x0c, 0xe6, 0x81, 0xa2, 0xe5, 0xa4, 0x8d, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a, 0x0d,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1e, 0x0a,
	0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x12, 0x0b, 0x12, 0x09, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xab, 0x99, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x2d, 0x6c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x2d, 0x64, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0xdd, 0x01, 0x0a, 0x15, 0x43,
	0x6c, 0x65, 0x61, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x12, 0x36, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x75, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a,
	0x0f, 0xe6, 0xb8, 0x85, 0xe7, 0xa9, 0xba, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xab, 0x99,
	0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a,
	0x1e, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x12, 0x0b, 0x12, 0x09, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xab, 0x99, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x2d, 0x6c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x2d, 0x64, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0xd2, 0x01, 0x0a, 0x17, 0x52,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x20, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x7e, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x43, 0x2a, 0x12, 0xe6, 0x81, 0xa2,
	0xe5, 0xa4, 0x8d, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a,
	0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1e,
	0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x12, 0x0b, 0x12, 0x09, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe7, 0xab, 0x99, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x2d, 0x61, 0x6c, 0x6c, 0x2d,
	0x6c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x2d, 0x64, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0xec, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2f, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x77, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0xba, 0x47, 0x40, 0x2a, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x8a, 0x95, 0xe6, 0x94,
	0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x1b, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66,
	0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x08, 0x12, 0x06, 0xe6, 0x8a, 0x95,
	0xe6, 0x94, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x67, 0x65,
	0x74, 0x2d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x2d, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xdd,
	0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x5a, 0x6f, 0x6e,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x71, 0xda, 0x41, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0xba, 0x47, 0x3d, 0x2a, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe5, 0x8c, 0xba, 0xe6, 0x9c, 0x8d, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x6a, 0x0d, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x18, 0x0a, 0x0f,
	0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12,
	0x05, 0x12, 0x03, 0x62, 0x69, 0x7a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c,
	0x2f, 0x67, 0x65, 0x74, 0x2d, 0x7a, 0x6f, 0x6e, 0x65, 0x2d, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xbc,
	0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12,
	0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x66, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47,
	0x40, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x6a,
	0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21,
	0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xc1, 0x01,
	0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12,
	0x2e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x69, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe9, 0x97, 0xae, 0xe5,
	0x8d, 0xb7, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50,
	0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x12, 0xee, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x83, 0x01, 0xda,
	0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x40, 0x2a, 0x0c, 0xe5, 0x9b, 0x9e, 0xe6, 0x94,
	0xb6, 0xe6, 0xa6, 0x82, 0xe5, 0x86, 0xb5, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70, 0x69, 0x66,
	0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe9, 0x97, 0xae,
	0xe5, 0x8d, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x3a,
	0x01, 0x2a, 0x22, 0x2e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x2f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x2d, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2d, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xdd, 0x01, 0x0a, 0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x7e, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47, 0x4d, 0x2a, 0x19,
	0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe6, 0xa6, 0x82, 0xe5, 0x86, 0xb5, 0x2d, 0xe9, 0x97, 0xae,
	0xe5, 0x8d, 0xb7, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0x6a, 0x0d, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78, 0x2d, 0x61, 0x70,
	0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x12, 0x0c, 0xe9,
	0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0xe1, 0x01, 0x0a, 0x19, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x6c, 0x64,
	0x12, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7f, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0xba, 0x47,
	0x4d, 0x2a, 0x19, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xb6, 0xe6, 0xa6, 0x82, 0xe5, 0x86, 0xb5, 0x2d,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0x6a, 0x0d, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x04, 0x12, 0x02, 0x50, 0x32, 0x6a, 0x21, 0x0a, 0x0f, 0x78,
	0x2d, 0x61, 0x70, 0x69, 0x66, 0x6f, 0x78, 0x2d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x0e,
	0x12, 0x0c, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x32, 0x12, 0x96, 0x01, 0x0a, 0x0b, 0x67, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2a, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x12,
	0x8f, 0x01, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2a, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x28, 0xda, 0x41, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a,
	0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x72,
	0x75, 0x6c, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xd0, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x79,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x3a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x42, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42,
	0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x3a, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2b, 0x12, 0x29, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67,
	0x65, 0x74, 0x2d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x2d, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2d, 0x62, 0x79, 0x2d, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2d, 0x69, 0x64, 0x12, 0xc6, 0x01,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x41, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x32, 0x12, 0x30, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x67, 0x65, 0x74, 0x2d, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x2d, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2d, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xc2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x50, 0x72,
	0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x40, 0xda, 0x41, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2f,
	0x67, 0x65, 0x74, 0x2d, 0x70, 0x72, 0x65, 0x2d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x2d, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x87, 0x01, 0x0a, 0x06,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0xda, 0x41, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x56, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x2a, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x0e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56,
	0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x22, 0x25, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x7b, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69,
	0x65, 0x77, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2a, 0xda, 0x41, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x56, 0x69, 0x65, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x2a, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x87, 0x01, 0x0a,
	0x11, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x12, 0x2e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0xda, 0x41, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x9e, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2b, 0xda, 0x41, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x87, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x98, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x22, 0x29, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x91, 0x01, 0x0a,
	0x14, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x2f, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20,
	0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x8d, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x2d, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x2f, 0x73, 0x65, 0x6e, 0x64,
	0x12, 0x8d, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x2d, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x2f, 0x73, 0x79, 0x6e, 0x63,
	0x12, 0x9d, 0x01, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x35, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x33, 0xda, 0x41, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22,
	0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x2f, 0x73, 0x65, 0x6e, 0x64,
	0x12, 0x9d, 0x01, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x35, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x33, 0xda, 0x41, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22,
	0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x2f, 0x73, 0x79, 0x6e, 0x63,
	0x12, 0xad, 0x01, 0x0a, 0x1d, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x12, 0x3a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x39, 0xda, 0x41, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2d, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x12, 0xa8, 0x01, 0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x22, 0x2d, 0xda, 0x41,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a,
	0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x65, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x17, 0xca, 0x41, 0x14,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_proto_rawDescOnce sync.Once
	file_proto_survey_proto_rawDescData = file_proto_survey_proto_rawDesc
)

func file_proto_survey_proto_rawDescGZIP() []byte {
	file_proto_survey_proto_rawDescOnce.Do(func() {
		file_proto_survey_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_proto_rawDescData)
	})
	return file_proto_survey_proto_rawDescData
}

var file_proto_survey_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_proto_survey_proto_goTypes = []any{
	(*HealthRequest)(nil),                     // 0: papegames.sparrow.survey.HealthRequest
	(*GetUserInfoRequest)(nil),                // 1: papegames.sparrow.survey.GetUserInfoRequest
	(*GetUserInfoResponse)(nil),               // 2: papegames.sparrow.survey.GetUserInfoResponse
	(*Userinfo)(nil),                          // 3: papegames.sparrow.survey.Userinfo
	(*Clientlist)(nil),                        // 4: papegames.sparrow.survey.Clientlist
	(*Permission)(nil),                        // 5: papegames.sparrow.survey.Permission
	(*JwtUserInfoV1)(nil),                     // 6: papegames.sparrow.survey.JwtUserInfoV1
	(*UserCheckRequest)(nil),                  // 7: papegames.sparrow.survey.UserCheckRequest
	(*UserCheckResponse)(nil),                 // 8: papegames.sparrow.survey.UserCheckResponse
	(*ClearAllSurveyRecycleRequest)(nil),      // 9: papegames.sparrow.survey.ClearAllSurveyRecycleRequest
	(*CreateSurveyRequest)(nil),               // 10: papegames.sparrow.survey.CreateSurveyRequest
	(*StatisticsUpdateRequest)(nil),           // 11: papegames.sparrow.survey.StatisticsUpdateRequest
	(*SurveyStatisticsRequest)(nil),           // 12: papegames.sparrow.survey.SurveyStatisticsRequest
	(*SurveyStatisticsResponse)(nil),          // 13: papegames.sparrow.survey.SurveyStatisticsResponse
	(*GetLatestSurveyBySurveyIdRequest)(nil),  // 14: papegames.sparrow.survey.GetLatestSurveyBySurveyIdRequest
	(*GetLatestSurveyBySurveyIdResponse)(nil), // 15: papegames.sparrow.survey.GetLatestSurveyBySurveyIdResponse
	(*UploadRequest)(nil),                     // 16: papegames.sparrow.survey.UploadRequest
	(*UploadResponse)(nil),                    // 17: papegames.sparrow.survey.UploadResponse
	(*JwtUserInfoV1_Data)(nil),                // 18: papegames.sparrow.survey.JwtUserInfoV1.Data
	(*SurveyExportTaskListRequest)(nil),       // 19: papegames.sparrow.survey.SurveyExportTaskListRequest
	(*SurveyExportTask)(nil),                  // 20: papegames.sparrow.survey.SurveyExportTask
	(*DelSurveyExportTaskReq)(nil),            // 21: papegames.sparrow.survey.DelSurveyExportTaskReq
	(*SurveyExportTaskDetailsReq)(nil),        // 22: papegames.sparrow.survey.SurveyExportTaskDetailsReq
	(*SurveyRecordListV2Request)(nil),         // 23: papegames.sparrow.survey.SurveyRecordListV2Request
	(*SurveyRecordDetailsRequest)(nil),        // 24: papegames.sparrow.survey.SurveyRecordDetailsRequest
	(*SurveyRecordsRequest)(nil),              // 25: papegames.sparrow.survey.SurveyRecordsRequest
	(*SetValidSurveyRecordRequest)(nil),       // 26: papegames.sparrow.survey.SetValidSurveyRecordRequest
	(*SurveyRecordListRequest)(nil),           // 27: papegames.sparrow.survey.SurveyRecordListRequest
	(*SurveyPreviewReq)(nil),                  // 28: papegames.sparrow.survey.SurveyPreviewReq
	(*SurveyRecordConfDetailsReq)(nil),        // 29: papegames.sparrow.survey.SurveyRecordConfDetailsReq
	(*SurveyListRequest)(nil),                 // 30: papegames.sparrow.survey.SurveyListRequest
	(*SurveyDetailRequest)(nil),               // 31: papegames.sparrow.survey.SurveyDetailRequest
	(*ShowSurveyRequest)(nil),                 // 32: papegames.sparrow.survey.ShowSurveyRequest
	(*SurveyDelRequest)(nil),                  // 33: papegames.sparrow.survey.SurveyDelRequest
	(*SurveyRequest)(nil),                     // 34: papegames.sparrow.survey.SurveyRequest
	(*SyncSurveyRequest)(nil),                 // 35: papegames.sparrow.survey.SyncSurveyRequest
	(*ImpSurveyRequest)(nil),                  // 36: papegames.sparrow.survey.ImpSurveyRequest
	(*SurveySetStatusRequest)(nil),            // 37: papegames.sparrow.survey.SurveySetStatusRequest
	(*RecoverSurveyRecycleReq)(nil),           // 38: papegames.sparrow.survey.RecoverSurveyRecycleReq
	(*Survey)(nil),                            // 39: papegames.sparrow.survey.Survey
	(*GetDeliverListRequest)(nil),             // 40: papegames.sparrow.survey.GetDeliverListRequest
	(*GetZoneListRequest)(nil),                // 41: papegames.sparrow.survey.GetZoneListRequest
	(*UpdateSurveyRequest)(nil),               // 42: papegames.sparrow.survey.UpdateSurveyRequest
	(*PublishSurveyRequest)(nil),              // 43: papegames.sparrow.survey.PublishSurveyRequest
	(*SurveyInputMethodListRequest)(nil),      // 44: papegames.sparrow.survey.SurveyInputMethodListRequest
	(*ValidRedeemConfigRequest)(nil),          // 45: papegames.sparrow.survey.ValidRedeemConfigRequest
	(*PreAwardTemplateRequest)(nil),           // 46: papegames.sparrow.survey.PreAwardTemplateRequest
	(*SurveyViewCreateReq)(nil),               // 47: papegames.sparrow.survey.SurveyViewCreateReq
	(*SurveyViewListReq)(nil),                 // 48: papegames.sparrow.survey.SurveyViewListReq
	(*SurveyView)(nil),                        // 49: papegames.sparrow.survey.SurveyView
	(*SurveyViewDeleteReq)(nil),               // 50: papegames.sparrow.survey.SurveyViewDeleteReq
	(*SurveyGroupCreateReq)(nil),              // 51: papegames.sparrow.survey.SurveyGroupCreateReq
	(*SurveyGroupDetailReq)(nil),              // 52: papegames.sparrow.survey.SurveyGroupDetailReq
	(*SurveyGroupUpdateReq)(nil),              // 53: papegames.sparrow.survey.SurveyGroupUpdateReq
	(*SurveyGroupListReq)(nil),                // 54: papegames.sparrow.survey.SurveyGroupListReq
	(*SurveyGroupSubUpdateReq)(nil),           // 55: papegames.sparrow.survey.SurveyGroupSubUpdateReq
	(*SurveyOverwriteSendReq)(nil),            // 56: papegames.sparrow.survey.SurveyOverwriteSendReq
	(*SurveyOverwriteSyncReq)(nil),            // 57: papegames.sparrow.survey.SurveyOverwriteSyncReq
	(*SurveyGroupOverwriteSendReq)(nil),       // 58: papegames.sparrow.survey.SurveyGroupOverwriteSendReq
	(*SurveyGroupOverwriteSyncReq)(nil),       // 59: papegames.sparrow.survey.SurveyGroupOverwriteSyncReq
	(*SurveyExportUserClusterSubmitReq)(nil),  // 60: papegames.sparrow.survey.SurveyExportUserClusterSubmitReq
	(*SurveyExportHeadersReq)(nil),            // 61: papegames.sparrow.survey.SurveyExportHeadersReq
	(*xtype.Empty)(nil),                       // 62: papegames.type.Empty
	(*SurveyExportTaskListResponse)(nil),      // 63: papegames.sparrow.survey.SurveyExportTaskListResponse
	(*SurveyRecordListResponse)(nil),          // 64: papegames.sparrow.survey.SurveyRecordListResponse
	(*xtype.RawMessage)(nil),                  // 65: papegames.type.RawMessage
	(*SurveyRecordConfDetailsRes)(nil),        // 66: papegames.sparrow.survey.SurveyRecordConfDetailsRes
	(*SurveyListResponse)(nil),                // 67: papegames.sparrow.survey.SurveyListResponse
	(*SurveyResponse)(nil),                    // 68: papegames.sparrow.survey.SurveyResponse
	(*SurveyDetailResponse)(nil),              // 69: papegames.sparrow.survey.SurveyDetailResponse
	(*GetDeliverListResponse)(nil),            // 70: papegames.sparrow.survey.GetDeliverListResponse
	(*GetZoneListResponse)(nil),               // 71: papegames.sparrow.survey.GetZoneListResponse
	(*ValidRedeemConfigResponse)(nil),         // 72: papegames.sparrow.survey.ValidRedeemConfigResponse
	(*PreAwardTemplateResponse)(nil),          // 73: papegames.sparrow.survey.PreAwardTemplateResponse
	(*SurveyViewCreateRes)(nil),               // 74: papegames.sparrow.survey.SurveyViewCreateRes
	(*SurveyViewListRes)(nil),                 // 75: papegames.sparrow.survey.SurveyViewListRes
	(*CmsSurveyGroupInfo)(nil),                // 76: papegames.sparrow.survey.CmsSurveyGroupInfo
	(*SurveyGroupListRes)(nil),                // 77: papegames.sparrow.survey.SurveyGroupListRes
	(*SurveyExportHeadersRes)(nil),            // 78: papegames.sparrow.survey.SurveyExportHeadersRes
}
var file_proto_survey_proto_depIdxs = []int32{
	3,  // 0: papegames.sparrow.survey.GetUserInfoResponse.userInfo:type_name -> papegames.sparrow.survey.Userinfo
	4,  // 1: papegames.sparrow.survey.GetUserInfoResponse.clientList:type_name -> papegames.sparrow.survey.Clientlist
	5,  // 2: papegames.sparrow.survey.GetUserInfoResponse.permission:type_name -> papegames.sparrow.survey.Permission
	18, // 3: papegames.sparrow.survey.JwtUserInfoV1.data:type_name -> papegames.sparrow.survey.JwtUserInfoV1.Data
	0,  // 4: papegames.sparrow.survey.SurveyService.Health:input_type -> papegames.sparrow.survey.HealthRequest
	19, // 5: papegames.sparrow.survey.SurveyService.SurveyExportTaskList:input_type -> papegames.sparrow.survey.SurveyExportTaskListRequest
	20, // 6: papegames.sparrow.survey.SurveyService.CreateSurveyExportTask:input_type -> papegames.sparrow.survey.SurveyExportTask
	21, // 7: papegames.sparrow.survey.SurveyService.DelSurveyExportTask:input_type -> papegames.sparrow.survey.DelSurveyExportTaskReq
	22, // 8: papegames.sparrow.survey.SurveyService.ResetSurveyExportTaskStatus:input_type -> papegames.sparrow.survey.SurveyExportTaskDetailsReq
	23, // 9: papegames.sparrow.survey.SurveyService.SurveyRecordList:input_type -> papegames.sparrow.survey.SurveyRecordListV2Request
	23, // 10: papegames.sparrow.survey.SurveyService.SurveyRecordListV2:input_type -> papegames.sparrow.survey.SurveyRecordListV2Request
	24, // 11: papegames.sparrow.survey.SurveyService.DelSurveyRecord:input_type -> papegames.sparrow.survey.SurveyRecordDetailsRequest
	25, // 12: papegames.sparrow.survey.SurveyService.SetValidSurveyRecord:input_type -> papegames.sparrow.survey.SurveyRecordsRequest
	26, // 13: papegames.sparrow.survey.SurveyService.SetInvalidSurveyRecord:input_type -> papegames.sparrow.survey.SetValidSurveyRecordRequest
	27, // 14: papegames.sparrow.survey.SurveyService.InValidSurveyRecordList:input_type -> papegames.sparrow.survey.SurveyRecordListRequest
	28, // 15: papegames.sparrow.survey.SurveyService.SurveyPreview:input_type -> papegames.sparrow.survey.SurveyPreviewReq
	29, // 16: papegames.sparrow.survey.SurveyService.SurveyRecordDetail:input_type -> papegames.sparrow.survey.SurveyRecordConfDetailsReq
	30, // 17: papegames.sparrow.survey.SurveyService.SurveyList:input_type -> papegames.sparrow.survey.SurveyListRequest
	10, // 18: papegames.sparrow.survey.SurveyService.CreateSurvey:input_type -> papegames.sparrow.survey.CreateSurveyRequest
	12, // 19: papegames.sparrow.survey.SurveyService.SurveyStatistics:input_type -> papegames.sparrow.survey.SurveyStatisticsRequest
	31, // 20: papegames.sparrow.survey.SurveyService.GetSurveyStatisticsList:input_type -> papegames.sparrow.survey.SurveyDetailRequest
	32, // 21: papegames.sparrow.survey.SurveyService.SurveyShow:input_type -> papegames.sparrow.survey.ShowSurveyRequest
	33, // 22: papegames.sparrow.survey.SurveyService.DeleteSurvey:input_type -> papegames.sparrow.survey.SurveyDelRequest
	34, // 23: papegames.sparrow.survey.SurveyService.CopySurvey:input_type -> papegames.sparrow.survey.SurveyRequest
	35, // 24: papegames.sparrow.survey.SurveyService.SyncSurvey:input_type -> papegames.sparrow.survey.SyncSurveyRequest
	36, // 25: papegames.sparrow.survey.SurveyService.ImpSurvey:input_type -> papegames.sparrow.survey.ImpSurveyRequest
	37, // 26: papegames.sparrow.survey.SurveyService.SetStatusSurvey:input_type -> papegames.sparrow.survey.SurveySetStatusRequest
	30, // 27: papegames.sparrow.survey.SurveyService.SurveyRecycleList:input_type -> papegames.sparrow.survey.SurveyListRequest
	33, // 28: papegames.sparrow.survey.SurveyService.DeleteSurveyRecycle:input_type -> papegames.sparrow.survey.SurveyDelRequest
	38, // 29: papegames.sparrow.survey.SurveyService.RecoverSurveyRecycle:input_type -> papegames.sparrow.survey.RecoverSurveyRecycleReq
	9,  // 30: papegames.sparrow.survey.SurveyService.ClearAllSurveyRecycle:input_type -> papegames.sparrow.survey.ClearAllSurveyRecycleRequest
	39, // 31: papegames.sparrow.survey.SurveyService.RecoverAllSurveyRecycle:input_type -> papegames.sparrow.survey.Survey
	40, // 32: papegames.sparrow.survey.SurveyService.GetDeliverList:input_type -> papegames.sparrow.survey.GetDeliverListRequest
	41, // 33: papegames.sparrow.survey.SurveyService.GetZoneList:input_type -> papegames.sparrow.survey.GetZoneListRequest
	42, // 34: papegames.sparrow.survey.SurveyService.UpdateSurvey:input_type -> papegames.sparrow.survey.UpdateSurveyRequest
	43, // 35: papegames.sparrow.survey.SurveyService.PublishSurvey:input_type -> papegames.sparrow.survey.PublishSurveyRequest
	44, // 36: papegames.sparrow.survey.SurveyService.GetInputMethodList:input_type -> papegames.sparrow.survey.SurveyInputMethodListRequest
	34, // 37: papegames.sparrow.survey.SurveyService.SurveyStatisticsDetail:input_type -> papegames.sparrow.survey.SurveyRequest
	34, // 38: papegames.sparrow.survey.SurveyService.SurveyStatisticsDetailOld:input_type -> papegames.sparrow.survey.SurveyRequest
	1,  // 39: papegames.sparrow.survey.SurveyService.getUserInfo:input_type -> papegames.sparrow.survey.GetUserInfoRequest
	7,  // 40: papegames.sparrow.survey.SurveyService.userCheck:input_type -> papegames.sparrow.survey.UserCheckRequest
	11, // 41: papegames.sparrow.survey.SurveyService.StatisticsUpdate:input_type -> papegames.sparrow.survey.StatisticsUpdateRequest
	14, // 42: papegames.sparrow.survey.SurveyService.GetLatestSurveyBySurveyId:input_type -> papegames.sparrow.survey.GetLatestSurveyBySurveyIdRequest
	45, // 43: papegames.sparrow.survey.SurveyService.GetValidRedeemConfigList:input_type -> papegames.sparrow.survey.ValidRedeemConfigRequest
	46, // 44: papegames.sparrow.survey.SurveyService.GetPreAwardTemplateList:input_type -> papegames.sparrow.survey.PreAwardTemplateRequest
	16, // 45: papegames.sparrow.survey.SurveyService.Upload:input_type -> papegames.sparrow.survey.UploadRequest
	47, // 46: papegames.sparrow.survey.SurveyService.SurveyViewCreate:input_type -> papegames.sparrow.survey.SurveyViewCreateReq
	48, // 47: papegames.sparrow.survey.SurveyService.SurveyViewList:input_type -> papegames.sparrow.survey.SurveyViewListReq
	49, // 48: papegames.sparrow.survey.SurveyService.SurveyViewUpdate:input_type -> papegames.sparrow.survey.SurveyView
	50, // 49: papegames.sparrow.survey.SurveyService.SurveyViewDelete:input_type -> papegames.sparrow.survey.SurveyViewDeleteReq
	51, // 50: papegames.sparrow.survey.SurveyService.SurveyGroupCreate:input_type -> papegames.sparrow.survey.SurveyGroupCreateReq
	52, // 51: papegames.sparrow.survey.SurveyService.SurveyGroupDetail:input_type -> papegames.sparrow.survey.SurveyGroupDetailReq
	53, // 52: papegames.sparrow.survey.SurveyService.SurveyGroupUpdate:input_type -> papegames.sparrow.survey.SurveyGroupUpdateReq
	54, // 53: papegames.sparrow.survey.SurveyService.SurveyGroupList:input_type -> papegames.sparrow.survey.SurveyGroupListReq
	55, // 54: papegames.sparrow.survey.SurveyService.SurveyGroupSubUpdate:input_type -> papegames.sparrow.survey.SurveyGroupSubUpdateReq
	56, // 55: papegames.sparrow.survey.SurveyService.SurveyOverwriteSend:input_type -> papegames.sparrow.survey.SurveyOverwriteSendReq
	57, // 56: papegames.sparrow.survey.SurveyService.SurveyOverwriteSync:input_type -> papegames.sparrow.survey.SurveyOverwriteSyncReq
	58, // 57: papegames.sparrow.survey.SurveyService.SurveyGroupOverwriteSend:input_type -> papegames.sparrow.survey.SurveyGroupOverwriteSendReq
	59, // 58: papegames.sparrow.survey.SurveyService.SurveyGroupOverwriteSync:input_type -> papegames.sparrow.survey.SurveyGroupOverwriteSyncReq
	60, // 59: papegames.sparrow.survey.SurveyService.SurveyExportUserClusterSubmit:input_type -> papegames.sparrow.survey.SurveyExportUserClusterSubmitReq
	61, // 60: papegames.sparrow.survey.SurveyService.SurveyExportHeaders:input_type -> papegames.sparrow.survey.SurveyExportHeadersReq
	62, // 61: papegames.sparrow.survey.SurveyService.Health:output_type -> papegames.type.Empty
	63, // 62: papegames.sparrow.survey.SurveyService.SurveyExportTaskList:output_type -> papegames.sparrow.survey.SurveyExportTaskListResponse
	62, // 63: papegames.sparrow.survey.SurveyService.CreateSurveyExportTask:output_type -> papegames.type.Empty
	62, // 64: papegames.sparrow.survey.SurveyService.DelSurveyExportTask:output_type -> papegames.type.Empty
	62, // 65: papegames.sparrow.survey.SurveyService.ResetSurveyExportTaskStatus:output_type -> papegames.type.Empty
	64, // 66: papegames.sparrow.survey.SurveyService.SurveyRecordList:output_type -> papegames.sparrow.survey.SurveyRecordListResponse
	65, // 67: papegames.sparrow.survey.SurveyService.SurveyRecordListV2:output_type -> papegames.type.RawMessage
	62, // 68: papegames.sparrow.survey.SurveyService.DelSurveyRecord:output_type -> papegames.type.Empty
	62, // 69: papegames.sparrow.survey.SurveyService.SetValidSurveyRecord:output_type -> papegames.type.Empty
	62, // 70: papegames.sparrow.survey.SurveyService.SetInvalidSurveyRecord:output_type -> papegames.type.Empty
	64, // 71: papegames.sparrow.survey.SurveyService.InValidSurveyRecordList:output_type -> papegames.sparrow.survey.SurveyRecordListResponse
	39, // 72: papegames.sparrow.survey.SurveyService.SurveyPreview:output_type -> papegames.sparrow.survey.Survey
	66, // 73: papegames.sparrow.survey.SurveyService.SurveyRecordDetail:output_type -> papegames.sparrow.survey.SurveyRecordConfDetailsRes
	67, // 74: papegames.sparrow.survey.SurveyService.SurveyList:output_type -> papegames.sparrow.survey.SurveyListResponse
	68, // 75: papegames.sparrow.survey.SurveyService.CreateSurvey:output_type -> papegames.sparrow.survey.SurveyResponse
	13, // 76: papegames.sparrow.survey.SurveyService.SurveyStatistics:output_type -> papegames.sparrow.survey.SurveyStatisticsResponse
	69, // 77: papegames.sparrow.survey.SurveyService.GetSurveyStatisticsList:output_type -> papegames.sparrow.survey.SurveyDetailResponse
	39, // 78: papegames.sparrow.survey.SurveyService.SurveyShow:output_type -> papegames.sparrow.survey.Survey
	62, // 79: papegames.sparrow.survey.SurveyService.DeleteSurvey:output_type -> papegames.type.Empty
	68, // 80: papegames.sparrow.survey.SurveyService.CopySurvey:output_type -> papegames.sparrow.survey.SurveyResponse
	62, // 81: papegames.sparrow.survey.SurveyService.SyncSurvey:output_type -> papegames.type.Empty
	62, // 82: papegames.sparrow.survey.SurveyService.ImpSurvey:output_type -> papegames.type.Empty
	62, // 83: papegames.sparrow.survey.SurveyService.SetStatusSurvey:output_type -> papegames.type.Empty
	67, // 84: papegames.sparrow.survey.SurveyService.SurveyRecycleList:output_type -> papegames.sparrow.survey.SurveyListResponse
	62, // 85: papegames.sparrow.survey.SurveyService.DeleteSurveyRecycle:output_type -> papegames.type.Empty
	62, // 86: papegames.sparrow.survey.SurveyService.RecoverSurveyRecycle:output_type -> papegames.type.Empty
	62, // 87: papegames.sparrow.survey.SurveyService.ClearAllSurveyRecycle:output_type -> papegames.type.Empty
	62, // 88: papegames.sparrow.survey.SurveyService.RecoverAllSurveyRecycle:output_type -> papegames.type.Empty
	70, // 89: papegames.sparrow.survey.SurveyService.GetDeliverList:output_type -> papegames.sparrow.survey.GetDeliverListResponse
	71, // 90: papegames.sparrow.survey.SurveyService.GetZoneList:output_type -> papegames.sparrow.survey.GetZoneListResponse
	62, // 91: papegames.sparrow.survey.SurveyService.UpdateSurvey:output_type -> papegames.type.Empty
	62, // 92: papegames.sparrow.survey.SurveyService.PublishSurvey:output_type -> papegames.type.Empty
	65, // 93: papegames.sparrow.survey.SurveyService.GetInputMethodList:output_type -> papegames.type.RawMessage
	65, // 94: papegames.sparrow.survey.SurveyService.SurveyStatisticsDetail:output_type -> papegames.type.RawMessage
	65, // 95: papegames.sparrow.survey.SurveyService.SurveyStatisticsDetailOld:output_type -> papegames.type.RawMessage
	2,  // 96: papegames.sparrow.survey.SurveyService.getUserInfo:output_type -> papegames.sparrow.survey.GetUserInfoResponse
	8,  // 97: papegames.sparrow.survey.SurveyService.userCheck:output_type -> papegames.sparrow.survey.UserCheckResponse
	62, // 98: papegames.sparrow.survey.SurveyService.StatisticsUpdate:output_type -> papegames.type.Empty
	15, // 99: papegames.sparrow.survey.SurveyService.GetLatestSurveyBySurveyId:output_type -> papegames.sparrow.survey.GetLatestSurveyBySurveyIdResponse
	72, // 100: papegames.sparrow.survey.SurveyService.GetValidRedeemConfigList:output_type -> papegames.sparrow.survey.ValidRedeemConfigResponse
	73, // 101: papegames.sparrow.survey.SurveyService.GetPreAwardTemplateList:output_type -> papegames.sparrow.survey.PreAwardTemplateResponse
	17, // 102: papegames.sparrow.survey.SurveyService.Upload:output_type -> papegames.sparrow.survey.UploadResponse
	74, // 103: papegames.sparrow.survey.SurveyService.SurveyViewCreate:output_type -> papegames.sparrow.survey.SurveyViewCreateRes
	75, // 104: papegames.sparrow.survey.SurveyService.SurveyViewList:output_type -> papegames.sparrow.survey.SurveyViewListRes
	62, // 105: papegames.sparrow.survey.SurveyService.SurveyViewUpdate:output_type -> papegames.type.Empty
	62, // 106: papegames.sparrow.survey.SurveyService.SurveyViewDelete:output_type -> papegames.type.Empty
	62, // 107: papegames.sparrow.survey.SurveyService.SurveyGroupCreate:output_type -> papegames.type.Empty
	76, // 108: papegames.sparrow.survey.SurveyService.SurveyGroupDetail:output_type -> papegames.sparrow.survey.CmsSurveyGroupInfo
	62, // 109: papegames.sparrow.survey.SurveyService.SurveyGroupUpdate:output_type -> papegames.type.Empty
	77, // 110: papegames.sparrow.survey.SurveyService.SurveyGroupList:output_type -> papegames.sparrow.survey.SurveyGroupListRes
	62, // 111: papegames.sparrow.survey.SurveyService.SurveyGroupSubUpdate:output_type -> papegames.type.Empty
	62, // 112: papegames.sparrow.survey.SurveyService.SurveyOverwriteSend:output_type -> papegames.type.Empty
	62, // 113: papegames.sparrow.survey.SurveyService.SurveyOverwriteSync:output_type -> papegames.type.Empty
	62, // 114: papegames.sparrow.survey.SurveyService.SurveyGroupOverwriteSend:output_type -> papegames.type.Empty
	62, // 115: papegames.sparrow.survey.SurveyService.SurveyGroupOverwriteSync:output_type -> papegames.type.Empty
	62, // 116: papegames.sparrow.survey.SurveyService.SurveyExportUserClusterSubmit:output_type -> papegames.type.Empty
	78, // 117: papegames.sparrow.survey.SurveyService.SurveyExportHeaders:output_type -> papegames.sparrow.survey.SurveyExportHeadersRes
	61, // [61:118] is the sub-list for method output_type
	4,  // [4:61] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_proto_survey_proto_init() }
func file_proto_survey_proto_init() {
	if File_proto_survey_proto != nil {
		return
	}
	file_proto_survey_export_task_proto_init()
	file_proto_survey_record_proto_init()
	file_proto_survey_conf_proto_init()
	file_proto_survey_deliver_proto_init()
	file_proto_settings_proto_init()
	file_proto_survey_group_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*HealthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetUserInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Userinfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Clientlist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Permission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*JwtUserInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*UserCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UserCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*ClearAllSurveyRecycleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CreateSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*StatisticsUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyStatisticsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*GetLatestSurveyBySurveyIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*GetLatestSurveyBySurveyIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*UploadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*UploadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*JwtUserInfoV1_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_survey_proto_goTypes,
		DependencyIndexes: file_proto_survey_proto_depIdxs,
		MessageInfos:      file_proto_survey_proto_msgTypes,
	}.Build()
	File_proto_survey_proto = out.File
	file_proto_survey_proto_rawDesc = nil
	file_proto_survey_proto_goTypes = nil
	file_proto_survey_proto_depIdxs = nil
}
