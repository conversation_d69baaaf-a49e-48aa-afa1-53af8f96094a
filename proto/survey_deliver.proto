syntax = "proto3";

package papegames.sparrow.survey;
import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// 获取投放列表
message GetDeliverListRequest {
  int64 client_id = 1 [json_name = "client_id"];
}

// 投放列表
message GetDeliverListResponse {
  repeated Deliver list = 1 [json_name = "list"];
}

message Deliver {
  string default_language = 1 [json_name = "default_language"];
  string region = 2 [json_name = "region"];
  string official_website_host = 3 [json_name = "official_website_host"];
  string survey_host = 4 [json_name = "survey_host"];
}

message GetZoneListRequest {
  int64 client_id = 1 [json_name = "client_id"];
}

message GetZoneListResponse {
  repeated DataItem list = 1;
}

message DataItem {
  int32 value = 1 [
    (tagger.tags) = "json:value"
  ];
  string label = 2 [
    (tagger.tags) = "json:label"
  ];
}
