// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_conf.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurveyListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页码，默认1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，默认10
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 答卷id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 答卷名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 创建时间排序
	SortCtime string `protobuf:"bytes,5,opt,name=sort_ctime,json=sortCtime,proto3" json:"sort_ctime,omitempty"`
	// 状态
	Status   int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	ClientId int64 `protobuf:"varint,7,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷ID数组
	IdList []int64 `protobuf:"varint,8,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
}

func (x *SurveyListRequest) Reset() {
	*x = SurveyListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyListRequest) ProtoMessage() {}

func (x *SurveyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyListRequest.ProtoReflect.Descriptor instead.
func (*SurveyListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SurveyListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SurveyListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyListRequest) GetSortCtime() string {
	if x != nil {
		return x.SortCtime
	}
	return ""
}

func (x *SurveyListRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SurveyListRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyListRequest) GetIdList() []int64 {
	if x != nil {
		return x.IdList
	}
	return nil
}

type SurveyListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*Survey `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64     `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *SurveyListResponse) Reset() {
	*x = SurveyListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyListResponse) ProtoMessage() {}

func (x *SurveyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyListResponse.ProtoReflect.Descriptor instead.
func (*SurveyListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyListResponse) GetList() []*Survey {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyListResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SurveyPreviewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *SurveyPreviewReq) Reset() {
	*x = SurveyPreviewReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyPreviewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyPreviewReq) ProtoMessage() {}

func (x *SurveyPreviewReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyPreviewReq.ProtoReflect.Descriptor instead.
func (*SurveyPreviewReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{2}
}

func (x *SurveyPreviewReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyPreviewReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type SurveyRecordConfDetailsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyRecordConfDetailsReq) Reset() {
	*x = SurveyRecordConfDetailsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordConfDetailsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordConfDetailsReq) ProtoMessage() {}

func (x *SurveyRecordConfDetailsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordConfDetailsReq.ProtoReflect.Descriptor instead.
func (*SurveyRecordConfDetailsReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{3}
}

func (x *SurveyRecordConfDetailsReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecordConfDetailsReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyRecordConfDetailsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordDetail string `protobuf:"bytes,1,opt,name=record_detail,json=recordDetail,proto3" json:"record_detail,omitempty"`
	SurveyConfig string `protobuf:"bytes,2,opt,name=survey_config,json=surveyConfig,proto3" json:"survey_config,omitempty"`
	UserRecord   string `protobuf:"bytes,3,opt,name=user_record,json=userRecord,proto3" json:"user_record,omitempty"`
}

func (x *SurveyRecordConfDetailsRes) Reset() {
	*x = SurveyRecordConfDetailsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordConfDetailsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordConfDetailsRes) ProtoMessage() {}

func (x *SurveyRecordConfDetailsRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordConfDetailsRes.ProtoReflect.Descriptor instead.
func (*SurveyRecordConfDetailsRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{4}
}

func (x *SurveyRecordConfDetailsRes) GetRecordDetail() string {
	if x != nil {
		return x.RecordDetail
	}
	return ""
}

func (x *SurveyRecordConfDetailsRes) GetSurveyConfig() string {
	if x != nil {
		return x.SurveyConfig
	}
	return ""
}

func (x *SurveyRecordConfDetailsRes) GetUserRecord() string {
	if x != nil {
		return x.UserRecord
	}
	return ""
}

type SyncSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId        int64  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	MaterialVersion string `protobuf:"bytes,3,opt,name=material_version,json=materialVersion,proto3" json:"material_version,omitempty"`
}

func (x *SyncSurveyRequest) Reset() {
	*x = SyncSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSurveyRequest) ProtoMessage() {}

func (x *SyncSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSurveyRequest.ProtoReflect.Descriptor instead.
func (*SyncSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{5}
}

func (x *SyncSurveyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SyncSurveyRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SyncSurveyRequest) GetMaterialVersion() string {
	if x != nil {
		return x.MaterialVersion
	}
	return ""
}

type SyncSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string ID = 1;               // 标识符
	// string CTime = 2; // 创建时间
	// string MTime = 3; // 修改时间
	// string Editor = 4;           // 编辑者
	// int32 IsDelete = 5;           // 删除标记
	// Schema PreviewSchema = 6;    // 预览模式
	// string HashCode = 7;         // 哈希码
	// int32 IsPause = 8;            // 暂停标记
	// int32 IsClosed = 9;           // 关闭标记
	// int32 IsOpened = 10;          // 打开标记
	// int32 IsModifyUnpublish = 11; // 修改未发布标记
	// string ClientId = 12;        // 客户端ID
	Data      uint32 `protobuf:"varint,1,opt,name=data,proto3" json:"data,omitempty"`
	Code      uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Success   bool   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Timestamp string `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Info      string `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SyncSurveyResponse) Reset() {
	*x = SyncSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSurveyResponse) ProtoMessage() {}

func (x *SyncSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSurveyResponse.ProtoReflect.Descriptor instead.
func (*SyncSurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{6}
}

func (x *SyncSurveyResponse) GetData() uint32 {
	if x != nil {
		return x.Data
	}
	return 0
}

func (x *SyncSurveyResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncSurveyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SyncSurveyResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *SyncSurveyResponse) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

type SyncSurveyResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      int64            `protobuf:"varint,1,opt,name=data,proto3" json:"data,omitempty"`
	Code      int64            `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Success   bool             `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Timestamp *xtype.Timestamp `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *SyncSurveyResponseData) Reset() {
	*x = SyncSurveyResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSurveyResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSurveyResponseData) ProtoMessage() {}

func (x *SyncSurveyResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSurveyResponseData.ProtoReflect.Descriptor instead.
func (*SyncSurveyResponseData) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{7}
}

func (x *SyncSurveyResponseData) GetData() int64 {
	if x != nil {
		return x.Data
	}
	return 0
}

func (x *SyncSurveyResponseData) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncSurveyResponseData) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SyncSurveyResponseData) GetTimestamp() *xtype.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type ImpSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client_id
	Clientid string `protobuf:"bytes,1,opt,name=clientid,proto3" json:"clientid"`
	// 问卷名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 问卷开始时间
	Stime string `protobuf:"bytes,3,opt,name=stime,proto3" json:"stime"`
	// 问卷结束时间
	Etime string `protobuf:"bytes,4,opt,name=etime,proto3" json:"etime"`
	// 配置 schema
	Schema *xtype.RawMessage `protobuf:"bytes,5,opt,name=schema,proto3" json:"schema"`
	// 预览 schema
	PreviewSchema *xtype.RawMessage `protobuf:"bytes,6,opt,name=previewSchema,proto3" json:"previewSchema"`
	// 问卷设置
	Settings *xtype.RawMessage `protobuf:"bytes,7,opt,name=settings,proto3" json:"settings"`
	// 问卷C端用到的配置
	WebSettings *xtype.RawMessage `protobuf:"bytes,8,opt,name=webSettings,proto3" json:"webSettings"`
	// 问卷C端用到的配置
	Languages  *xtype.RawMessage `protobuf:"bytes,9,opt,name=languages,proto3" json:"languages"`
	ApiVersion int32             `protobuf:"varint,10,opt,name=apiVersion,proto3" json:"apiVersion"`
	KeyValue   string            `protobuf:"bytes,11,opt,name=keyValue,proto3" json:"keyValue"`
	Font       string            `protobuf:"bytes,12,opt,name=font,proto3" json:"font"`
	// 创建人
	Creator string `protobuf:"bytes,13,opt,name=creator,proto3" json:"creator"`
}

func (x *ImpSurveyRequest) Reset() {
	*x = ImpSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImpSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImpSurveyRequest) ProtoMessage() {}

func (x *ImpSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImpSurveyRequest.ProtoReflect.Descriptor instead.
func (*ImpSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{8}
}

func (x *ImpSurveyRequest) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *ImpSurveyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImpSurveyRequest) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *ImpSurveyRequest) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

func (x *ImpSurveyRequest) GetSchema() *xtype.RawMessage {
	if x != nil {
		return x.Schema
	}
	return nil
}

func (x *ImpSurveyRequest) GetPreviewSchema() *xtype.RawMessage {
	if x != nil {
		return x.PreviewSchema
	}
	return nil
}

func (x *ImpSurveyRequest) GetSettings() *xtype.RawMessage {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *ImpSurveyRequest) GetWebSettings() *xtype.RawMessage {
	if x != nil {
		return x.WebSettings
	}
	return nil
}

func (x *ImpSurveyRequest) GetLanguages() *xtype.RawMessage {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *ImpSurveyRequest) GetApiVersion() int32 {
	if x != nil {
		return x.ApiVersion
	}
	return 0
}

func (x *ImpSurveyRequest) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *ImpSurveyRequest) GetFont() string {
	if x != nil {
		return x.Font
	}
	return ""
}

func (x *ImpSurveyRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type SurveySetStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status   int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *SurveySetStatusRequest) Reset() {
	*x = SurveySetStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveySetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveySetStatusRequest) ProtoMessage() {}

func (x *SurveySetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveySetStatusRequest.ProtoReflect.Descriptor instead.
func (*SurveySetStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{9}
}

func (x *SurveySetStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveySetStatusRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SurveySetStatusRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type SurveyDelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64   `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DelList  []int64 `protobuf:"varint,2,rep,packed,name=del_list,json=delList,proto3" json:"del_list,omitempty"`
}

func (x *SurveyDelRequest) Reset() {
	*x = SurveyDelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyDelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyDelRequest) ProtoMessage() {}

func (x *SurveyDelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyDelRequest.ProtoReflect.Descriptor instead.
func (*SurveyDelRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{10}
}

func (x *SurveyDelRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyDelRequest) GetDelList() []int64 {
	if x != nil {
		return x.DelList
	}
	return nil
}

type SurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId int64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *SurveyRequest) Reset() {
	*x = SurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRequest) ProtoMessage() {}

func (x *SurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRequest.ProtoReflect.Descriptor instead.
func (*SurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{11}
}

func (x *SurveyRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type SurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SurveyResponse) Reset() {
	*x = SurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyResponse) ProtoMessage() {}

func (x *SurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyResponse.ProtoReflect.Descriptor instead.
func (*SurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{12}
}

func (x *SurveyResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Survey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:主键ID"`
	// client_id
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id"`
	// 问卷名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name" gorm:"type:varchar(128);comment:问卷名称"`
	// 问卷是否打开
	IsClosed int32 `protobuf:"varint,4,opt,name=is_closed,json=isClosed,proto3" json:"is_closed" gorm:"type:int(8);comment:问卷是否打开：（0: 打开，1: 关闭）"`
	// 问卷是否暂停
	IsPause int32 `protobuf:"varint,5,opt,name=is_pause,json=isPause,proto3" json:"is_pause" gorm:"type:int(8);comment:问卷是否暂停：（0: 不暂停，1: 暂停）"`
	// 是否发布
	IsPublish int32 `protobuf:"varint,6,opt,name=is_publish,json=isPublish,proto3" json:"is_publish" gorm:"type:tinyint(4);comment:是否发布（0: 未发布，1: 已发布）"`
	// 是否有修改未发布
	IsModifyUnpublish int32 `protobuf:"varint,7,opt,name=is_modify_unpublish,json=isModifyUnpublish,proto3" json:"is_modify_unpublish" gorm:"type:tinyint(4);comment:是否有修改未发布（0: 无，1: 有）"`
	// 是否开启过答题
	IsOpened int32 `protobuf:"varint,8,opt,name=is_opened,json=isOpened,proto3" json:"is_opened" gorm:"type:tinyint(4);comment:是否开启过答题（0: 未开启过，1: 开启过）"`
	// 问卷开始时间
	Stime string `protobuf:"bytes,9,opt,name=stime,proto3" json:"stime" gorm:"type:varchar;comment:问卷开始时间"`
	// 问卷结束时间
	Etime string `protobuf:"bytes,10,opt,name=etime,proto3" json:"etime" gorm:"type:varchar;comment:问卷结束时间"`
	// 问卷类型
	Type int32 `protobuf:"varint,11,opt,name=type,proto3" json:"type" gorm:"type:int(8);comment:问卷类型"`
	// 配置 schema
	Schema string `protobuf:"bytes,12,opt,name=schema,proto3" json:"schema"`
	// 预览 schema
	PreviewSchema string `protobuf:"bytes,13,opt,name=preview_schema,json=previewSchema,proto3" json:"preview_schema"`
	// 问卷设置
	Settings string `protobuf:"bytes,14,opt,name=settings,proto3" json:"settings" gorm:"json;comment:问卷设置"`
	// 问卷C端用到的配置
	WebSettings string `protobuf:"bytes,15,opt,name=web_settings,json=webSettings,proto3" json:"web_settings" gorm:"type:varchar;comment:问卷C端用到的配置"`
	// 问卷C端用到的配置
	Languages string `protobuf:"bytes,16,opt,name=languages,proto3" json:"languages" gorm:"type:varchar;comment:语言包"`
	// 问卷id-hash
	HashCode string `protobuf:"bytes,17,opt,name=hash_code,json=hashCode,proto3" json:"hash_code" gorm:"type:varchar;comment:问卷id-hash"`
	// 是否删除
	IsDelete int32 `protobuf:"varint,18,opt,name=is_delete,json=isDelete,proto3" json:"is_delete" gorm:"type:tinyint(4);comment:是否删除（0: 未删除，1: 已删除）"`
	// 删除时间
	Deltime  string `protobuf:"bytes,19,opt,name=deltime,proto3" json:"deltime" gorm:"type:varchar;comment:删除时间"`
	KeyValue string `protobuf:"bytes,20,opt,name=key_value,json=keyValue,proto3" json:"key_value" gorm:"type:text;comment:中文/英文文案"`
	Font     string `protobuf:"bytes,21,opt,name=font,proto3" json:"font" gorm:"type:text;comment:字体 ttf 的 oss 链接"`
	// 备注
	Remark string `protobuf:"bytes,22,opt,name=remark,proto3" json:"remark" gorm:"type:varchar;comment:备注"`
	// 创建时间
	Ctime string `protobuf:"bytes,23,opt,name=ctime,proto3" json:"ctime,omitempty" gorm:"autoCtime"`
	// 更新时间
	Mtime string `protobuf:"bytes,24,opt,name=mtime,proto3" json:"mtime,omitempty" gorm:"autoMtime"`
	// 创建人
	Creator string `protobuf:"bytes,25,opt,name=creator,proto3" json:"creator" gorm:"type:varchar(64);comment:创建人"`
	// 最近修改人
	Editor string `protobuf:"bytes,26,opt,name=editor,proto3" json:"editor" gorm:"type:varchar(64);comment:最近修改人"`
	// isTimeLimit
	IsTimeLimit bool `protobuf:"varint,27,opt,name=is_time_limit,json=isTimeLimit,proto3" json:"is_time_limit" gorm:"-"`
	// 用户答卷数量
	AllAnsweredUserCount int64 `protobuf:"varint,28,opt,name=all_answered_user_count,json=allAnsweredUserCount,proto3" json:"all_answered_user_count" gorm:"-"`
	// 状态
	Status int32 `protobuf:"varint,29,opt,name=status,proto3" json:"status" gorm:"-"`
	// 答卷统计
	FullValidUid           int64                   `protobuf:"varint,30,opt,name=full_valid_uid,json=fullValidUid,proto3" json:"full_valid_uid" gorm:"-"`
	QuestionStatisticsData *QuestionStatisticsData `protobuf:"bytes,31,opt,name=question_statistics_data,json=questionStatisticsData,proto3" json:"question_statistics_data" gorm:"-"`
	WebPathList            []*WebPath              `protobuf:"bytes,32,rep,name=web_path_list,json=webPathList,proto3" json:"web_path_list" gorm:"-"`
}

func (x *Survey) Reset() {
	*x = Survey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Survey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Survey) ProtoMessage() {}

func (x *Survey) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Survey.ProtoReflect.Descriptor instead.
func (*Survey) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{13}
}

func (x *Survey) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Survey) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *Survey) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Survey) GetIsClosed() int32 {
	if x != nil {
		return x.IsClosed
	}
	return 0
}

func (x *Survey) GetIsPause() int32 {
	if x != nil {
		return x.IsPause
	}
	return 0
}

func (x *Survey) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *Survey) GetIsModifyUnpublish() int32 {
	if x != nil {
		return x.IsModifyUnpublish
	}
	return 0
}

func (x *Survey) GetIsOpened() int32 {
	if x != nil {
		return x.IsOpened
	}
	return 0
}

func (x *Survey) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *Survey) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

func (x *Survey) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Survey) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *Survey) GetPreviewSchema() string {
	if x != nil {
		return x.PreviewSchema
	}
	return ""
}

func (x *Survey) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *Survey) GetWebSettings() string {
	if x != nil {
		return x.WebSettings
	}
	return ""
}

func (x *Survey) GetLanguages() string {
	if x != nil {
		return x.Languages
	}
	return ""
}

func (x *Survey) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *Survey) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *Survey) GetDeltime() string {
	if x != nil {
		return x.Deltime
	}
	return ""
}

func (x *Survey) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *Survey) GetFont() string {
	if x != nil {
		return x.Font
	}
	return ""
}

func (x *Survey) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Survey) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *Survey) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *Survey) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Survey) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

func (x *Survey) GetIsTimeLimit() bool {
	if x != nil {
		return x.IsTimeLimit
	}
	return false
}

func (x *Survey) GetAllAnsweredUserCount() int64 {
	if x != nil {
		return x.AllAnsweredUserCount
	}
	return 0
}

func (x *Survey) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Survey) GetFullValidUid() int64 {
	if x != nil {
		return x.FullValidUid
	}
	return 0
}

func (x *Survey) GetQuestionStatisticsData() *QuestionStatisticsData {
	if x != nil {
		return x.QuestionStatisticsData
	}
	return nil
}

func (x *Survey) GetWebPathList() []*WebPath {
	if x != nil {
		return x.WebPathList
	}
	return nil
}

type QuestionStatisticsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidAnswerTotal int64  `protobuf:"varint,1,opt,name=valid_answer_total,json=validAnswerTotal,proto3" json:"valid_answer_total"`
	ValidUserTotal   uint32 `protobuf:"varint,2,opt,name=valid_user_total,json=validUserTotal,proto3" json:"valid_user_total"`
}

func (x *QuestionStatisticsData) Reset() {
	*x = QuestionStatisticsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionStatisticsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionStatisticsData) ProtoMessage() {}

func (x *QuestionStatisticsData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionStatisticsData.ProtoReflect.Descriptor instead.
func (*QuestionStatisticsData) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{14}
}

func (x *QuestionStatisticsData) GetValidAnswerTotal() int64 {
	if x != nil {
		return x.ValidAnswerTotal
	}
	return 0
}

func (x *QuestionStatisticsData) GetValidUserTotal() uint32 {
	if x != nil {
		return x.ValidUserTotal
	}
	return 0
}

// 回收站
type SurveyRecycleListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	FullValidUid int64  `protobuf:"varint,3,opt,name=full_valid_uid,json=fullValidUid,proto3" json:"full_valid_uid,omitempty"`
	Ctime        string `protobuf:"bytes,4,opt,name=ctime,proto3" json:"ctime,omitempty"`
}

func (x *SurveyRecycleListRes) Reset() {
	*x = SurveyRecycleListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecycleListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecycleListRes) ProtoMessage() {}

func (x *SurveyRecycleListRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecycleListRes.ProtoReflect.Descriptor instead.
func (*SurveyRecycleListRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{15}
}

func (x *SurveyRecycleListRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecycleListRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyRecycleListRes) GetFullValidUid() int64 {
	if x != nil {
		return x.FullValidUid
	}
	return 0
}

func (x *SurveyRecycleListRes) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

type RecoverSurveyRecycleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []int64 `protobuf:"varint,1,rep,packed,name=list,proto3" json:"list,omitempty"`
	ClientId int64   `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *RecoverSurveyRecycleReq) Reset() {
	*x = RecoverSurveyRecycleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverSurveyRecycleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverSurveyRecycleReq) ProtoMessage() {}

func (x *RecoverSurveyRecycleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverSurveyRecycleReq.ProtoReflect.Descriptor instead.
func (*RecoverSurveyRecycleReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{16}
}

func (x *RecoverSurveyRecycleReq) GetList() []int64 {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecoverSurveyRecycleReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

// Settings message
type Setting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRuleConfig    *BaseRuleConfig    `protobuf:"bytes,1,opt,name=baseRuleConfig,proto3" json:"baseRuleConfig,omitempty"`
	GiftConfig        *GiftConfig        `protobuf:"bytes,2,opt,name=giftConfig,proto3" json:"giftConfig,omitempty"`
	AnswerLimitConfig *AnswerLimitConfig `protobuf:"bytes,3,opt,name=answerLimitConfig,proto3" json:"answerLimitConfig,omitempty"`
	ZoneIds           []int32            `protobuf:"varint,4,rep,packed,name=zoneIds,proto3" json:"zoneIds,omitempty"`
	MaterialsConfig   *MaterialsConfig   `protobuf:"bytes,5,opt,name=materialsConfig,proto3" json:"materialsConfig,omitempty"`
	FooterConfig      *FooterConfig      `protobuf:"bytes,6,opt,name=footerConfig,proto3" json:"footerConfig,omitempty"`
	SourceConfig      *SourceConfig      `protobuf:"bytes,7,opt,name=sourceConfig,proto3" json:"sourceConfig,omitempty"`
}

func (x *Setting) Reset() {
	*x = Setting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Setting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Setting) ProtoMessage() {}

func (x *Setting) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Setting.ProtoReflect.Descriptor instead.
func (*Setting) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{17}
}

func (x *Setting) GetBaseRuleConfig() *BaseRuleConfig {
	if x != nil {
		return x.BaseRuleConfig
	}
	return nil
}

func (x *Setting) GetGiftConfig() *GiftConfig {
	if x != nil {
		return x.GiftConfig
	}
	return nil
}

func (x *Setting) GetAnswerLimitConfig() *AnswerLimitConfig {
	if x != nil {
		return x.AnswerLimitConfig
	}
	return nil
}

func (x *Setting) GetZoneIds() []int32 {
	if x != nil {
		return x.ZoneIds
	}
	return nil
}

func (x *Setting) GetMaterialsConfig() *MaterialsConfig {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *Setting) GetFooterConfig() *FooterConfig {
	if x != nil {
		return x.FooterConfig
	}
	return nil
}

func (x *Setting) GetSourceConfig() *SourceConfig {
	if x != nil {
		return x.SourceConfig
	}
	return nil
}

type WebSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType       string           `protobuf:"bytes,1,opt,name=loginType,proto3" json:"loginType,omitempty"`
	IsEndPreview    bool             `protobuf:"varint,2,opt,name=isEndPreview,proto3" json:"isEndPreview,omitempty"`
	IsGoOnAnswer    bool             `protobuf:"varint,3,opt,name=isGoOnAnswer,proto3" json:"isGoOnAnswer,omitempty"`
	LanguageList    []string         `protobuf:"bytes,4,rep,name=languageList,proto3" json:"languageList,omitempty"`
	MaterialsConfig *MaterialsConfig `protobuf:"bytes,5,opt,name=materialsConfig,proto3" json:"materialsConfig,omitempty"`
	FooterConfig    *FooterConfig    `protobuf:"bytes,6,opt,name=footerConfig,proto3" json:"footerConfig,omitempty"`
	SourceConfig    *SourceConfig    `protobuf:"bytes,7,opt,name=sourceConfig,proto3" json:"sourceConfig,omitempty"`
}

func (x *WebSetting) Reset() {
	*x = WebSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSetting) ProtoMessage() {}

func (x *WebSetting) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSetting.ProtoReflect.Descriptor instead.
func (*WebSetting) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{18}
}

func (x *WebSetting) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *WebSetting) GetIsEndPreview() bool {
	if x != nil {
		return x.IsEndPreview
	}
	return false
}

func (x *WebSetting) GetIsGoOnAnswer() bool {
	if x != nil {
		return x.IsGoOnAnswer
	}
	return false
}

func (x *WebSetting) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *WebSetting) GetMaterialsConfig() *MaterialsConfig {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *WebSetting) GetFooterConfig() *FooterConfig {
	if x != nil {
		return x.FooterConfig
	}
	return nil
}

func (x *WebSetting) GetSourceConfig() *SourceConfig {
	if x != nil {
		return x.SourceConfig
	}
	return nil
}

type BaseRuleConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType         string             `protobuf:"bytes,1,opt,name=loginType,proto3" json:"loginType,omitempty"`
	TimeLimitConfig   *TimeLimitConfig   `protobuf:"bytes,2,opt,name=timeLimitConfig,proto3" json:"timeLimitConfig,omitempty"`
	IsEndPreview      bool               `protobuf:"varint,3,opt,name=isEndPreview,proto3" json:"isEndPreview,omitempty"`
	IsGoOnAnswer      bool               `protobuf:"varint,4,opt,name=isGoOnAnswer,proto3" json:"isGoOnAnswer,omitempty"`
	AnswerTimesConfig *AnswerTimesConfig `protobuf:"bytes,5,opt,name=answerTimesConfig,proto3" json:"answerTimesConfig,omitempty"`
	LanguageList      []string           `protobuf:"bytes,6,rep,name=languageList,proto3" json:"languageList,omitempty"`
	DeliverList       []string           `protobuf:"bytes,7,rep,name=deliverList,proto3" json:"deliverList,omitempty"`
}

func (x *BaseRuleConfig) Reset() {
	*x = BaseRuleConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseRuleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseRuleConfig) ProtoMessage() {}

func (x *BaseRuleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseRuleConfig.ProtoReflect.Descriptor instead.
func (*BaseRuleConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{19}
}

func (x *BaseRuleConfig) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *BaseRuleConfig) GetTimeLimitConfig() *TimeLimitConfig {
	if x != nil {
		return x.TimeLimitConfig
	}
	return nil
}

func (x *BaseRuleConfig) GetIsEndPreview() bool {
	if x != nil {
		return x.IsEndPreview
	}
	return false
}

func (x *BaseRuleConfig) GetIsGoOnAnswer() bool {
	if x != nil {
		return x.IsGoOnAnswer
	}
	return false
}

func (x *BaseRuleConfig) GetAnswerTimesConfig() *AnswerTimesConfig {
	if x != nil {
		return x.AnswerTimesConfig
	}
	return nil
}

func (x *BaseRuleConfig) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *BaseRuleConfig) GetDeliverList() []string {
	if x != nil {
		return x.DeliverList
	}
	return nil
}

type TimeLimitConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsTimeLimit bool   `protobuf:"varint,1,opt,name=isTimeLimit,proto3" json:"isTimeLimit,omitempty"`
	Stime       string `protobuf:"bytes,2,opt,name=stime,proto3" json:"stime,omitempty"`
	Etime       string `protobuf:"bytes,3,opt,name=etime,proto3" json:"etime,omitempty"`
}

func (x *TimeLimitConfig) Reset() {
	*x = TimeLimitConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeLimitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeLimitConfig) ProtoMessage() {}

func (x *TimeLimitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeLimitConfig.ProtoReflect.Descriptor instead.
func (*TimeLimitConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{20}
}

func (x *TimeLimitConfig) GetIsTimeLimit() bool {
	if x != nil {
		return x.IsTimeLimit
	}
	return false
}

func (x *TimeLimitConfig) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *TimeLimitConfig) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

type AnswerTimesConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LimitType int32 `protobuf:"varint,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
	Times     int32 `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
}

func (x *AnswerTimesConfig) Reset() {
	*x = AnswerTimesConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerTimesConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerTimesConfig) ProtoMessage() {}

func (x *AnswerTimesConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerTimesConfig.ProtoReflect.Descriptor instead.
func (*AnswerTimesConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{21}
}

func (x *AnswerTimesConfig) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *AnswerTimesConfig) GetTimes() int32 {
	if x != nil {
		return x.Times
	}
	return 0
}

type GiftConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsGiveOutByCms bool            `protobuf:"varint,1,opt,name=isGiveOutByCms,proto3" json:"isGiveOutByCms,omitempty"`
	GiveOutType    string          `protobuf:"bytes,2,opt,name=giveOutType,proto3" json:"giveOutType,omitempty"`
	PreAwardConfig *PreAwardConfig `protobuf:"bytes,3,opt,name=preAwardConfig,proto3" json:"preAwardConfig,omitempty"`
	RedeemConfig   *RedeemConfig   `protobuf:"bytes,4,opt,name=redeemConfig,proto3" json:"redeemConfig,omitempty"`
}

func (x *GiftConfig) Reset() {
	*x = GiftConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftConfig) ProtoMessage() {}

func (x *GiftConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftConfig.ProtoReflect.Descriptor instead.
func (*GiftConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{22}
}

func (x *GiftConfig) GetIsGiveOutByCms() bool {
	if x != nil {
		return x.IsGiveOutByCms
	}
	return false
}

func (x *GiftConfig) GetGiveOutType() string {
	if x != nil {
		return x.GiveOutType
	}
	return ""
}

func (x *GiftConfig) GetPreAwardConfig() *PreAwardConfig {
	if x != nil {
		return x.PreAwardConfig
	}
	return nil
}

func (x *GiftConfig) GetRedeemConfig() *RedeemConfig {
	if x != nil {
		return x.RedeemConfig
	}
	return nil
}

type RedeemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedeemHead string `protobuf:"bytes,1,opt,name=redeemHead,json=redeemConfig,proto3" json:"redeemHead,omitempty"`
}

func (x *RedeemConfig) Reset() {
	*x = RedeemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemConfig) ProtoMessage() {}

func (x *RedeemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemConfig.ProtoReflect.Descriptor instead.
func (*RedeemConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{23}
}

func (x *RedeemConfig) GetRedeemHead() string {
	if x != nil {
		return x.RedeemHead
	}
	return ""
}

type PreAwardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *PreAwardConfig) Reset() {
	*x = PreAwardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAwardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAwardConfig) ProtoMessage() {}

func (x *PreAwardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAwardConfig.ProtoReflect.Descriptor instead.
func (*PreAwardConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{24}
}

func (x *PreAwardConfig) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AnswerLimitConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LimitType string `protobuf:"bytes,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
}

func (x *AnswerLimitConfig) Reset() {
	*x = AnswerLimitConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerLimitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerLimitConfig) ProtoMessage() {}

func (x *AnswerLimitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerLimitConfig.ProtoReflect.Descriptor instead.
func (*AnswerLimitConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{25}
}

func (x *AnswerLimitConfig) GetLimitType() string {
	if x != nil {
		return x.LimitType
	}
	return ""
}

type MaterialsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AutoLatestMaterial bool   `protobuf:"varint,1,opt,name=autoLatestMaterial,proto3" json:"autoLatestMaterial,omitempty"`
	MaterialVersion    string `protobuf:"bytes,2,opt,name=materialVersion,proto3" json:"materialVersion,omitempty"`
}

func (x *MaterialsConfig) Reset() {
	*x = MaterialsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialsConfig) ProtoMessage() {}

func (x *MaterialsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialsConfig.ProtoReflect.Descriptor instead.
func (*MaterialsConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{26}
}

func (x *MaterialsConfig) GetAutoLatestMaterial() bool {
	if x != nil {
		return x.AutoLatestMaterial
	}
	return false
}

func (x *MaterialsConfig) GetMaterialVersion() string {
	if x != nil {
		return x.MaterialVersion
	}
	return ""
}

type FooterConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *FooterConfig) Reset() {
	*x = FooterConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FooterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FooterConfig) ProtoMessage() {}

func (x *FooterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FooterConfig.ProtoReflect.Descriptor instead.
func (*FooterConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{27}
}

func (x *FooterConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FooterConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SourceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CityUrl    string       `protobuf:"bytes,1,opt,name=cityUrl,proto3" json:"cityUrl,omitempty"`
	Agreements []*Agreement `protobuf:"bytes,2,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

func (x *SourceConfig) Reset() {
	*x = SourceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceConfig) ProtoMessage() {}

func (x *SourceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceConfig.ProtoReflect.Descriptor instead.
func (*SourceConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{28}
}

func (x *SourceConfig) GetCityUrl() string {
	if x != nil {
		return x.CityUrl
	}
	return ""
}

func (x *SourceConfig) GetAgreements() []*Agreement {
	if x != nil {
		return x.Agreements
	}
	return nil
}

type Agreement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image string `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Text  string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Link  string `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
}

func (x *Agreement) Reset() {
	*x = Agreement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Agreement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Agreement) ProtoMessage() {}

func (x *Agreement) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Agreement.ProtoReflect.Descriptor instead.
func (*Agreement) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{29}
}

func (x *Agreement) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Agreement) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Agreement) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

type Schema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version        string           `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	ComponentsMap  []*ComponentMap  `protobuf:"bytes,2,rep,name=componentsMap,proto3" json:"componentsMap,omitempty"`
	ComponentsTree []*ComponentTree `protobuf:"bytes,3,rep,name=componentsTree,proto3" json:"componentsTree,omitempty"`
	I18N           *I18N            `protobuf:"bytes,4,opt,name=i18n,proto3" json:"i18n,omitempty"`
	Config         *Config          `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	Meta           *Meta            `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`
	ClientId       int64            `protobuf:"varint,7,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *Schema) Reset() {
	*x = Schema{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Schema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Schema) ProtoMessage() {}

func (x *Schema) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Schema.ProtoReflect.Descriptor instead.
func (*Schema) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{30}
}

func (x *Schema) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Schema) GetComponentsMap() []*ComponentMap {
	if x != nil {
		return x.ComponentsMap
	}
	return nil
}

func (x *Schema) GetComponentsTree() []*ComponentTree {
	if x != nil {
		return x.ComponentsTree
	}
	return nil
}

func (x *Schema) GetI18N() *I18N {
	if x != nil {
		return x.I18N
	}
	return nil
}

func (x *Schema) GetConfig() *Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *Schema) GetMeta() *Meta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *Schema) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type I18N struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *I18N) Reset() {
	*x = I18N{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *I18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*I18N) ProtoMessage() {}

func (x *I18N) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use I18N.ProtoReflect.Descriptor instead.
func (*I18N) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{31}
}

type ComponentMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageName   string `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	Version       string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	ExportName    string `protobuf:"bytes,3,opt,name=export_name,json=exportName,proto3" json:"export_name,omitempty"`
	Main          string `protobuf:"bytes,4,opt,name=main,proto3" json:"main,omitempty"`
	Destructuring bool   `protobuf:"varint,5,opt,name=destructuring,proto3" json:"destructuring,omitempty"`
	SubName       string `protobuf:"bytes,6,opt,name=sub_name,json=subName,proto3" json:"sub_name,omitempty"`
	ComponentName string `protobuf:"bytes,7,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	DevMode       string `protobuf:"bytes,8,opt,name=dev_mode,json=devMode,proto3" json:"dev_mode,omitempty"`
}

func (x *ComponentMap) Reset() {
	*x = ComponentMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentMap) ProtoMessage() {}

func (x *ComponentMap) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentMap.ProtoReflect.Descriptor instead.
func (*ComponentMap) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{32}
}

func (x *ComponentMap) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *ComponentMap) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ComponentMap) GetExportName() string {
	if x != nil {
		return x.ExportName
	}
	return ""
}

func (x *ComponentMap) GetMain() string {
	if x != nil {
		return x.Main
	}
	return ""
}

func (x *ComponentMap) GetDestructuring() bool {
	if x != nil {
		return x.Destructuring
	}
	return false
}

func (x *ComponentMap) GetSubName() string {
	if x != nil {
		return x.SubName
	}
	return ""
}

func (x *ComponentMap) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *ComponentMap) GetDevMode() string {
	if x != nil {
		return x.DevMode
	}
	return ""
}

type MetaTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title  string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Router string `protobuf:"bytes,2,opt,name=router,proto3" json:"router,omitempty"`
}

func (x *MetaTree) Reset() {
	*x = MetaTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaTree) ProtoMessage() {}

func (x *MetaTree) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaTree.ProtoReflect.Descriptor instead.
func (*MetaTree) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{33}
}

func (x *MetaTree) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MetaTree) GetRouter() string {
	if x != nil {
		return x.Router
	}
	return ""
}

type SurveySettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCustomSkinUrl              string `protobuf:"bytes,1,opt,name=is_custom_skin_url,json=isCustomSkinUrl,proto3" json:"is_custom_skin_url,omitempty"`
	SkinUrl                      string `protobuf:"bytes,2,opt,name=skin_url,json=skinUrl,proto3" json:"skin_url,omitempty"`
	ShowQuestionSerialNumber     bool   `protobuf:"varint,3,opt,name=show_question_serial_number,json=showQuestionSerialNumber,proto3" json:"show_question_serial_number,omitempty"`
	AnswerQuestionProcessCanBack bool   `protobuf:"varint,4,opt,name=answer_question_process_can_back,json=answerQuestionProcessCanBack,proto3" json:"answer_question_process_can_back,omitempty"`
}

func (x *SurveySettings) Reset() {
	*x = SurveySettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveySettings) ProtoMessage() {}

func (x *SurveySettings) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveySettings.ProtoReflect.Descriptor instead.
func (*SurveySettings) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{34}
}

func (x *SurveySettings) GetIsCustomSkinUrl() string {
	if x != nil {
		return x.IsCustomSkinUrl
	}
	return ""
}

func (x *SurveySettings) GetSkinUrl() string {
	if x != nil {
		return x.SkinUrl
	}
	return ""
}

func (x *SurveySettings) GetShowQuestionSerialNumber() bool {
	if x != nil {
		return x.ShowQuestionSerialNumber
	}
	return false
}

func (x *SurveySettings) GetAnswerQuestionProcessCanBack() bool {
	if x != nil {
		return x.AnswerQuestionProcessCanBack
	}
	return false
}

type SkinConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCustomSkinUrl string `protobuf:"bytes,1,opt,name=is_custom_skin_url,json=isCustomSkinUrl,proto3" json:"is_custom_skin_url,omitempty"`
	SkinUrl         string `protobuf:"bytes,2,opt,name=skin_url,json=skinUrl,proto3" json:"skin_url,omitempty"`
	BgColor         string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *SkinConfig) Reset() {
	*x = SkinConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkinConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkinConfig) ProtoMessage() {}

func (x *SkinConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkinConfig.ProtoReflect.Descriptor instead.
func (*SkinConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{35}
}

func (x *SkinConfig) GetIsCustomSkinUrl() string {
	if x != nil {
		return x.IsCustomSkinUrl
	}
	return ""
}

func (x *SkinConfig) GetSkinUrl() string {
	if x != nil {
		return x.SkinUrl
	}
	return ""
}

func (x *SkinConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type SettingsProps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRuleConfig    *BaseRuleConfig    `protobuf:"bytes,1,opt,name=base_rule_config,json=baseRuleConfig,proto3" json:"base_rule_config,omitempty"`
	SkinConfig        *SkinConfig        `protobuf:"bytes,2,opt,name=skin_config,json=skinConfig,proto3" json:"skin_config,omitempty"`
	MaterialsConfig   *MaterialsConfig   `protobuf:"bytes,3,opt,name=materials_config,json=materialsConfig,proto3" json:"materials_config,omitempty"`
	AnswerLimitConfig *AnswerLimitConfig `protobuf:"bytes,4,opt,name=answer_limit_config,json=answerLimitConfig,proto3" json:"answer_limit_config,omitempty"`
	GiftConfig        *GiftConfig        `protobuf:"bytes,5,opt,name=gift_config,json=giftConfig,proto3" json:"gift_config,omitempty"`
	EngineIsInit      bool               `protobuf:"varint,6,opt,name=engine_is_init,json=engineIsInit,proto3" json:"engine_is_init,omitempty"`
}

func (x *SettingsProps) Reset() {
	*x = SettingsProps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettingsProps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettingsProps) ProtoMessage() {}

func (x *SettingsProps) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettingsProps.ProtoReflect.Descriptor instead.
func (*SettingsProps) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{36}
}

func (x *SettingsProps) GetBaseRuleConfig() *BaseRuleConfig {
	if x != nil {
		return x.BaseRuleConfig
	}
	return nil
}

func (x *SettingsProps) GetSkinConfig() *SkinConfig {
	if x != nil {
		return x.SkinConfig
	}
	return nil
}

func (x *SettingsProps) GetMaterialsConfig() *MaterialsConfig {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

func (x *SettingsProps) GetAnswerLimitConfig() *AnswerLimitConfig {
	if x != nil {
		return x.AnswerLimitConfig
	}
	return nil
}

func (x *SettingsProps) GetGiftConfig() *GiftConfig {
	if x != nil {
		return x.GiftConfig
	}
	return nil
}

func (x *SettingsProps) GetEngineIsInit() bool {
	if x != nil {
		return x.EngineIsInit
	}
	return false
}

type PropsTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Spacing                      int32           `protobuf:"varint,1,opt,name=spacing,proto3" json:"spacing,omitempty"`
	WrapperMainTitle             string          `protobuf:"bytes,2,opt,name=wrapper_main_title,json=wrapperMainTitle,proto3" json:"wrapper_main_title,omitempty"`
	WrapperSubTitle              string          `protobuf:"bytes,3,opt,name=wrapper_sub_title,json=wrapperSubTitle,proto3" json:"wrapper_sub_title,omitempty"`
	ShowQuestionSerialNumber     bool            `protobuf:"varint,4,opt,name=show_question_serial_number,json=showQuestionSerialNumber,proto3" json:"show_question_serial_number,omitempty"`
	SkinUrl                      string          `protobuf:"bytes,5,opt,name=skin_url,json=skinUrl,proto3" json:"skin_url,omitempty"`
	AnswerQuestionProcessCanBack bool            `protobuf:"varint,6,opt,name=answer_question_process_can_back,json=answerQuestionProcessCanBack,proto3" json:"answer_question_process_can_back,omitempty"`
	SurveySettings               *SurveySettings `protobuf:"bytes,7,opt,name=survey_settings,json=surveySettings,proto3" json:"survey_settings,omitempty"`
	QuestionWrapperActiveKey     int32           `protobuf:"varint,8,opt,name=question_wrapper_active_key,json=questionWrapperActiveKey,proto3" json:"question_wrapper_active_key,omitempty"`
	EngineIsInit                 bool            `protobuf:"varint,9,opt,name=engine_is_init,json=engineIsInit,proto3" json:"engine_is_init,omitempty"`
	SettingsProps                *SettingsProps  `protobuf:"bytes,10,opt,name=settings_props,json=settingsProps,proto3" json:"settings_props,omitempty"`
	ClientId                     int64           `protobuf:"varint,11,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *PropsTree) Reset() {
	*x = PropsTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropsTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropsTree) ProtoMessage() {}

func (x *PropsTree) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropsTree.ProtoReflect.Descriptor instead.
func (*PropsTree) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{37}
}

func (x *PropsTree) GetSpacing() int32 {
	if x != nil {
		return x.Spacing
	}
	return 0
}

func (x *PropsTree) GetWrapperMainTitle() string {
	if x != nil {
		return x.WrapperMainTitle
	}
	return ""
}

func (x *PropsTree) GetWrapperSubTitle() string {
	if x != nil {
		return x.WrapperSubTitle
	}
	return ""
}

func (x *PropsTree) GetShowQuestionSerialNumber() bool {
	if x != nil {
		return x.ShowQuestionSerialNumber
	}
	return false
}

func (x *PropsTree) GetSkinUrl() string {
	if x != nil {
		return x.SkinUrl
	}
	return ""
}

func (x *PropsTree) GetAnswerQuestionProcessCanBack() bool {
	if x != nil {
		return x.AnswerQuestionProcessCanBack
	}
	return false
}

func (x *PropsTree) GetSurveySettings() *SurveySettings {
	if x != nil {
		return x.SurveySettings
	}
	return nil
}

func (x *PropsTree) GetQuestionWrapperActiveKey() int32 {
	if x != nil {
		return x.QuestionWrapperActiveKey
	}
	return 0
}

func (x *PropsTree) GetEngineIsInit() bool {
	if x != nil {
		return x.EngineIsInit
	}
	return false
}

func (x *PropsTree) GetSettingsProps() *SettingsProps {
	if x != nil {
		return x.SettingsProps
	}
	return nil
}

func (x *PropsTree) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type ComponentTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComponentName  string           `protobuf:"bytes,1,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	Id             string           `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	DocId          string           `protobuf:"bytes,3,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	Meta           *MetaTree        `protobuf:"bytes,4,opt,name=meta,proto3" json:"meta,omitempty"`
	FileName       string           `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Hidden         bool             `protobuf:"varint,6,opt,name=hidden,proto3" json:"hidden,omitempty"`
	Title          string           `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	IsLocked       bool             `protobuf:"varint,8,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	Condition      bool             `protobuf:"varint,9,opt,name=condition,proto3" json:"condition,omitempty"`
	ConditionGroup string           `protobuf:"bytes,10,opt,name=condition_group,json=conditionGroup,proto3" json:"condition_group,omitempty"`
	Children       []*ComponentTree `protobuf:"bytes,11,rep,name=children,proto3" json:"children,omitempty"`
}

func (x *ComponentTree) Reset() {
	*x = ComponentTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentTree) ProtoMessage() {}

func (x *ComponentTree) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentTree.ProtoReflect.Descriptor instead.
func (*ComponentTree) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{38}
}

func (x *ComponentTree) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *ComponentTree) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ComponentTree) GetDocId() string {
	if x != nil {
		return x.DocId
	}
	return ""
}

func (x *ComponentTree) GetMeta() *MetaTree {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *ComponentTree) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ComponentTree) GetHidden() bool {
	if x != nil {
		return x.Hidden
	}
	return false
}

func (x *ComponentTree) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ComponentTree) GetIsLocked() bool {
	if x != nil {
		return x.IsLocked
	}
	return false
}

func (x *ComponentTree) GetCondition() bool {
	if x != nil {
		return x.Condition
	}
	return false
}

func (x *ComponentTree) GetConditionGroup() string {
	if x != nil {
		return x.ConditionGroup
	}
	return ""
}

func (x *ComponentTree) GetChildren() []*ComponentTree {
	if x != nil {
		return x.Children
	}
	return nil
}

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HistoryMode  string  `protobuf:"bytes,1,opt,name=history_mode,json=historyMode,proto3" json:"history_mode,omitempty"`
	TargetRootId string  `protobuf:"bytes,2,opt,name=target_root_id,json=targetRootId,proto3" json:"target_root_id,omitempty"`
	Layout       *Layout `protobuf:"bytes,3,opt,name=layout,proto3" json:"layout,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{39}
}

func (x *Config) GetHistoryMode() string {
	if x != nil {
		return x.HistoryMode
	}
	return ""
}

func (x *Config) GetTargetRootId() string {
	if x != nil {
		return x.TargetRootId
	}
	return ""
}

func (x *Config) GetLayout() *Layout {
	if x != nil {
		return x.Layout
	}
	return nil
}

type Meta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ProjectName string `protobuf:"bytes,2,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Spma        string `protobuf:"bytes,4,opt,name=spma,proto3" json:"spma,omitempty"`
	Creator     string `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`
}

func (x *Meta) Reset() {
	*x = Meta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Meta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Meta) ProtoMessage() {}

func (x *Meta) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Meta.ProtoReflect.Descriptor instead.
func (*Meta) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{40}
}

func (x *Meta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Meta) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *Meta) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Meta) GetSpma() string {
	if x != nil {
		return x.Spma
	}
	return ""
}

func (x *Meta) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

type Layout struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComponentName string       `protobuf:"bytes,1,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	Props         *PropsLayout `protobuf:"bytes,2,opt,name=props,proto3" json:"props,omitempty"`
}

func (x *Layout) Reset() {
	*x = Layout{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Layout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Layout) ProtoMessage() {}

func (x *Layout) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Layout.ProtoReflect.Descriptor instead.
func (*Layout) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{41}
}

func (x *Layout) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *Layout) GetProps() *PropsLayout {
	if x != nil {
		return x.Props
	}
	return nil
}

type PropsLayout struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Logo string `protobuf:"bytes,1,opt,name=logo,proto3" json:"logo,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PropsLayout) Reset() {
	*x = PropsLayout{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropsLayout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropsLayout) ProtoMessage() {}

func (x *PropsLayout) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropsLayout.ProtoReflect.Descriptor instead.
func (*PropsLayout) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{42}
}

func (x *PropsLayout) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *PropsLayout) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UpdateSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Schema      string `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	Languages   string `protobuf:"bytes,5,opt,name=languages,proto3" json:"languages,omitempty"`
	WebSettings string `protobuf:"bytes,6,opt,name=web_settings,json=webSettings,proto3" json:"web_settings,omitempty"`
	KeyValue    string `protobuf:"bytes,7,opt,name=key_value,json=keyValue,proto3" json:"key_value,omitempty"`
	Font        string `protobuf:"bytes,8,opt,name=font,proto3" json:"font,omitempty"`
}

func (x *UpdateSurveyRequest) Reset() {
	*x = UpdateSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSurveyRequest) ProtoMessage() {}

func (x *UpdateSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSurveyRequest.ProtoReflect.Descriptor instead.
func (*UpdateSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateSurveyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSurveyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateSurveyRequest) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *UpdateSurveyRequest) GetLanguages() string {
	if x != nil {
		return x.Languages
	}
	return ""
}

func (x *UpdateSurveyRequest) GetWebSettings() string {
	if x != nil {
		return x.WebSettings
	}
	return ""
}

func (x *UpdateSurveyRequest) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *UpdateSurveyRequest) GetFont() string {
	if x != nil {
		return x.Font
	}
	return ""
}

type UpdateSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateSurveyResponse) Reset() {
	*x = UpdateSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSurveyResponse) ProtoMessage() {}

func (x *UpdateSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSurveyResponse.ProtoReflect.Descriptor instead.
func (*UpdateSurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{44}
}

func (x *UpdateSurveyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 问卷详情
type SurveyDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId int64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *SurveyDetailRequest) Reset() {
	*x = SurveyDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyDetailRequest) ProtoMessage() {}

func (x *SurveyDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyDetailRequest.ProtoReflect.Descriptor instead.
func (*SurveyDetailRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{45}
}

func (x *SurveyDetailRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyDetailRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type SurveyDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionList           []*QuestionList              `protobuf:"bytes,1,rep,name=question_list,json=questionList,proto3" json:"question_list,omitempty"`
	SurveyConfig           *SurveyConfig                `protobuf:"bytes,2,opt,name=survey_config,json=surveyConfig,proto3" json:"survey_config,omitempty"`
	QuestionStatisticsData *OuterQuestionStatisticsData `protobuf:"bytes,3,opt,name=question_statistics_data,json=questionStatisticsData,proto3" json:"question_statistics_data,omitempty"`
}

func (x *SurveyDetailResponse) Reset() {
	*x = SurveyDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyDetailResponse) ProtoMessage() {}

func (x *SurveyDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyDetailResponse.ProtoReflect.Descriptor instead.
func (*SurveyDetailResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{46}
}

func (x *SurveyDetailResponse) GetQuestionList() []*QuestionList {
	if x != nil {
		return x.QuestionList
	}
	return nil
}

func (x *SurveyDetailResponse) GetSurveyConfig() *SurveyConfig {
	if x != nil {
		return x.SurveyConfig
	}
	return nil
}

func (x *SurveyDetailResponse) GetQuestionStatisticsData() *OuterQuestionStatisticsData {
	if x != nil {
		return x.QuestionStatisticsData
	}
	return nil
}

type SurveyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   // 问卷ID
	ClientId               int64                   `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id"` // 客户端ID
	Name                   string                  `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`                          // 问卷名称
	IsClosed               int32                   `protobuf:"varint,4,opt,name=is_closed,json=isClosed,proto3" json:"is_closed"` // 是否关闭
	IsPause                int32                   `protobuf:"varint,5,opt,name=is_pause,json=isPause,proto3" json:"is_pause"`    // 是否暂停
	IsPublish              int32                   `protobuf:"varint,6,opt,name=is_publish,json=isPublish,proto3" json:"is_publish"`
	IsModifyUnpublish      int32                   `protobuf:"varint,7,opt,name=is_modify_unpublish,json=isModifyUnpublish,proto3" json:"is_modify_unpublish"`                // 是否允许修改未发布
	IsOpened               int32                   `protobuf:"varint,8,opt,name=is_opened,json=isOpened,proto3" json:"is_opened"`                                             // 是否开放
	Stime                  string                  `protobuf:"bytes,9,opt,name=stime,proto3" json:"stime"`                                                                    // 开始时间
	Etime                  string                  `protobuf:"bytes,10,opt,name=etime,proto3" json:"etime"`                                                                   // 结束时间
	Type                   int32                   `protobuf:"varint,11,opt,name=type,proto3" json:"type"`                                                                    // 类型
	Schema                 string                  `protobuf:"bytes,12,opt,name=schema,proto3" json:"schema"`                                                                 // 问卷架构
	PreviewSchema          string                  `protobuf:"bytes,13,opt,name=preview_schema,json=previewSchema,proto3" json:"preview_schema"`                              // 预览架构
	Settings               string                  `protobuf:"bytes,14,opt,name=settings,proto3" json:"settings"`                                                             // 设置，使用Any类型来存储任意数据结构
	WebSettings            string                  `protobuf:"bytes,15,opt,name=web_settings,json=webSettings,proto3" json:"IsTimeLimit"`                                     // 网络设置
	Languages              string                  `protobuf:"bytes,16,opt,name=languages,proto3" json:"languages"`                                                           // 语言设置，使用Any类型来存储任意数据结构
	HashCode               string                  `protobuf:"bytes,17,opt,name=hash_code,json=hashCode,proto3" json:"hash_code"`                                             // 哈希码
	IsDelete               int32                   `protobuf:"varint,18,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`                                            // 是否删除
	Deltime                string                  `protobuf:"bytes,19,opt,name=deltime,proto3" json:"deltime"`                                                               // 删除时间
	Remark                 string                  `protobuf:"bytes,20,opt,name=remark,proto3" json:"remark"`                                                                 // 备注
	Ctime                  string                  `protobuf:"bytes,21,opt,name=ctime,proto3" json:"ctime"`                                                                   // 创建时间
	Mtime                  string                  `protobuf:"bytes,22,opt,name=mtime,proto3" json:"mtime"`                                                                   // 修改时间
	Creator                string                  `protobuf:"bytes,23,opt,name=creator,proto3" json:"creator"`                                                               // 创建者
	Editor                 string                  `protobuf:"bytes,24,opt,name=editor,proto3" json:"editor"`                                                                 // 编辑者
	IsTimeLimit            bool                    `protobuf:"varint,25,opt,name=is_time_limit,json=isTimeLimit,proto3" json:"is_time_limit"`                                 // 是否有时间限制
	WebPathList            []*WebPath              `protobuf:"bytes,26,rep,name=web_path_list,json=webPathList,proto3" json:"web_path_list"`                                  // 网络路径列表
	Status                 int32                   `protobuf:"varint,27,opt,name=status,proto3" json:"status"`                                                                // 状态
	QuestionStatisticsData *QuestionStatisticsData `protobuf:"bytes,28,opt,name=question_statistics_data,json=questionStatisticsData,proto3" json:"question_statistics_data"` // 问题统计数据，使用Any类型来存储任意数据结构
}

func (x *SurveyConfig) Reset() {
	*x = SurveyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyConfig) ProtoMessage() {}

func (x *SurveyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyConfig.ProtoReflect.Descriptor instead.
func (*SurveyConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{47}
}

func (x *SurveyConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyConfig) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyConfig) GetIsClosed() int32 {
	if x != nil {
		return x.IsClosed
	}
	return 0
}

func (x *SurveyConfig) GetIsPause() int32 {
	if x != nil {
		return x.IsPause
	}
	return 0
}

func (x *SurveyConfig) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *SurveyConfig) GetIsModifyUnpublish() int32 {
	if x != nil {
		return x.IsModifyUnpublish
	}
	return 0
}

func (x *SurveyConfig) GetIsOpened() int32 {
	if x != nil {
		return x.IsOpened
	}
	return 0
}

func (x *SurveyConfig) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *SurveyConfig) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

func (x *SurveyConfig) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SurveyConfig) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *SurveyConfig) GetPreviewSchema() string {
	if x != nil {
		return x.PreviewSchema
	}
	return ""
}

func (x *SurveyConfig) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *SurveyConfig) GetWebSettings() string {
	if x != nil {
		return x.WebSettings
	}
	return ""
}

func (x *SurveyConfig) GetLanguages() string {
	if x != nil {
		return x.Languages
	}
	return ""
}

func (x *SurveyConfig) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *SurveyConfig) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *SurveyConfig) GetDeltime() string {
	if x != nil {
		return x.Deltime
	}
	return ""
}

func (x *SurveyConfig) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *SurveyConfig) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *SurveyConfig) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *SurveyConfig) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *SurveyConfig) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

func (x *SurveyConfig) GetIsTimeLimit() bool {
	if x != nil {
		return x.IsTimeLimit
	}
	return false
}

func (x *SurveyConfig) GetWebPathList() []*WebPath {
	if x != nil {
		return x.WebPathList
	}
	return nil
}

func (x *SurveyConfig) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SurveyConfig) GetQuestionStatisticsData() *QuestionStatisticsData {
	if x != nil {
		return x.QuestionStatisticsData
	}
	return nil
}

type WebPath struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region  string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`                  // 地区或区域名称
	WebPath string `protobuf:"bytes,2,opt,name=web_path,json=webPath,proto3" json:"web_path,omitempty"` // 网络路径
}

func (x *WebPath) Reset() {
	*x = WebPath{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebPath) ProtoMessage() {}

func (x *WebPath) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebPath.ProtoReflect.Descriptor instead.
func (*WebPath) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{48}
}

func (x *WebPath) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *WebPath) GetWebPath() string {
	if x != nil {
		return x.WebPath
	}
	return ""
}

type QuestionBaseConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionTitle *Title `protobuf:"bytes,1,opt,name=questionTitle,proto3" json:"questionTitle,omitempty"`
	QuestionDesc  *Desc  `protobuf:"bytes,2,opt,name=questionDesc,proto3" json:"questionDesc,omitempty"`
	QuestionTip   *Tip   `protobuf:"bytes,3,opt,name=questionTip,proto3" json:"questionTip,omitempty"`
}

func (x *QuestionBaseConfig) Reset() {
	*x = QuestionBaseConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionBaseConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionBaseConfig) ProtoMessage() {}

func (x *QuestionBaseConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionBaseConfig.ProtoReflect.Descriptor instead.
func (*QuestionBaseConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{49}
}

func (x *QuestionBaseConfig) GetQuestionTitle() *Title {
	if x != nil {
		return x.QuestionTitle
	}
	return nil
}

func (x *QuestionBaseConfig) GetQuestionDesc() *Desc {
	if x != nil {
		return x.QuestionDesc
	}
	return nil
}

func (x *QuestionBaseConfig) GetQuestionTip() *Tip {
	if x != nil {
		return x.QuestionTip
	}
	return nil
}

type Title struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *Title) Reset() {
	*x = Title{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Title) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Title) ProtoMessage() {}

func (x *Title) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Title.ProtoReflect.Descriptor instead.
func (*Title) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{50}
}

func (x *Title) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Title) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type Desc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *Desc) Reset() {
	*x = Desc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Desc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Desc) ProtoMessage() {}

func (x *Desc) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Desc.ProtoReflect.Descriptor instead.
func (*Desc) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{51}
}

func (x *Desc) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Desc) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type Tip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *Tip) Reset() {
	*x = Tip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tip) ProtoMessage() {}

func (x *Tip) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tip.ProtoReflect.Descriptor instead.
func (*Tip) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{52}
}

func (x *Tip) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Tip) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type ConfigProps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsQuestion       bool   `protobuf:"varint,1,opt,name=is_question,json=isQuestion,proto3" json:"is_question,omitempty"`
	QuestionType     string `protobuf:"bytes,2,opt,name=question_type,json=questionType,proto3" json:"question_type,omitempty"`
	ValueType        string `protobuf:"bytes,3,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
	UniqueKey        string `protobuf:"bytes,4,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`
	QuestionId       int32  `protobuf:"varint,5,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	StatisticsMethod string `protobuf:"bytes,6,opt,name=statistics_method,json=statisticsMethod,proto3" json:"statistics_method,omitempty"`
	IsAddress        bool   `protobuf:"varint,7,opt,name=is_address,json=isAddress,proto3" json:"is_address,omitempty"`
}

func (x *ConfigProps) Reset() {
	*x = ConfigProps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigProps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigProps) ProtoMessage() {}

func (x *ConfigProps) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigProps.ProtoReflect.Descriptor instead.
func (*ConfigProps) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{53}
}

func (x *ConfigProps) GetIsQuestion() bool {
	if x != nil {
		return x.IsQuestion
	}
	return false
}

func (x *ConfigProps) GetQuestionType() string {
	if x != nil {
		return x.QuestionType
	}
	return ""
}

func (x *ConfigProps) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

func (x *ConfigProps) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *ConfigProps) GetQuestionId() int32 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *ConfigProps) GetStatisticsMethod() string {
	if x != nil {
		return x.StatisticsMethod
	}
	return ""
}

func (x *ConfigProps) GetIsAddress() bool {
	if x != nil {
		return x.IsAddress
	}
	return false
}

type QuestionLogicalConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VisibleSwitch *VisibleSwitch `protobuf:"bytes,1,opt,name=visibleSwitch,proto3" json:"visibleSwitch,omitempty"`
	ConditionDesc string         `protobuf:"bytes,2,opt,name=conditionDesc,proto3" json:"conditionDesc,omitempty"`
	DisplayLogic  *DisplayLogic  `protobuf:"bytes,3,opt,name=displayLogic,proto3" json:"displayLogic,omitempty"`
}

func (x *QuestionLogicalConfig) Reset() {
	*x = QuestionLogicalConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionLogicalConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionLogicalConfig) ProtoMessage() {}

func (x *QuestionLogicalConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionLogicalConfig.ProtoReflect.Descriptor instead.
func (*QuestionLogicalConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{54}
}

func (x *QuestionLogicalConfig) GetVisibleSwitch() *VisibleSwitch {
	if x != nil {
		return x.VisibleSwitch
	}
	return nil
}

func (x *QuestionLogicalConfig) GetConditionDesc() string {
	if x != nil {
		return x.ConditionDesc
	}
	return ""
}

func (x *QuestionLogicalConfig) GetDisplayLogic() *DisplayLogic {
	if x != nil {
		return x.DisplayLogic
	}
	return nil
}

type VisibleSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VisibleSwitch) Reset() {
	*x = VisibleSwitch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisibleSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisibleSwitch) ProtoMessage() {}

func (x *VisibleSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisibleSwitch.ProtoReflect.Descriptor instead.
func (*VisibleSwitch) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{55}
}

type DisplayLogic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentQuestionId string  `protobuf:"bytes,1,opt,name=currentQuestionId,proto3" json:"currentQuestionId,omitempty"`
	Rules             []*Rule `protobuf:"bytes,2,rep,name=rules,proto3" json:"rules,omitempty"`
	Relation          string  `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *DisplayLogic) Reset() {
	*x = DisplayLogic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayLogic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayLogic) ProtoMessage() {}

func (x *DisplayLogic) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayLogic.ProtoReflect.Descriptor instead.
func (*DisplayLogic) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{56}
}

func (x *DisplayLogic) GetCurrentQuestionId() string {
	if x != nil {
		return x.CurrentQuestionId
	}
	return ""
}

func (x *DisplayLogic) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *DisplayLogic) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type RelatedContentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Relation   string  `protobuf:"bytes,1,opt,name=relation,proto3" json:"relation,omitempty"`
	ChooseType string  `protobuf:"bytes,2,opt,name=chooseType,proto3" json:"chooseType,omitempty"`
	Content    []int32 `protobuf:"varint,3,rep,packed,name=content,proto3" json:"content,omitempty"`
}

func (x *RelatedContentConfig) Reset() {
	*x = RelatedContentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelatedContentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedContentConfig) ProtoMessage() {}

func (x *RelatedContentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedContentConfig.ProtoReflect.Descriptor instead.
func (*RelatedContentConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{57}
}

func (x *RelatedContentConfig) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *RelatedContentConfig) GetChooseType() string {
	if x != nil {
		return x.ChooseType
	}
	return ""
}

func (x *RelatedContentConfig) GetContent() []int32 {
	if x != nil {
		return x.Content
	}
	return nil
}

type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key                  string                `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Id                   string                `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	RelatedContentConfig *RelatedContentConfig `protobuf:"bytes,3,opt,name=relatedContentConfig,proto3" json:"relatedContentConfig,omitempty"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{58}
}

func (x *Rule) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Rule) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Rule) GetRelatedContentConfig() *RelatedContentConfig {
	if x != nil {
		return x.RelatedContentConfig
	}
	return nil
}

type QuestionComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeaderContent *HeaderContent `protobuf:"bytes,1,opt,name=headerContent,proto3" json:"headerContent,omitempty"`
	FooterContent *FooterContent `protobuf:"bytes,2,opt,name=FooterContent,proto3" json:"FooterContent,omitempty"`
	Dimension     int32          `protobuf:"varint,3,opt,name=dimension,proto3" json:"dimension,omitempty"`
	StartValue    int32          `protobuf:"varint,4,opt,name=startValue,proto3" json:"startValue,omitempty"`
	Sort          int32          `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *QuestionComponentConfig) Reset() {
	*x = QuestionComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionComponentConfig) ProtoMessage() {}

func (x *QuestionComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionComponentConfig.ProtoReflect.Descriptor instead.
func (*QuestionComponentConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{59}
}

func (x *QuestionComponentConfig) GetHeaderContent() *HeaderContent {
	if x != nil {
		return x.HeaderContent
	}
	return nil
}

func (x *QuestionComponentConfig) GetFooterContent() *FooterContent {
	if x != nil {
		return x.FooterContent
	}
	return nil
}

func (x *QuestionComponentConfig) GetDimension() int32 {
	if x != nil {
		return x.Dimension
	}
	return 0
}

func (x *QuestionComponentConfig) GetStartValue() int32 {
	if x != nil {
		return x.StartValue
	}
	return 0
}

func (x *QuestionComponentConfig) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type HeaderContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *HeaderContent) Reset() {
	*x = HeaderContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeaderContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderContent) ProtoMessage() {}

func (x *HeaderContent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderContent.ProtoReflect.Descriptor instead.
func (*HeaderContent) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{60}
}

func (x *HeaderContent) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *HeaderContent) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type FooterContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *FooterContent) Reset() {
	*x = FooterContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FooterContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FooterContent) ProtoMessage() {}

func (x *FooterContent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FooterContent.ProtoReflect.Descriptor instead.
func (*FooterContent) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{61}
}

func (x *FooterContent) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *FooterContent) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type QuestionSelectConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxSelected          int32           `protobuf:"varint,1,opt,name=maxSelected,proto3" json:"maxSelected,omitempty"`
	SelectOptions        []*SelectOption `protobuf:"bytes,2,rep,name=selectOptions,proto3" json:"selectOptions,omitempty"`
	SelectItemLayout     bool            `protobuf:"varint,3,opt,name=selectItemLayout,proto3" json:"selectItemLayout,omitempty"`
	SelectItemRandomSort bool            `protobuf:"varint,4,opt,name=selectItemRandomSort,proto3" json:"selectItemRandomSort,omitempty"`
}

func (x *QuestionSelectConfig) Reset() {
	*x = QuestionSelectConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionSelectConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionSelectConfig) ProtoMessage() {}

func (x *QuestionSelectConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionSelectConfig.ProtoReflect.Descriptor instead.
func (*QuestionSelectConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{62}
}

func (x *QuestionSelectConfig) GetMaxSelected() int32 {
	if x != nil {
		return x.MaxSelected
	}
	return 0
}

func (x *QuestionSelectConfig) GetSelectOptions() []*SelectOption {
	if x != nil {
		return x.SelectOptions
	}
	return nil
}

func (x *QuestionSelectConfig) GetSelectItemLayout() bool {
	if x != nil {
		return x.SelectItemLayout
	}
	return false
}

func (x *QuestionSelectConfig) GetSelectItemRandomSort() bool {
	if x != nil {
		return x.SelectItemRandomSort
	}
	return false
}

type SelectOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SelectOption) Reset() {
	*x = SelectOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOption) ProtoMessage() {}

func (x *SelectOption) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOption.ProtoReflect.Descriptor instead.
func (*SelectOption) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{63}
}

func (x *SelectOption) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type QuestionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComponentName           string                   `protobuf:"bytes,1,opt,name=componentName,proto3" json:"componentName,omitempty"`
	ComponentTitle          string                   `protobuf:"bytes,2,opt,name=componentTitle,proto3" json:"componentTitle,omitempty"`
	QuestionBaseConfig      *QuestionBaseConfig      `protobuf:"bytes,3,opt,name=questionBaseConfig,proto3" json:"questionBaseConfig,omitempty"`
	ConfigProps             *ConfigProps             `protobuf:"bytes,4,opt,name=configProps,proto3" json:"configProps,omitempty"`
	QuestionLogicalConfig   *QuestionLogicalConfig   `protobuf:"bytes,5,opt,name=questionLogicalConfig,proto3" json:"questionLogicalConfig,omitempty"`
	QuestionComponentConfig *QuestionComponentConfig `protobuf:"bytes,6,opt,name=questionComponentConfig,proto3" json:"questionComponentConfig,omitempty"`
	QuestionSelectConfig    *QuestionSelectConfig    `protobuf:"bytes,7,opt,name=questionSelectConfig,proto3" json:"questionSelectConfig,omitempty"`
	RequestConfig           *RequestConfig           `protobuf:"bytes,8,opt,name=requestConfig,proto3" json:"requestConfig,omitempty"`
	WrapperIndex            int32                    `protobuf:"varint,9,opt,name=wrapperIndex,proto3" json:"wrapperIndex,omitempty"`
}

func (x *QuestionList) Reset() {
	*x = QuestionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionList) ProtoMessage() {}

func (x *QuestionList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionList.ProtoReflect.Descriptor instead.
func (*QuestionList) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{64}
}

func (x *QuestionList) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *QuestionList) GetComponentTitle() string {
	if x != nil {
		return x.ComponentTitle
	}
	return ""
}

func (x *QuestionList) GetQuestionBaseConfig() *QuestionBaseConfig {
	if x != nil {
		return x.QuestionBaseConfig
	}
	return nil
}

func (x *QuestionList) GetConfigProps() *ConfigProps {
	if x != nil {
		return x.ConfigProps
	}
	return nil
}

func (x *QuestionList) GetQuestionLogicalConfig() *QuestionLogicalConfig {
	if x != nil {
		return x.QuestionLogicalConfig
	}
	return nil
}

func (x *QuestionList) GetQuestionComponentConfig() *QuestionComponentConfig {
	if x != nil {
		return x.QuestionComponentConfig
	}
	return nil
}

func (x *QuestionList) GetQuestionSelectConfig() *QuestionSelectConfig {
	if x != nil {
		return x.QuestionSelectConfig
	}
	return nil
}

func (x *QuestionList) GetRequestConfig() *RequestConfig {
	if x != nil {
		return x.RequestConfig
	}
	return nil
}

func (x *QuestionList) GetWrapperIndex() int32 {
	if x != nil {
		return x.WrapperIndex
	}
	return 0
}

type RequestConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionUniqueKey string       `protobuf:"bytes,1,opt,name=question_unique_key,json=questionUniqueKey,proto3" json:"question_unique_key,omitempty"`
	ConfigProps       *ConfigProps `protobuf:"bytes,2,opt,name=config_props,json=configProps,proto3" json:"config_props,omitempty"`
	OptionValue       string       `protobuf:"bytes,3,opt,name=option_value,json=optionValue,proto3" json:"option_value,omitempty"`
}

func (x *RequestConfig) Reset() {
	*x = RequestConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestConfig) ProtoMessage() {}

func (x *RequestConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestConfig.ProtoReflect.Descriptor instead.
func (*RequestConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{65}
}

func (x *RequestConfig) GetQuestionUniqueKey() string {
	if x != nil {
		return x.QuestionUniqueKey
	}
	return ""
}

func (x *RequestConfig) GetConfigProps() *ConfigProps {
	if x != nil {
		return x.ConfigProps
	}
	return nil
}

func (x *RequestConfig) GetOptionValue() string {
	if x != nil {
		return x.OptionValue
	}
	return ""
}

type QuestionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestConfig *RequestConfig `protobuf:"bytes,1,opt,name=request_config,json=requestConfig,proto3" json:"request_config,omitempty"`
	Success       bool           `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *QuestionConfig) Reset() {
	*x = QuestionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionConfig) ProtoMessage() {}

func (x *QuestionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionConfig.ProtoReflect.Descriptor instead.
func (*QuestionConfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{66}
}

func (x *QuestionConfig) GetRequestConfig() *RequestConfig {
	if x != nil {
		return x.RequestConfig
	}
	return nil
}

func (x *QuestionConfig) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type PublishSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *PublishSurveyRequest) Reset() {
	*x = PublishSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishSurveyRequest) ProtoMessage() {}

func (x *PublishSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishSurveyRequest.ProtoReflect.Descriptor instead.
func (*PublishSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{67}
}

func (x *PublishSurveyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PublishSurveyRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type PublishSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *PublishSurveyResponse) Reset() {
	*x = PublishSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishSurveyResponse) ProtoMessage() {}

func (x *PublishSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishSurveyResponse.ProtoReflect.Descriptor instead.
func (*PublishSurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{68}
}

func (x *PublishSurveyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type ShowSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *ShowSurveyRequest) Reset() {
	*x = ShowSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowSurveyRequest) ProtoMessage() {}

func (x *ShowSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowSurveyRequest.ProtoReflect.Descriptor instead.
func (*ShowSurveyRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{69}
}

func (x *ShowSurveyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ShowSurveyRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type ShowSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data    *Survey `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Success bool    `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ShowSurveyResponse) Reset() {
	*x = ShowSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShowSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShowSurveyResponse) ProtoMessage() {}

func (x *ShowSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShowSurveyResponse.ProtoReflect.Descriptor instead.
func (*ShowSurveyResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{70}
}

func (x *ShowSurveyResponse) GetData() *Survey {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ShowSurveyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SurveyInputMethodListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页码，默认1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，默认10
	PageSize      int32          `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SurveyId      int64          `protobuf:"varint,3,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	RequestConfig *RequestConfig `protobuf:"bytes,4,opt,name=request_config,json=requestConfig,proto3" json:"request_config,omitempty"`
	ClientId      int64          `protobuf:"varint,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 过滤空
	FilterNullValue int32 `protobuf:"varint,6,opt,name=filter_null_value,json=filterNullValue,proto3" json:"filter_null_value,omitempty"`
}

func (x *SurveyInputMethodListRequest) Reset() {
	*x = SurveyInputMethodListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyInputMethodListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyInputMethodListRequest) ProtoMessage() {}

func (x *SurveyInputMethodListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyInputMethodListRequest.ProtoReflect.Descriptor instead.
func (*SurveyInputMethodListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{71}
}

func (x *SurveyInputMethodListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SurveyInputMethodListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SurveyInputMethodListRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyInputMethodListRequest) GetRequestConfig() *RequestConfig {
	if x != nil {
		return x.RequestConfig
	}
	return nil
}

func (x *SurveyInputMethodListRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyInputMethodListRequest) GetFilterNullValue() int32 {
	if x != nil {
		return x.FilterNullValue
	}
	return 0
}

type SurveyInputMethodListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List        []*InputMethodListResponseData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCounts int32                          `protobuf:"varint,2,opt,name=totalCounts,proto3" json:"totalCounts,omitempty"`
	CurrentPage int32                          `protobuf:"varint,3,opt,name=currentPage,proto3" json:"currentPage,omitempty"`
}

func (x *SurveyInputMethodListResponse) Reset() {
	*x = SurveyInputMethodListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyInputMethodListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyInputMethodListResponse) ProtoMessage() {}

func (x *SurveyInputMethodListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyInputMethodListResponse.ProtoReflect.Descriptor instead.
func (*SurveyInputMethodListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{72}
}

func (x *SurveyInputMethodListResponse) GetList() []*InputMethodListResponseData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyInputMethodListResponse) GetTotalCounts() int32 {
	if x != nil {
		return x.TotalCounts
	}
	return 0
}

func (x *SurveyInputMethodListResponse) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

type InputMethodListResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设备ID
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id" gorm:"type:varchar;comment:设备ID"`
	// 记录ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id" gorm:"type:varchar;comment:记录ID"`
	// IP地址
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip" gorm:"type:varchar;comment:IP地址"`
	// 用户openid
	Openid string `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid" gorm:"type:varchar;comment:用户openid"`
	// 题目选项
	Option string `protobuf:"bytes,5,opt,name=option,proto3" json:"option" gorm:"type:varchar;comment:题目选项"`
	// 题目ID
	Question string `protobuf:"bytes,6,opt,name=question,proto3" json:"question" gorm:"type:varchar;comment:题目ID"`
	// 用户roleid
	RoleId string `protobuf:"bytes,7,opt,name=role_id,json=roleId,proto3" json:"role_id" gorm:"type:varchar;comment:用户roleid"`
	// 问卷记录ID
	SurveyRecordId int64 `protobuf:"varint,8,opt,name=survey_record_id,json=surveyRecordId,proto3" json:"survey_record_id" gorm:"type:varchar;comment:问卷记录ID"`
	// 用户输入的文本
	Text string `protobuf:"bytes,9,opt,name=text,proto3" json:"text" gorm:"type:varchar;comment:用户输入的文本"`
}

func (x *InputMethodListResponseData) Reset() {
	*x = InputMethodListResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputMethodListResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputMethodListResponseData) ProtoMessage() {}

func (x *InputMethodListResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputMethodListResponseData.ProtoReflect.Descriptor instead.
func (*InputMethodListResponseData) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{73}
}

func (x *InputMethodListResponseData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *InputMethodListResponseData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InputMethodListResponseData) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *InputMethodListResponseData) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *InputMethodListResponseData) GetOption() string {
	if x != nil {
		return x.Option
	}
	return ""
}

func (x *InputMethodListResponseData) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *InputMethodListResponseData) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *InputMethodListResponseData) GetSurveyRecordId() int64 {
	if x != nil {
		return x.SurveyRecordId
	}
	return 0
}

func (x *InputMethodListResponseData) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type OuterQuestionStatisticsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidAnswerTotal int32     `protobuf:"varint,1,opt,name=valid_answer_total,json=validAnswerTotal,proto3" json:"valid_answer_total,omitempty"`
	ValidUserTotal   int32     `protobuf:"varint,2,opt,name=valid_user_total,json=validUserTotal,proto3" json:"valid_user_total,omitempty"`
	Detail           []*Detail `protobuf:"bytes,3,rep,name=detail,proto3" json:"detail,omitempty"`
}

func (x *OuterQuestionStatisticsData) Reset() {
	*x = OuterQuestionStatisticsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OuterQuestionStatisticsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OuterQuestionStatisticsData) ProtoMessage() {}

func (x *OuterQuestionStatisticsData) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OuterQuestionStatisticsData.ProtoReflect.Descriptor instead.
func (*OuterQuestionStatisticsData) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{74}
}

func (x *OuterQuestionStatisticsData) GetValidAnswerTotal() int32 {
	if x != nil {
		return x.ValidAnswerTotal
	}
	return 0
}

func (x *OuterQuestionStatisticsData) GetValidUserTotal() int32 {
	if x != nil {
		return x.ValidUserTotal
	}
	return 0
}

func (x *OuterQuestionStatisticsData) GetDetail() []*Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueKey   string          `protobuf:"bytes,1,opt,name=uniqueKey,proto3" json:"uniqueKey,omitempty"`
	Count       string          `protobuf:"bytes,2,opt,name=count,proto3" json:"count,omitempty"`
	SelectCount string          `protobuf:"bytes,3,opt,name=selectCount,proto3" json:"selectCount,omitempty"`
	Detail      []*OptionDetail `protobuf:"bytes,4,rep,name=detail,proto3" json:"detail,omitempty"`
}

func (x *Detail) Reset() {
	*x = Detail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Detail) ProtoMessage() {}

func (x *Detail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Detail.ProtoReflect.Descriptor instead.
func (*Detail) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{75}
}

func (x *Detail) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *Detail) GetCount() string {
	if x != nil {
		return x.Count
	}
	return ""
}

func (x *Detail) GetSelectCount() string {
	if x != nil {
		return x.SelectCount
	}
	return ""
}

func (x *Detail) GetDetail() []*OptionDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type OptionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value         string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Label         *Label `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	ImagePosition int32  `protobuf:"varint,3,opt,name=image_position,json=imagePosition,proto3" json:"image_position,omitempty"`
	BlankFill     string `protobuf:"bytes,4,opt,name=blank_fill,json=blankFill,proto3" json:"blank_fill,omitempty"`
	Image         *Image `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	Count         int32  `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	Proportion    string `protobuf:"bytes,7,opt,name=proportion,proto3" json:"proportion,omitempty"`
}

func (x *OptionDetail) Reset() {
	*x = OptionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionDetail) ProtoMessage() {}

func (x *OptionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionDetail.ProtoReflect.Descriptor instead.
func (*OptionDetail) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{76}
}

func (x *OptionDetail) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *OptionDetail) GetLabel() *Label {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *OptionDetail) GetImagePosition() int32 {
	if x != nil {
		return x.ImagePosition
	}
	return 0
}

func (x *OptionDetail) GetBlankFill() string {
	if x != nil {
		return x.BlankFill
	}
	return ""
}

func (x *OptionDetail) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *OptionDetail) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *OptionDetail) GetProportion() string {
	if x != nil {
		return x.Proportion
	}
	return ""
}

type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	LanguKey string `protobuf:"bytes,2,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{77}
}

func (x *Label) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Label) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguKey string `protobuf:"bytes,1,opt,name=languKey,proto3" json:"languKey,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{78}
}

func (x *Image) GetLanguKey() string {
	if x != nil {
		return x.LanguKey
	}
	return ""
}

type SurveyOverwriteSendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷ID
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyOverwriteSendReq) Reset() {
	*x = SurveyOverwriteSendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyOverwriteSendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyOverwriteSendReq) ProtoMessage() {}

func (x *SurveyOverwriteSendReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyOverwriteSendReq.ProtoReflect.Descriptor instead.
func (*SurveyOverwriteSendReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{79}
}

func (x *SurveyOverwriteSendReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyOverwriteSendReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyOverwriteSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷
	Survey string `protobuf:"bytes,2,opt,name=survey,proto3" json:"survey,omitempty"`
}

func (x *SurveyOverwriteSyncReq) Reset() {
	*x = SurveyOverwriteSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_conf_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyOverwriteSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyOverwriteSyncReq) ProtoMessage() {}

func (x *SurveyOverwriteSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_conf_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyOverwriteSyncReq.ProtoReflect.Descriptor instead.
func (*SurveyOverwriteSyncReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_conf_proto_rawDescGZIP(), []int{80}
}

func (x *SurveyOverwriteSyncReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyOverwriteSyncReq) GetSurvey() string {
	if x != nil {
		return x.Survey
	}
	return ""
}

var File_proto_survey_conf_proto protoreflect.FileDescriptor

var file_proto_survey_conf_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x72, 0x61, 0x77, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdb, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x43, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2,
	0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06,
	0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x67, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22,
	0x4b, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41,
	0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x1a,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x1a, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x22, 0x6b, 0x0a, 0x11, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x88,
	0x01, 0x0a, 0x12, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x93, 0x01, 0x0a, 0x16, 0x53, 0x79,
	0x6e, 0x63, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0x8b, 0x04, 0x0a, 0x10, 0x49, 0x6d, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x40, 0x0a, 0x0d, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0d, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x36, 0x0a, 0x08,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x52, 0x61, 0x77, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6f, 0x6e, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x6f, 0x0a,
	0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2,
	0x41, 0x01, 0x02, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x56,
	0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x07, 0x64,
	0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x49, 0x0a, 0x0d, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x20, 0x0a, 0x0e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x87, 0x16, 0x0a, 0x06, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x4c,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x3c, 0xd2, 0xa7, 0x86, 0x07,
	0x37, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x3b, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x20, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x20, 0x30, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0xe4, 0xb8, 0xbb, 0xe9, 0x94, 0xae, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x52, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3e, 0xd2,
	0xa7, 0x86, 0x07, 0x2b, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xd2,
	0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x6b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x4e, 0xd2, 0xa7, 0x86, 0x07, 0x49, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x38, 0x29, 0x3b, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe6, 0x89, 0x93, 0xe5, 0xbc, 0x80, 0xef, 0xbc, 0x9a, 0xef, 0xbc, 0x88, 0x30, 0x3a,
	0x20, 0xe6, 0x89, 0x93, 0xe5, 0xbc, 0x80, 0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe5, 0x85, 0xb3,
	0xe9, 0x97, 0xad, 0xef, 0xbc, 0x89, 0x52, 0x08, 0x69, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x12, 0x6c, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x51, 0xd2, 0xa7, 0x86, 0x07, 0x4c, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79,
	0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9a,
	0x82, 0xe5, 0x81, 0x9c, 0xef, 0xbc, 0x9a, 0xef, 0xbc, 0x88, 0x30, 0x3a, 0x20, 0xe4, 0xb8, 0x8d,
	0xe6, 0x9a, 0x82, 0xe5, 0x81, 0x9c, 0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe6, 0x9a, 0x82, 0xe5,
	0x81, 0x9c, 0xef, 0xbc, 0x89, 0x52, 0x07, 0x69, 0x73, 0x50, 0x61, 0x75, 0x73, 0x65, 0x12, 0x6e,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x4f, 0xd2, 0xa7, 0x86, 0x07, 0x4a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79,
	0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0x91, 0xe5,
	0xb8, 0x83, 0xef, 0xbc, 0x88, 0x30, 0x3a, 0x20, 0xe6, 0x9c, 0xaa, 0xe5, 0x8f, 0x91, 0xe5, 0xb8,
	0x83, 0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83,
	0xef, 0xbc, 0x89, 0x52, 0x09, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x7f,
	0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x6e, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x4f, 0xd2, 0xa7, 0x86,
	0x07, 0x4a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79,
	0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe6, 0x9c,
	0xaa, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xef, 0xbc, 0x88, 0x30, 0x3a, 0x20, 0xe6, 0x97, 0xa0,
	0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe6, 0x9c, 0x89, 0xef, 0xbc, 0x89, 0x52, 0x11, 0x69, 0x73,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x6e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12,
	0x78, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x5b, 0xd2, 0xa7, 0x86, 0x07, 0x56, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79,
	0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xbc, 0x80, 0xe5,
	0x90, 0xaf, 0xe8, 0xbf, 0x87, 0xe7, 0xad, 0x94, 0xe9, 0xa2, 0x98, 0xef, 0xbc, 0x88, 0x30, 0x3a,
	0x20, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0x90, 0xaf, 0xe8, 0xbf, 0x87, 0xef, 0xbc, 0x8c,
	0x31, 0x3a, 0x20, 0xe5, 0xbc, 0x80, 0xe5, 0x90, 0xaf, 0xe8, 0xbf, 0x87, 0xef, 0xbc, 0x89, 0x52,
	0x08, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x56, 0x0a, 0x05, 0x73, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x40, 0xd2, 0xa7, 0x86, 0x07, 0x2c, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe5,
	0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xd2, 0xa7, 0x86, 0x07, 0x0a,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x56, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x40, 0xd2, 0xa7, 0x86, 0x07, 0x2c, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65,
	0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42, 0x38, 0xd2, 0xa7, 0x86, 0x07, 0x25, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x38, 0x29, 0x3b, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x51, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0xd2, 0xa7, 0x86, 0x07,
	0x1e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x6a, 0x73, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xd2,
	0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x5b, 0x0a, 0x0c, 0x77,
	0x65, 0x62, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x38, 0xd2, 0xa7, 0x86, 0x07, 0x33, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x43, 0xe7, 0xab, 0xaf, 0xe7, 0x94, 0xa8, 0xe5,
	0x88, 0xb0, 0xe7, 0x9a, 0x84, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x0b, 0x77, 0x65, 0x62,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x59, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3b, 0xd2, 0xa7, 0x86,
	0x07, 0x23, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63,
	0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xaf, 0xad, 0xe8,
	0xa8, 0x80, 0xe5, 0x8c, 0x85, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0xd2, 0xa7, 0x86, 0x07, 0x27, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x69, 0x64, 0x2d,
	0x68, 0x61, 0x73, 0x68, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x6c,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x4f, 0xd2, 0xa7, 0x86, 0x07, 0x4a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x88, 0xa0, 0xe9, 0x99,
	0xa4, 0xef, 0xbc, 0x88, 0x30, 0x3a, 0x20, 0xe6, 0x9c, 0xaa, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4,
	0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe5, 0xb7, 0xb2, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef,
	0xbc, 0x89, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x07,
	0x64, 0x65, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3c, 0xd2,
	0xa7, 0x86, 0x07, 0x26, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe5, 0x88,
	0xa0, 0xe9, 0x99, 0xa4, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x64, 0x65, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x5f, 0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x42, 0x42, 0xd2, 0xa7, 0x86, 0x07, 0x2a, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x65, 0x78, 0x74, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe4, 0xb8, 0xad, 0xe6, 0x96, 0x87, 0x2f, 0xe8, 0x8b, 0xb1, 0xe6,
	0x96, 0x87, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x6b, 0x65, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x57, 0x0a, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x43, 0xd2, 0xa7, 0x86, 0x07, 0x30, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74,
	0x79, 0x70, 0x65, 0x3a, 0x74, 0x65, 0x78, 0x74, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x3a, 0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0x20, 0x74, 0x74, 0x66, 0x20, 0xe7, 0x9a, 0x84, 0x20,
	0x6f, 0x73, 0x73, 0x20, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x66, 0x6f, 0x6e, 0x74, 0x52, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x12, 0x4d,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35,
	0xd2, 0xa7, 0x86, 0x07, 0x20, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76,
	0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe5,
	0xa4, 0x87, 0xe6, 0xb3, 0xa8, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x46, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0xe2, 0x41,
	0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x14, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x0e,
	0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x61, 0x75, 0x74, 0x6f, 0x43, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x14, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x61, 0x75, 0x74,
	0x6f, 0x4d, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x57, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3d,
	0xd2, 0xa7, 0x86, 0x07, 0x27, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76,
	0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe4, 0xba, 0xba, 0xd2, 0xa7, 0x86, 0x07,
	0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x5a, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x42, 0xd2, 0xa7, 0x86, 0x07, 0x2d, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x36,
	0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x9c, 0x80, 0xe8, 0xbf,
	0x91, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74,
	0x6f, 0x72, 0x12, 0x46, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x42, 0x22, 0xd2, 0xa7, 0x86, 0x07, 0x06,
	0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x12, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x69, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x0b, 0x69,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x63, 0x0a, 0x17, 0x61, 0x6c,
	0x6c, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2c, 0xd2, 0xa7, 0x86,
	0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x1c, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x1b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07,
	0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x23, 0xd2, 0xa7,
	0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x75, 0x69,
	0x64, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x55, 0x69, 0x64, 0x12,
	0x99, 0x01, 0x0a, 0x18, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x1d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x16, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x69, 0x0a, 0x0d, 0x77,
	0x65, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x20, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x57, 0x65,
	0x62, 0x50, 0x61, 0x74, 0x68, 0x42, 0x22, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d,
	0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x12, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x77, 0x65, 0x62, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x50, 0x61,
	0x74, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x77, 0x0a,
	0x16, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x3a,
	0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x76, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x4a,
	0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x83, 0x04, 0x0a, 0x07, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x50, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59,
	0x0a, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x6f, 0x6e,
	0x65, 0x49, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x7a, 0x6f, 0x6e, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x53, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x66, 0x6f, 0x6f, 0x74,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x83, 0x03, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0f, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a,
	0x0a, 0x0c, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x66, 0x6f,
	0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xec, 0x02, 0x0a, 0x0e, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x74, 0x69, 0x6d,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c,
	0x69, 0x73, 0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x5f, 0x0a, 0x0f, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x47, 0x0a, 0x11, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x22,
	0xf4, 0x01, 0x0a, 0x0a, 0x47, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26,
	0x0a, 0x0e, 0x69, 0x73, 0x47, 0x69, 0x76, 0x65, 0x4f, 0x75, 0x74, 0x42, 0x79, 0x43, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x47, 0x69, 0x76, 0x65, 0x4f, 0x75,
	0x74, 0x42, 0x79, 0x43, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x69, 0x76, 0x65, 0x4f, 0x75,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x76,
	0x65, 0x4f, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x50, 0x72, 0x65, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4a, 0x0a, 0x0c, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x30, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x48, 0x65, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x20, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6b, 0x0a,
	0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x75,
	0x74, 0x6f, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x34, 0x0a, 0x0c, 0x46, 0x6f,
	0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x6d, 0x0a, 0x0c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x69, 0x74, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x69, 0x74, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x43, 0x0a, 0x0a, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x49, 0x0a, 0x09, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x80, 0x03, 0x0a, 0x06, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x4c, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x4d, 0x61, 0x70,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x0d,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x4f, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x72, 0x65, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x0e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x72, 0x65, 0x65, 0x12, 0x32,
	0x0a, 0x04, 0x69, 0x31, 0x38, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x49, 0x31, 0x38, 0x4e, 0x52, 0x04, 0x69, 0x31,
	0x38, 0x6e, 0x12, 0x38, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x04,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x06, 0x0a,
	0x04, 0x49, 0x31, 0x38, 0x4e, 0x22, 0x83, 0x02, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x64, 0x65, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x76, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x38, 0x0a, 0x08, 0x4d,
	0x65, 0x74, 0x61, 0x54, 0x72, 0x65, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x72, 0x22, 0xdf, 0x01, 0x0a, 0x0e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x6b,
	0x69, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x73, 0x68, 0x6f, 0x77, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x46, 0x0a, 0x20, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x6e, 0x5f, 0x62,
	0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x6e, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x6f, 0x0a, 0x0a, 0x53, 0x6b, 0x69, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x5f, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x6b, 0x69, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a,
	0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xca, 0x03, 0x0a, 0x0d, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x52, 0x0a, 0x10, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e,
	0x62, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x45,
	0x0a, 0x0b, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x6b, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x73, 0x6b, 0x69, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x54, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x13, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x45, 0x0a, 0x0b, 0x67, 0x69, 0x66, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x69,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x49,
	0x73, 0x49, 0x6e, 0x69, 0x74, 0x22, 0xc6, 0x04, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x54,
	0x72, 0x65, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x70, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a,
	0x12, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x4d, 0x61, 0x69, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x77,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x53,
	0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x73, 0x68,
	0x6f, 0x77, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6b, 0x69, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x46, 0x0a, 0x20, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x6e,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x61, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x6e, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x51, 0x0a, 0x0f, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73,
	0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x1b,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x18, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x49, 0x73, 0x49, 0x6e, 0x69,
	0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x72, 0x6f,
	0x70, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x72, 0x6f, 0x70,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x89,
	0x03, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x36,
	0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x54, 0x72, 0x65, 0x65,
	0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x43, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65,
	0x6e, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65,
	0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x22, 0x8b, 0x01, 0x0a, 0x06, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x38,
	0x0a, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x04, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x70,
	0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x70, 0x6d, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x6c, 0x0a, 0x06, 0x4c, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x05, 0x70, 0x72, 0x6f,
	0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x52,
	0x05, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x22, 0x35, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x4c,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe7, 0x01,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x10, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47, 0x09, 0x69, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0xf0, 0x3f, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12,
	0x22, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x6f, 0x6e, 0x74, 0x22, 0x30, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x5b, 0x0a, 0x13, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa1, 0x02, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0d,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6f, 0x0a, 0x18, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4f, 0x75, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x16, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x22, 0xbd, 0x0b, 0x0a, 0x0c, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13,
	0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xd2, 0xa7, 0x86,
	0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x69, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x52, 0x08, 0x69, 0x73, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x12, 0x2d, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x12, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x52, 0x07, 0x69, 0x73, 0x50, 0x61, 0x75,
	0x73, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x09, 0x69, 0x73,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x4d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x6e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x1d, 0xd2, 0xa7, 0x86, 0x07, 0x18, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x6e, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x52, 0x11, 0x69, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x55, 0x6e, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x30, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65,
	0x6e, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x52, 0x08,
	0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f,
	0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x52,
	0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x74, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07,
	0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xd2, 0xa7,
	0x86, 0x07, 0x13, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xd2, 0xa7, 0x86,
	0x07, 0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x49, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x31, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x08, 0x69, 0x73,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x25, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7,
	0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xd2, 0xa7,
	0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74,
	0x6f, 0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74,
	0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0xd2, 0xa7, 0x86, 0x07, 0x12,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x0b, 0x69, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x5e, 0x0a, 0x0d, 0x77, 0x65, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x57, 0x65, 0x62, 0x50, 0x61, 0x74, 0x68, 0x42, 0x17, 0xd2, 0xa7, 0x86, 0x07, 0x12,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x77, 0x65, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x50, 0x61, 0x74, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8e, 0x01, 0x0a, 0x18, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x22,
	0xd2, 0xa7, 0x86, 0x07, 0x1d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x16, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x22, 0x3c, 0x0a, 0x07, 0x57, 0x65,
	0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x77, 0x65, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x77, 0x65, 0x62, 0x50, 0x61, 0x74, 0x68, 0x22, 0xe0, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x45, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x52, 0x0c, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3f, 0x0a, 0x0b, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x54, 0x69, 0x70, 0x52, 0x0b,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x70, 0x22, 0x39, 0x0a, 0x05, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x22, 0x38, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79,
	0x22, 0x37, 0x0a, 0x03, 0x54, 0x69, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x22, 0xfe, 0x01, 0x0a, 0x0b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b,
	0x0a, 0x11, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xd8, 0x01, 0x0a, 0x15, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x4a, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4c, 0x6f, 0x67, 0x69, 0x63, 0x22, 0x0f, 0x0a, 0x0d, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x8e, 0x01, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x14, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x62, 0x0a, 0x14, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x14,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0x89, 0x02, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x4d, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x4d, 0x0a, 0x0d, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x0d, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x22, 0x41, 0x0a, 0x0d, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x4b, 0x65, 0x79, 0x22, 0x41, 0x0a, 0x0d, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x22, 0xe6, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x12, 0x4c, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x32, 0x0a, 0x14, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x53,
	0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x53, 0x6f, 0x72, 0x74, 0x22,
	0x24, 0x0a, 0x0c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xae, 0x05, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x5c, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x47, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x70,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61,
	0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x12, 0x65, 0x0a, 0x15, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x15, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x6b, 0x0a, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x62, 0x0a, 0x14, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x14, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xac, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2e, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x50, 0x72, 0x6f, 0x70, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f,
	0x70, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x7a, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4e, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0x43, 0x0a, 0x14, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x15, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x46, 0x0a, 0x11, 0x53, 0x68, 0x6f,
	0x77, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x64, 0x0a, 0x12, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x1c, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41,
	0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x4e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xae, 0x01, 0x0a, 0x1d,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x22, 0xa3, 0x06, 0x0a,
	0x1b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x3a, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a,
	0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x33, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xd2,
	0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x70, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x53, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x3b, 0xd2, 0xa7, 0x86, 0x07, 0x26, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a,
	0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07,
	0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x12, 0x53, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x3b, 0xd2, 0xa7, 0x86, 0x07, 0x26, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0xe9, 0x80, 0x89, 0xe9, 0xa1,
	0xb9, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x08, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0xd2, 0xa7, 0x86,
	0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63,
	0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0xa2, 0x98, 0xe7,
	0x9b, 0xae, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x55, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x3c, 0xd2, 0xa7, 0x86, 0x07, 0x26, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x72, 0x6f, 0x6c, 0x65, 0x69, 0x64, 0xd2, 0xa7,
	0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x52,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x71, 0x0a, 0x10, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x47, 0xd2, 0xa7, 0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x49, 0x44,
	0xd2, 0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x52, 0x0e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x42, 0xd2, 0xa7, 0x86, 0x07, 0x2f, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8,
	0xbe, 0x93, 0xe5, 0x85, 0xa5, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xd2, 0xa7,
	0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x74, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x22, 0xaf, 0x01, 0x0a, 0x1b, 0x4f, 0x75, 0x74, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x28, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x38, 0x0a, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x22, 0x9e, 0x01, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x8e, 0x02, 0x0a, 0x0c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c,
	0x61, 0x6e, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x6c, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x4b, 0x65,
	0x79, 0x22, 0x23, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x4b, 0x65, 0x79, 0x22, 0x5e, 0x0a, 0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x06, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x06, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_conf_proto_rawDescOnce sync.Once
	file_proto_survey_conf_proto_rawDescData = file_proto_survey_conf_proto_rawDesc
)

func file_proto_survey_conf_proto_rawDescGZIP() []byte {
	file_proto_survey_conf_proto_rawDescOnce.Do(func() {
		file_proto_survey_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_conf_proto_rawDescData)
	})
	return file_proto_survey_conf_proto_rawDescData
}

var file_proto_survey_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 81)
var file_proto_survey_conf_proto_goTypes = []any{
	(*SurveyListRequest)(nil),             // 0: papegames.sparrow.survey.SurveyListRequest
	(*SurveyListResponse)(nil),            // 1: papegames.sparrow.survey.SurveyListResponse
	(*SurveyPreviewReq)(nil),              // 2: papegames.sparrow.survey.SurveyPreviewReq
	(*SurveyRecordConfDetailsReq)(nil),    // 3: papegames.sparrow.survey.SurveyRecordConfDetailsReq
	(*SurveyRecordConfDetailsRes)(nil),    // 4: papegames.sparrow.survey.SurveyRecordConfDetailsRes
	(*SyncSurveyRequest)(nil),             // 5: papegames.sparrow.survey.SyncSurveyRequest
	(*SyncSurveyResponse)(nil),            // 6: papegames.sparrow.survey.SyncSurveyResponse
	(*SyncSurveyResponseData)(nil),        // 7: papegames.sparrow.survey.SyncSurveyResponseData
	(*ImpSurveyRequest)(nil),              // 8: papegames.sparrow.survey.ImpSurveyRequest
	(*SurveySetStatusRequest)(nil),        // 9: papegames.sparrow.survey.SurveySetStatusRequest
	(*SurveyDelRequest)(nil),              // 10: papegames.sparrow.survey.SurveyDelRequest
	(*SurveyRequest)(nil),                 // 11: papegames.sparrow.survey.SurveyRequest
	(*SurveyResponse)(nil),                // 12: papegames.sparrow.survey.SurveyResponse
	(*Survey)(nil),                        // 13: papegames.sparrow.survey.Survey
	(*QuestionStatisticsData)(nil),        // 14: papegames.sparrow.survey.QuestionStatisticsData
	(*SurveyRecycleListRes)(nil),          // 15: papegames.sparrow.survey.SurveyRecycleListRes
	(*RecoverSurveyRecycleReq)(nil),       // 16: papegames.sparrow.survey.RecoverSurveyRecycleReq
	(*Setting)(nil),                       // 17: papegames.sparrow.survey.Setting
	(*WebSetting)(nil),                    // 18: papegames.sparrow.survey.WebSetting
	(*BaseRuleConfig)(nil),                // 19: papegames.sparrow.survey.BaseRuleConfig
	(*TimeLimitConfig)(nil),               // 20: papegames.sparrow.survey.TimeLimitConfig
	(*AnswerTimesConfig)(nil),             // 21: papegames.sparrow.survey.AnswerTimesConfig
	(*GiftConfig)(nil),                    // 22: papegames.sparrow.survey.GiftConfig
	(*RedeemConfig)(nil),                  // 23: papegames.sparrow.survey.RedeemConfig
	(*PreAwardConfig)(nil),                // 24: papegames.sparrow.survey.PreAwardConfig
	(*AnswerLimitConfig)(nil),             // 25: papegames.sparrow.survey.AnswerLimitConfig
	(*MaterialsConfig)(nil),               // 26: papegames.sparrow.survey.MaterialsConfig
	(*FooterConfig)(nil),                  // 27: papegames.sparrow.survey.FooterConfig
	(*SourceConfig)(nil),                  // 28: papegames.sparrow.survey.SourceConfig
	(*Agreement)(nil),                     // 29: papegames.sparrow.survey.Agreement
	(*Schema)(nil),                        // 30: papegames.sparrow.survey.Schema
	(*I18N)(nil),                          // 31: papegames.sparrow.survey.I18N
	(*ComponentMap)(nil),                  // 32: papegames.sparrow.survey.ComponentMap
	(*MetaTree)(nil),                      // 33: papegames.sparrow.survey.MetaTree
	(*SurveySettings)(nil),                // 34: papegames.sparrow.survey.SurveySettings
	(*SkinConfig)(nil),                    // 35: papegames.sparrow.survey.SkinConfig
	(*SettingsProps)(nil),                 // 36: papegames.sparrow.survey.SettingsProps
	(*PropsTree)(nil),                     // 37: papegames.sparrow.survey.PropsTree
	(*ComponentTree)(nil),                 // 38: papegames.sparrow.survey.ComponentTree
	(*Config)(nil),                        // 39: papegames.sparrow.survey.Config
	(*Meta)(nil),                          // 40: papegames.sparrow.survey.Meta
	(*Layout)(nil),                        // 41: papegames.sparrow.survey.Layout
	(*PropsLayout)(nil),                   // 42: papegames.sparrow.survey.PropsLayout
	(*UpdateSurveyRequest)(nil),           // 43: papegames.sparrow.survey.UpdateSurveyRequest
	(*UpdateSurveyResponse)(nil),          // 44: papegames.sparrow.survey.UpdateSurveyResponse
	(*SurveyDetailRequest)(nil),           // 45: papegames.sparrow.survey.SurveyDetailRequest
	(*SurveyDetailResponse)(nil),          // 46: papegames.sparrow.survey.SurveyDetailResponse
	(*SurveyConfig)(nil),                  // 47: papegames.sparrow.survey.SurveyConfig
	(*WebPath)(nil),                       // 48: papegames.sparrow.survey.WebPath
	(*QuestionBaseConfig)(nil),            // 49: papegames.sparrow.survey.QuestionBaseConfig
	(*Title)(nil),                         // 50: papegames.sparrow.survey.Title
	(*Desc)(nil),                          // 51: papegames.sparrow.survey.Desc
	(*Tip)(nil),                           // 52: papegames.sparrow.survey.Tip
	(*ConfigProps)(nil),                   // 53: papegames.sparrow.survey.ConfigProps
	(*QuestionLogicalConfig)(nil),         // 54: papegames.sparrow.survey.QuestionLogicalConfig
	(*VisibleSwitch)(nil),                 // 55: papegames.sparrow.survey.VisibleSwitch
	(*DisplayLogic)(nil),                  // 56: papegames.sparrow.survey.DisplayLogic
	(*RelatedContentConfig)(nil),          // 57: papegames.sparrow.survey.RelatedContentConfig
	(*Rule)(nil),                          // 58: papegames.sparrow.survey.Rule
	(*QuestionComponentConfig)(nil),       // 59: papegames.sparrow.survey.QuestionComponentConfig
	(*HeaderContent)(nil),                 // 60: papegames.sparrow.survey.HeaderContent
	(*FooterContent)(nil),                 // 61: papegames.sparrow.survey.FooterContent
	(*QuestionSelectConfig)(nil),          // 62: papegames.sparrow.survey.QuestionSelectConfig
	(*SelectOption)(nil),                  // 63: papegames.sparrow.survey.SelectOption
	(*QuestionList)(nil),                  // 64: papegames.sparrow.survey.QuestionList
	(*RequestConfig)(nil),                 // 65: papegames.sparrow.survey.RequestConfig
	(*QuestionConfig)(nil),                // 66: papegames.sparrow.survey.QuestionConfig
	(*PublishSurveyRequest)(nil),          // 67: papegames.sparrow.survey.PublishSurveyRequest
	(*PublishSurveyResponse)(nil),         // 68: papegames.sparrow.survey.PublishSurveyResponse
	(*ShowSurveyRequest)(nil),             // 69: papegames.sparrow.survey.ShowSurveyRequest
	(*ShowSurveyResponse)(nil),            // 70: papegames.sparrow.survey.ShowSurveyResponse
	(*SurveyInputMethodListRequest)(nil),  // 71: papegames.sparrow.survey.SurveyInputMethodListRequest
	(*SurveyInputMethodListResponse)(nil), // 72: papegames.sparrow.survey.SurveyInputMethodListResponse
	(*InputMethodListResponseData)(nil),   // 73: papegames.sparrow.survey.InputMethodListResponseData
	(*OuterQuestionStatisticsData)(nil),   // 74: papegames.sparrow.survey.OuterQuestionStatisticsData
	(*Detail)(nil),                        // 75: papegames.sparrow.survey.Detail
	(*OptionDetail)(nil),                  // 76: papegames.sparrow.survey.OptionDetail
	(*Label)(nil),                         // 77: papegames.sparrow.survey.Label
	(*Image)(nil),                         // 78: papegames.sparrow.survey.Image
	(*SurveyOverwriteSendReq)(nil),        // 79: papegames.sparrow.survey.SurveyOverwriteSendReq
	(*SurveyOverwriteSyncReq)(nil),        // 80: papegames.sparrow.survey.SurveyOverwriteSyncReq
	(*xtype.Timestamp)(nil),               // 81: papegames.type.Timestamp
	(*xtype.RawMessage)(nil),              // 82: papegames.type.RawMessage
}
var file_proto_survey_conf_proto_depIdxs = []int32{
	13, // 0: papegames.sparrow.survey.SurveyListResponse.list:type_name -> papegames.sparrow.survey.Survey
	81, // 1: papegames.sparrow.survey.SyncSurveyResponseData.timestamp:type_name -> papegames.type.Timestamp
	82, // 2: papegames.sparrow.survey.ImpSurveyRequest.schema:type_name -> papegames.type.RawMessage
	82, // 3: papegames.sparrow.survey.ImpSurveyRequest.previewSchema:type_name -> papegames.type.RawMessage
	82, // 4: papegames.sparrow.survey.ImpSurveyRequest.settings:type_name -> papegames.type.RawMessage
	82, // 5: papegames.sparrow.survey.ImpSurveyRequest.webSettings:type_name -> papegames.type.RawMessage
	82, // 6: papegames.sparrow.survey.ImpSurveyRequest.languages:type_name -> papegames.type.RawMessage
	14, // 7: papegames.sparrow.survey.Survey.question_statistics_data:type_name -> papegames.sparrow.survey.QuestionStatisticsData
	48, // 8: papegames.sparrow.survey.Survey.web_path_list:type_name -> papegames.sparrow.survey.WebPath
	19, // 9: papegames.sparrow.survey.Setting.baseRuleConfig:type_name -> papegames.sparrow.survey.BaseRuleConfig
	22, // 10: papegames.sparrow.survey.Setting.giftConfig:type_name -> papegames.sparrow.survey.GiftConfig
	25, // 11: papegames.sparrow.survey.Setting.answerLimitConfig:type_name -> papegames.sparrow.survey.AnswerLimitConfig
	26, // 12: papegames.sparrow.survey.Setting.materialsConfig:type_name -> papegames.sparrow.survey.MaterialsConfig
	27, // 13: papegames.sparrow.survey.Setting.footerConfig:type_name -> papegames.sparrow.survey.FooterConfig
	28, // 14: papegames.sparrow.survey.Setting.sourceConfig:type_name -> papegames.sparrow.survey.SourceConfig
	26, // 15: papegames.sparrow.survey.WebSetting.materialsConfig:type_name -> papegames.sparrow.survey.MaterialsConfig
	27, // 16: papegames.sparrow.survey.WebSetting.footerConfig:type_name -> papegames.sparrow.survey.FooterConfig
	28, // 17: papegames.sparrow.survey.WebSetting.sourceConfig:type_name -> papegames.sparrow.survey.SourceConfig
	20, // 18: papegames.sparrow.survey.BaseRuleConfig.timeLimitConfig:type_name -> papegames.sparrow.survey.TimeLimitConfig
	21, // 19: papegames.sparrow.survey.BaseRuleConfig.answerTimesConfig:type_name -> papegames.sparrow.survey.AnswerTimesConfig
	24, // 20: papegames.sparrow.survey.GiftConfig.preAwardConfig:type_name -> papegames.sparrow.survey.PreAwardConfig
	23, // 21: papegames.sparrow.survey.GiftConfig.redeemConfig:type_name -> papegames.sparrow.survey.RedeemConfig
	29, // 22: papegames.sparrow.survey.SourceConfig.agreements:type_name -> papegames.sparrow.survey.Agreement
	32, // 23: papegames.sparrow.survey.Schema.componentsMap:type_name -> papegames.sparrow.survey.ComponentMap
	38, // 24: papegames.sparrow.survey.Schema.componentsTree:type_name -> papegames.sparrow.survey.ComponentTree
	31, // 25: papegames.sparrow.survey.Schema.i18n:type_name -> papegames.sparrow.survey.I18N
	39, // 26: papegames.sparrow.survey.Schema.config:type_name -> papegames.sparrow.survey.Config
	40, // 27: papegames.sparrow.survey.Schema.meta:type_name -> papegames.sparrow.survey.Meta
	19, // 28: papegames.sparrow.survey.SettingsProps.base_rule_config:type_name -> papegames.sparrow.survey.BaseRuleConfig
	35, // 29: papegames.sparrow.survey.SettingsProps.skin_config:type_name -> papegames.sparrow.survey.SkinConfig
	26, // 30: papegames.sparrow.survey.SettingsProps.materials_config:type_name -> papegames.sparrow.survey.MaterialsConfig
	25, // 31: papegames.sparrow.survey.SettingsProps.answer_limit_config:type_name -> papegames.sparrow.survey.AnswerLimitConfig
	22, // 32: papegames.sparrow.survey.SettingsProps.gift_config:type_name -> papegames.sparrow.survey.GiftConfig
	34, // 33: papegames.sparrow.survey.PropsTree.survey_settings:type_name -> papegames.sparrow.survey.SurveySettings
	36, // 34: papegames.sparrow.survey.PropsTree.settings_props:type_name -> papegames.sparrow.survey.SettingsProps
	33, // 35: papegames.sparrow.survey.ComponentTree.meta:type_name -> papegames.sparrow.survey.MetaTree
	38, // 36: papegames.sparrow.survey.ComponentTree.children:type_name -> papegames.sparrow.survey.ComponentTree
	41, // 37: papegames.sparrow.survey.Config.layout:type_name -> papegames.sparrow.survey.Layout
	42, // 38: papegames.sparrow.survey.Layout.props:type_name -> papegames.sparrow.survey.PropsLayout
	64, // 39: papegames.sparrow.survey.SurveyDetailResponse.question_list:type_name -> papegames.sparrow.survey.QuestionList
	47, // 40: papegames.sparrow.survey.SurveyDetailResponse.survey_config:type_name -> papegames.sparrow.survey.SurveyConfig
	74, // 41: papegames.sparrow.survey.SurveyDetailResponse.question_statistics_data:type_name -> papegames.sparrow.survey.OuterQuestionStatisticsData
	48, // 42: papegames.sparrow.survey.SurveyConfig.web_path_list:type_name -> papegames.sparrow.survey.WebPath
	14, // 43: papegames.sparrow.survey.SurveyConfig.question_statistics_data:type_name -> papegames.sparrow.survey.QuestionStatisticsData
	50, // 44: papegames.sparrow.survey.QuestionBaseConfig.questionTitle:type_name -> papegames.sparrow.survey.Title
	51, // 45: papegames.sparrow.survey.QuestionBaseConfig.questionDesc:type_name -> papegames.sparrow.survey.Desc
	52, // 46: papegames.sparrow.survey.QuestionBaseConfig.questionTip:type_name -> papegames.sparrow.survey.Tip
	55, // 47: papegames.sparrow.survey.QuestionLogicalConfig.visibleSwitch:type_name -> papegames.sparrow.survey.VisibleSwitch
	56, // 48: papegames.sparrow.survey.QuestionLogicalConfig.displayLogic:type_name -> papegames.sparrow.survey.DisplayLogic
	58, // 49: papegames.sparrow.survey.DisplayLogic.rules:type_name -> papegames.sparrow.survey.Rule
	57, // 50: papegames.sparrow.survey.Rule.relatedContentConfig:type_name -> papegames.sparrow.survey.RelatedContentConfig
	60, // 51: papegames.sparrow.survey.QuestionComponentConfig.headerContent:type_name -> papegames.sparrow.survey.HeaderContent
	61, // 52: papegames.sparrow.survey.QuestionComponentConfig.FooterContent:type_name -> papegames.sparrow.survey.FooterContent
	63, // 53: papegames.sparrow.survey.QuestionSelectConfig.selectOptions:type_name -> papegames.sparrow.survey.SelectOption
	49, // 54: papegames.sparrow.survey.QuestionList.questionBaseConfig:type_name -> papegames.sparrow.survey.QuestionBaseConfig
	53, // 55: papegames.sparrow.survey.QuestionList.configProps:type_name -> papegames.sparrow.survey.ConfigProps
	54, // 56: papegames.sparrow.survey.QuestionList.questionLogicalConfig:type_name -> papegames.sparrow.survey.QuestionLogicalConfig
	59, // 57: papegames.sparrow.survey.QuestionList.questionComponentConfig:type_name -> papegames.sparrow.survey.QuestionComponentConfig
	62, // 58: papegames.sparrow.survey.QuestionList.questionSelectConfig:type_name -> papegames.sparrow.survey.QuestionSelectConfig
	65, // 59: papegames.sparrow.survey.QuestionList.requestConfig:type_name -> papegames.sparrow.survey.RequestConfig
	53, // 60: papegames.sparrow.survey.RequestConfig.config_props:type_name -> papegames.sparrow.survey.ConfigProps
	65, // 61: papegames.sparrow.survey.QuestionConfig.request_config:type_name -> papegames.sparrow.survey.RequestConfig
	13, // 62: papegames.sparrow.survey.ShowSurveyResponse.data:type_name -> papegames.sparrow.survey.Survey
	65, // 63: papegames.sparrow.survey.SurveyInputMethodListRequest.request_config:type_name -> papegames.sparrow.survey.RequestConfig
	73, // 64: papegames.sparrow.survey.SurveyInputMethodListResponse.list:type_name -> papegames.sparrow.survey.InputMethodListResponseData
	75, // 65: papegames.sparrow.survey.OuterQuestionStatisticsData.detail:type_name -> papegames.sparrow.survey.Detail
	76, // 66: papegames.sparrow.survey.Detail.detail:type_name -> papegames.sparrow.survey.OptionDetail
	77, // 67: papegames.sparrow.survey.OptionDetail.label:type_name -> papegames.sparrow.survey.Label
	78, // 68: papegames.sparrow.survey.OptionDetail.image:type_name -> papegames.sparrow.survey.Image
	69, // [69:69] is the sub-list for method output_type
	69, // [69:69] is the sub-list for method input_type
	69, // [69:69] is the sub-list for extension type_name
	69, // [69:69] is the sub-list for extension extendee
	0,  // [0:69] is the sub-list for field type_name
}

func init() { file_proto_survey_conf_proto_init() }
func file_proto_survey_conf_proto_init() {
	if File_proto_survey_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_conf_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyPreviewReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordConfDetailsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordConfDetailsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*SyncSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*SyncSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SyncSurveyResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*ImpSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SurveySetStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyDelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*Survey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionStatisticsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecycleListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*RecoverSurveyRecycleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*Setting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*WebSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*BaseRuleConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*TimeLimitConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[21].Exporter = func(v any, i int) any {
			switch v := v.(*AnswerTimesConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[22].Exporter = func(v any, i int) any {
			switch v := v.(*GiftConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[23].Exporter = func(v any, i int) any {
			switch v := v.(*RedeemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[24].Exporter = func(v any, i int) any {
			switch v := v.(*PreAwardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[25].Exporter = func(v any, i int) any {
			switch v := v.(*AnswerLimitConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[26].Exporter = func(v any, i int) any {
			switch v := v.(*MaterialsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[27].Exporter = func(v any, i int) any {
			switch v := v.(*FooterConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[28].Exporter = func(v any, i int) any {
			switch v := v.(*SourceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[29].Exporter = func(v any, i int) any {
			switch v := v.(*Agreement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[30].Exporter = func(v any, i int) any {
			switch v := v.(*Schema); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[31].Exporter = func(v any, i int) any {
			switch v := v.(*I18N); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[32].Exporter = func(v any, i int) any {
			switch v := v.(*ComponentMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[33].Exporter = func(v any, i int) any {
			switch v := v.(*MetaTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[34].Exporter = func(v any, i int) any {
			switch v := v.(*SurveySettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[35].Exporter = func(v any, i int) any {
			switch v := v.(*SkinConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[36].Exporter = func(v any, i int) any {
			switch v := v.(*SettingsProps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[37].Exporter = func(v any, i int) any {
			switch v := v.(*PropsTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[38].Exporter = func(v any, i int) any {
			switch v := v.(*ComponentTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[39].Exporter = func(v any, i int) any {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[40].Exporter = func(v any, i int) any {
			switch v := v.(*Meta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[41].Exporter = func(v any, i int) any {
			switch v := v.(*Layout); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[42].Exporter = func(v any, i int) any {
			switch v := v.(*PropsLayout); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[43].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[44].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[45].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[46].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[47].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[48].Exporter = func(v any, i int) any {
			switch v := v.(*WebPath); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[49].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionBaseConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[50].Exporter = func(v any, i int) any {
			switch v := v.(*Title); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[51].Exporter = func(v any, i int) any {
			switch v := v.(*Desc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[52].Exporter = func(v any, i int) any {
			switch v := v.(*Tip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[53].Exporter = func(v any, i int) any {
			switch v := v.(*ConfigProps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[54].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionLogicalConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[55].Exporter = func(v any, i int) any {
			switch v := v.(*VisibleSwitch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[56].Exporter = func(v any, i int) any {
			switch v := v.(*DisplayLogic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[57].Exporter = func(v any, i int) any {
			switch v := v.(*RelatedContentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[58].Exporter = func(v any, i int) any {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[59].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[60].Exporter = func(v any, i int) any {
			switch v := v.(*HeaderContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[61].Exporter = func(v any, i int) any {
			switch v := v.(*FooterContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[62].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionSelectConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[63].Exporter = func(v any, i int) any {
			switch v := v.(*SelectOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[64].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[65].Exporter = func(v any, i int) any {
			switch v := v.(*RequestConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[66].Exporter = func(v any, i int) any {
			switch v := v.(*QuestionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[67].Exporter = func(v any, i int) any {
			switch v := v.(*PublishSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[68].Exporter = func(v any, i int) any {
			switch v := v.(*PublishSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[69].Exporter = func(v any, i int) any {
			switch v := v.(*ShowSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[70].Exporter = func(v any, i int) any {
			switch v := v.(*ShowSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[71].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyInputMethodListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[72].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyInputMethodListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[73].Exporter = func(v any, i int) any {
			switch v := v.(*InputMethodListResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[74].Exporter = func(v any, i int) any {
			switch v := v.(*OuterQuestionStatisticsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[75].Exporter = func(v any, i int) any {
			switch v := v.(*Detail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[76].Exporter = func(v any, i int) any {
			switch v := v.(*OptionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[77].Exporter = func(v any, i int) any {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[78].Exporter = func(v any, i int) any {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[79].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyOverwriteSendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_conf_proto_msgTypes[80].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyOverwriteSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   81,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_conf_proto_goTypes,
		DependencyIndexes: file_proto_survey_conf_proto_depIdxs,
		MessageInfos:      file_proto_survey_conf_proto_msgTypes,
	}.Build()
	File_proto_survey_conf_proto = out.File
	file_proto_survey_conf_proto_rawDesc = nil
	file_proto_survey_conf_proto_goTypes = nil
	file_proto_survey_conf_proto_depIdxs = nil
}
