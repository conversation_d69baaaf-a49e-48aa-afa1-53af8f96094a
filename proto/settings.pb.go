// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/settings.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ValidRedeemConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *ValidRedeemConfigRequest) Reset() {
	*x = ValidRedeemConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidRedeemConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidRedeemConfigRequest) ProtoMessage() {}

func (x *ValidRedeemConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidRedeemConfigRequest.ProtoReflect.Descriptor instead.
func (*ValidRedeemConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{0}
}

func (x *ValidRedeemConfigRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ValidRedeemConfigRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ValidRedeemConfigRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type ValidRedeemConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ValidRedeemConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int64                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ValidRedeemConfigResponse) Reset() {
	*x = ValidRedeemConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidRedeemConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidRedeemConfigResponse) ProtoMessage() {}

func (x *ValidRedeemConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidRedeemConfigResponse.ProtoReflect.Descriptor instead.
func (*ValidRedeemConfigResponse) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{1}
}

func (x *ValidRedeemConfigResponse) GetList() []*ValidRedeemConfig {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ValidRedeemConfigResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ValidRedeemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ClientId           string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id"`
	RedeemId           int32  `protobuf:"varint,3,opt,name=redeem_id,json=redeemId,proto3" json:"redeem_id"`
	RedeemHead         string `protobuf:"bytes,4,opt,name=redeem_head,json=redeemHead,proto3" json:"redeem_head"`
	RedeemType         int32  `protobuf:"varint,5,opt,name=redeem_type,json=redeemType,proto3" json:"redeem_type"`
	IsCustom           int32  `protobuf:"varint,6,opt,name=is_custom,json=isCustom,proto3" json:"is_custom"`
	RedeemNumber       int32  `protobuf:"varint,7,opt,name=redeem_number,json=redeemNumber,proto3" json:"redeem_number"`
	RedeemTimefor1Name int32  `protobuf:"varint,8,opt,name=redeem_timefor1name,json=redeemTimefor1name,proto3" json:"redeem_timefor1name"`
	RedeemTimefor1Id   int32  `protobuf:"varint,9,opt,name=redeem_timefor1id,json=redeemTimefor1id,proto3" json:"redeem_timefor1id"`
	RedeemOpenTime     string `protobuf:"bytes,10,opt,name=redeem_open_time,json=redeemOpenTime,proto3" json:"redeem_opentime"`
	RedeemCloseTime    string `protobuf:"bytes,11,opt,name=redeem_close_time,json=redeemCloseTime,proto3" json:"redeem_closetime"`
	RedeemItem         string `protobuf:"bytes,12,opt,name=redeem_item,json=redeemItem,proto3" json:"redeem_item"`
	RedeemGift         string `protobuf:"bytes,13,opt,name=redeem_gift,json=redeemGift,proto3" json:"redeem_gift"`
	RedeemChannel      string `protobuf:"bytes,14,opt,name=redeem_channel,json=redeemChannel,proto3" json:"redeem_channel"`
	RedeemCreateId     string `protobuf:"bytes,15,opt,name=redeem_create_id,json=redeemCreateId,proto3" json:"redeem_create_id"`
	RedeemWhitelist    string `protobuf:"bytes,16,opt,name=redeem_whitelist,json=redeemWhitelist,proto3" json:"redeem_whitelist"`
	Extra              string `protobuf:"bytes,17,opt,name=extra,proto3" json:"extra"`
	Memo               string `protobuf:"bytes,18,opt,name=memo,proto3" json:"memo"`
	Mtime              string `protobuf:"bytes,19,opt,name=mtime,proto3" json:"mtime"`
	Ctime              string `protobuf:"bytes,20,opt,name=ctime,proto3" json:"ctime"`
	RedeemAuto         int32  `protobuf:"varint,21,opt,name=redeem_auto,json=redeemAuto,proto3" json:"redeem_auto"`
	Notify             int32  `protobuf:"varint,22,opt,name=notify,proto3" json:"notify"`
	RedeemHead2        string `protobuf:"bytes,23,opt,name=redeem_head2,json=redeemHead2,proto3" json:"redeemHead"`
}

func (x *ValidRedeemConfig) Reset() {
	*x = ValidRedeemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidRedeemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidRedeemConfig) ProtoMessage() {}

func (x *ValidRedeemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidRedeemConfig.ProtoReflect.Descriptor instead.
func (*ValidRedeemConfig) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{2}
}

func (x *ValidRedeemConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ValidRedeemConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemId() int32 {
	if x != nil {
		return x.RedeemId
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemHead() string {
	if x != nil {
		return x.RedeemHead
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemType() int32 {
	if x != nil {
		return x.RedeemType
	}
	return 0
}

func (x *ValidRedeemConfig) GetIsCustom() int32 {
	if x != nil {
		return x.IsCustom
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemNumber() int32 {
	if x != nil {
		return x.RedeemNumber
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemTimefor1Name() int32 {
	if x != nil {
		return x.RedeemTimefor1Name
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemTimefor1Id() int32 {
	if x != nil {
		return x.RedeemTimefor1Id
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemOpenTime() string {
	if x != nil {
		return x.RedeemOpenTime
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemCloseTime() string {
	if x != nil {
		return x.RedeemCloseTime
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemItem() string {
	if x != nil {
		return x.RedeemItem
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemGift() string {
	if x != nil {
		return x.RedeemGift
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemChannel() string {
	if x != nil {
		return x.RedeemChannel
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemCreateId() string {
	if x != nil {
		return x.RedeemCreateId
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemWhitelist() string {
	if x != nil {
		return x.RedeemWhitelist
	}
	return ""
}

func (x *ValidRedeemConfig) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *ValidRedeemConfig) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *ValidRedeemConfig) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *ValidRedeemConfig) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *ValidRedeemConfig) GetRedeemAuto() int32 {
	if x != nil {
		return x.RedeemAuto
	}
	return 0
}

func (x *ValidRedeemConfig) GetNotify() int32 {
	if x != nil {
		return x.Notify
	}
	return 0
}

func (x *ValidRedeemConfig) GetRedeemHead2() string {
	if x != nil {
		return x.RedeemHead2
	}
	return ""
}

type PreAwardTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *PreAwardTemplateRequest) Reset() {
	*x = PreAwardTemplateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAwardTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAwardTemplateRequest) ProtoMessage() {}

func (x *PreAwardTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAwardTemplateRequest.ProtoReflect.Descriptor instead.
func (*PreAwardTemplateRequest) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{3}
}

func (x *PreAwardTemplateRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PreAwardTemplateRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PreAwardTemplateRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type PreAwardTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*PreAwardTemplate `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int64               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PreAwardTemplateResponse) Reset() {
	*x = PreAwardTemplateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAwardTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAwardTemplateResponse) ProtoMessage() {}

func (x *PreAwardTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAwardTemplateResponse.ProtoReflect.Descriptor instead.
func (*PreAwardTemplateResponse) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{4}
}

func (x *PreAwardTemplateResponse) GetList() []*PreAwardTemplate {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PreAwardTemplateResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type PreAwardTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	ClientId  string `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id"`
	Sender    string `protobuf:"bytes,4,opt,name=sender,proto3" json:"sender"`
	Title     string `protobuf:"bytes,5,opt,name=title,proto3" json:"title"`
	Body      string `protobuf:"bytes,6,opt,name=body,proto3" json:"body"`
	Content   string `protobuf:"bytes,7,opt,name=content,proto3" json:"content"`
	Memo      string `protobuf:"bytes,8,opt,name=memo,proto3" json:"memo"`
	Editor    string `protobuf:"bytes,9,opt,name=editor,proto3" json:"editor"`
	Stime     string `protobuf:"bytes,10,opt,name=stime,proto3" json:"stime"`
	Etime     string `protobuf:"bytes,11,opt,name=etime,proto3" json:"etime"`
	Mtime     string `protobuf:"bytes,12,opt,name=mtime,proto3" json:"mtime"`
	Ctime     string `protobuf:"bytes,13,opt,name=ctime,proto3" json:"ctime"`
	Permanent int32  `protobuf:"varint,14,opt,name=permanent,proto3" json:"permanent"`
	Creator   string `protobuf:"bytes,15,opt,name=creator,proto3" json:"creator"`
	Key       int32  `protobuf:"varint,16,opt,name=key,proto3" json:"key"`
}

func (x *PreAwardTemplate) Reset() {
	*x = PreAwardTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_settings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAwardTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAwardTemplate) ProtoMessage() {}

func (x *PreAwardTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_settings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAwardTemplate.ProtoReflect.Descriptor instead.
func (*PreAwardTemplate) Descriptor() ([]byte, []int) {
	return file_proto_settings_proto_rawDescGZIP(), []int{5}
}

func (x *PreAwardTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PreAwardTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PreAwardTemplate) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *PreAwardTemplate) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *PreAwardTemplate) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PreAwardTemplate) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *PreAwardTemplate) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PreAwardTemplate) GetMemo() string {
	if x != nil {
		return x.Memo
	}
	return ""
}

func (x *PreAwardTemplate) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

func (x *PreAwardTemplate) GetStime() string {
	if x != nil {
		return x.Stime
	}
	return ""
}

func (x *PreAwardTemplate) GetEtime() string {
	if x != nil {
		return x.Etime
	}
	return ""
}

func (x *PreAwardTemplate) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *PreAwardTemplate) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *PreAwardTemplate) GetPermanent() int32 {
	if x != nil {
		return x.Permanent
	}
	return 0
}

func (x *PreAwardTemplate) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PreAwardTemplate) GetKey() int32 {
	if x != nil {
		return x.Key
	}
	return 0
}

var File_proto_settings_proto protoreflect.FileDescriptor

var file_proto_settings_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x68, 0x0a, 0x18, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x72, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x95, 0x0a, 0x0a, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07,
	0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0xd2, 0xa7,
	0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x52, 0x08, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x48,
	0x65, 0x61, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x15, 0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13,
	0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x08, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x12, 0x3c, 0x0a,
	0x0d, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0xd2, 0xa7, 0x86, 0x07, 0x12, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x13, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x66, 0x6f, 0x72, 0x31, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1d, 0xd2, 0xa7, 0x86, 0x07, 0x18, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x66,
	0x6f, 0x72, 0x31, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x12, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x54,
	0x69, 0x6d, 0x65, 0x66, 0x6f, 0x72, 0x31, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x11, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x66, 0x6f, 0x72, 0x31, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1b, 0xd2, 0xa7, 0x86, 0x07, 0x16, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x66, 0x6f, 0x72,
	0x31, 0x69, 0x64, 0x52, 0x10, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x66,
	0x6f, 0x72, 0x31, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x19, 0xd2, 0xa7, 0x86, 0x07, 0x14, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xd2, 0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x52, 0x0a,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x36, 0x0a, 0x0b, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x15, 0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69,
	0x66, 0x74, 0x12, 0x3f, 0x0a, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xd2, 0xa7, 0x86, 0x07,
	0x13, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0d, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x12, 0x44, 0x0a, 0x10, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xd2,
	0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x10, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xd2, 0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52,
	0x0f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x22, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x6d, 0x65, 0x6d, 0x6f, 0x52, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x25, 0x0a, 0x05, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07,
	0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x42, 0x15,
	0xd2, 0xa7, 0x86, 0x07, 0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x0a, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x41, 0x75, 0x74,
	0x6f, 0x12, 0x28, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x37, 0x0a, 0x0c, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x32, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x64, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x48,
	0x65, 0x61, 0x64, 0x32, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x67, 0x0a, 0x17, 0x50,
	0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x93, 0x05, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x13, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x22, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e,
	0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x62, 0x6f, 0x64, 0x79, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x22, 0x0a, 0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6d, 0x65, 0x6d, 0x6f, 0x52,
	0x04, 0x6d, 0x65, 0x6d, 0x6f, 0x12, 0x28, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x12,
	0x25, 0x0a, 0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f,
	0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e,
	0x3a, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7,
	0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x70,
	0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13,
	0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x2b,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0xd2, 0xa7, 0x86, 0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d, 0xd2, 0xa7, 0x86, 0x07, 0x08, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x6b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x42, 0x41, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_settings_proto_rawDescOnce sync.Once
	file_proto_settings_proto_rawDescData = file_proto_settings_proto_rawDesc
)

func file_proto_settings_proto_rawDescGZIP() []byte {
	file_proto_settings_proto_rawDescOnce.Do(func() {
		file_proto_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_settings_proto_rawDescData)
	})
	return file_proto_settings_proto_rawDescData
}

var file_proto_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_settings_proto_goTypes = []any{
	(*ValidRedeemConfigRequest)(nil),  // 0: papegames.sparrow.survey.ValidRedeemConfigRequest
	(*ValidRedeemConfigResponse)(nil), // 1: papegames.sparrow.survey.ValidRedeemConfigResponse
	(*ValidRedeemConfig)(nil),         // 2: papegames.sparrow.survey.ValidRedeemConfig
	(*PreAwardTemplateRequest)(nil),   // 3: papegames.sparrow.survey.PreAwardTemplateRequest
	(*PreAwardTemplateResponse)(nil),  // 4: papegames.sparrow.survey.PreAwardTemplateResponse
	(*PreAwardTemplate)(nil),          // 5: papegames.sparrow.survey.PreAwardTemplate
}
var file_proto_settings_proto_depIdxs = []int32{
	2, // 0: papegames.sparrow.survey.ValidRedeemConfigResponse.list:type_name -> papegames.sparrow.survey.ValidRedeemConfig
	5, // 1: papegames.sparrow.survey.PreAwardTemplateResponse.list:type_name -> papegames.sparrow.survey.PreAwardTemplate
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_settings_proto_init() }
func file_proto_settings_proto_init() {
	if File_proto_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_settings_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ValidRedeemConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_settings_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ValidRedeemConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_settings_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ValidRedeemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_settings_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*PreAwardTemplateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_settings_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PreAwardTemplateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_settings_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PreAwardTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_settings_proto_goTypes,
		DependencyIndexes: file_proto_settings_proto_depIdxs,
		MessageInfos:      file_proto_settings_proto_msgTypes,
	}.Build()
	File_proto_settings_proto = out.File
	file_proto_settings_proto_rawDesc = nil
	file_proto_settings_proto_goTypes = nil
	file_proto_settings_proto_depIdxs = nil
}
