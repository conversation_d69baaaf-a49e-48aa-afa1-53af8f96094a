syntax = "proto3";

package papegames.sparrow.survey;

import "google/api/field_behavior.proto";
import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";
import "papegames/type/raw_message.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message SurveyListRequest {
  // 当前页码，默认1
  int32 page = 1;
  // 每页条数，默认10
  int32 page_size = 2;
  // 答卷id
  int64 id = 3;
  // 答卷名称
  string name = 4;
  // 创建时间排序
  string sort_ctime = 5;
  // 状态
  int32 status = 6;
  int64 client_id = 7 [(google.api.field_behavior) = REQUIRED];
  // 问卷ID数组
  repeated int64 id_list = 8;
}

message SurveyListResponse {
  option (tagger.disable_omitempty) = true;
  repeated Survey list = 1;
  int64 total = 2;
}

message SurveyPreviewReq {
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
  int64 client_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyRecordConfDetailsReq {
  int64 id = 1;
  int64 survey_id = 2;
}

message SurveyRecordConfDetailsRes {
  string record_detail = 1;
  string survey_config = 2;
  string user_record = 3;
}

message SyncSurveyRequest {
  int64 id = 1;
  int64 client_id = 2;
  string material_version = 3;
}

message SyncSurveyResponse {
  //	string ID = 1;               // 标识符
  //	string CTime = 2; // 创建时间
  //	string MTime = 3; // 修改时间
  //	string Editor = 4;           // 编辑者
  //	int32 IsDelete = 5;           // 删除标记
  //	Schema PreviewSchema = 6;    // 预览模式
  //	string HashCode = 7;         // 哈希码
  //	int32 IsPause = 8;            // 暂停标记
  //	int32 IsClosed = 9;           // 关闭标记
  //	int32 IsOpened = 10;          // 打开标记
  //	int32 IsModifyUnpublish = 11; // 修改未发布标记
  //	string ClientId = 12;        // 客户端ID
  uint32 data = 1;
  uint32 code = 2;
  bool success = 3;
  string timestamp = 4;
  string info = 5;
}

message SyncSurveyResponseData {
  int64 data = 1;
  int64 code = 2;
  bool success = 3;
  papegames.type.Timestamp timestamp = 4;
}
message ImpSurveyRequest {
  option (tagger.disable_omitempty) = true;
  // client_id
  string clientid = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷名称
  string name = 2;
  // 问卷开始时间
  string stime = 3;
  // 问卷结束时间
  string etime = 4;
  // 配置 schema
  papegames.type.RawMessage schema = 5;
  // 预览 schema
  papegames.type.RawMessage previewSchema = 6;
  // 问卷设置
  papegames.type.RawMessage settings = 7;

  // 问卷C端用到的配置
  papegames.type.RawMessage webSettings = 8;

  // 问卷C端用到的配置
  papegames.type.RawMessage languages = 9;


  int32 apiVersion = 10;
  string keyValue = 11 ;

  string font = 12 ;
  // 创建人
  string creator = 13 ;
}
message SurveySetStatusRequest {
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
  int32 status = 2 [(google.api.field_behavior) = REQUIRED];
  int64 client_id = 3 [(google.api.field_behavior) = REQUIRED];
}

message SurveyDelRequest {
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  repeated int64 del_list = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyRequest {
  int64 survey_id = 1;
  int64 client_id = 2;
}

message SurveyResponse {
  int64 id = 1;
}

message Survey {
  option (tagger.disable_omitempty) = true;

  // ID，自增主键，创建时此参数不需要传
  int64 id = 1 [(tagger.tags) = "gorm:primaryKey;type:int(11) default 0;comment:主键ID"];

  // client_id
  int64 client_id = 2 [(google.api.field_behavior) = REQUIRED];

  // 问卷名称
  string name = 3 [(tagger.tags) = "gorm:type:varchar(128);comment:问卷名称", (tagger.tags) = "json:name"];

  // 问卷是否打开
  int32 is_closed = 4 [(tagger.tags) = "gorm:type:int(8);comment:问卷是否打开：（0: 打开，1: 关闭）"];

  // 问卷是否暂停
  int32 is_pause = 5 [(tagger.tags) = "gorm:type:int(8);comment:问卷是否暂停：（0: 不暂停，1: 暂停）"];

  // 是否发布
  int32 is_publish = 6 [(tagger.tags) = "gorm:type:tinyint(4);comment:是否发布（0: 未发布，1: 已发布）"];

  // 是否有修改未发布
  int32 is_modify_unpublish = 7 [(tagger.tags) = "gorm:type:tinyint(4);comment:是否有修改未发布（0: 无，1: 有）"];

  // 是否开启过答题
  int32 is_opened = 8 [(tagger.tags) = "gorm:type:tinyint(4);comment:是否开启过答题（0: 未开启过，1: 开启过）"];

  // 问卷开始时间
  string stime = 9 [(tagger.tags) = "gorm:type:varchar;comment:问卷开始时间", (tagger.tags) = "json:stime"];

  // 问卷结束时间
  string etime = 10 [(tagger.tags) = "gorm:type:varchar;comment:问卷结束时间", (tagger.tags) = "json:etime"];

  // 问卷类型
  int32 type = 11 [(tagger.tags) = "gorm:type:int(8);comment:问卷类型", (tagger.tags) = "json:type"];

  // 配置 schema
  string schema = 12 [(tagger.tags) = "json:schema"];

  // 预览 schema
  string preview_schema = 13;

  // 问卷设置
  string settings = 14 [(tagger.tags) = "gorm:json;comment:问卷设置", (tagger.tags) = "json:settings"];

  // 问卷C端用到的配置
  string web_settings = 15 [(tagger.tags) = "gorm:type:varchar;comment:问卷C端用到的配置"];

  // 问卷C端用到的配置
  string languages = 16 [(tagger.tags) = "gorm:type:varchar;comment:语言包", (tagger.tags) = "json:languages"];

  // 问卷id-hash
  string hash_code = 17 [
    (tagger.tags) = "gorm:type:varchar;comment:问卷id-hash"
    //    (tagger.tags) = "json:hash_code"
  ];

  // 是否删除
  int32 is_delete = 18 [
    (tagger.tags) = "gorm:type:tinyint(4);comment:是否删除（0: 未删除，1: 已删除）"
    //    (tagger.tags) = "json:is_delete"
  ];

  // 删除时间
  string deltime = 19 [(tagger.tags) = "gorm:type:varchar;comment:删除时间", (tagger.tags) = "json:deltime"];

  string key_value = 20 [(tagger.tags) = "gorm:type:text;comment:中文/英文文案", (tagger.tags) = "json:key_value"];

  string font = 21 [(tagger.tags) = "gorm:type:text;comment:字体 ttf 的 oss 链接", (tagger.tags) = "json:font"];


  // 备注
  string remark = 22 [(tagger.tags) = "gorm:type:varchar;comment:备注", (tagger.tags) = "json:remark"];

  // 创建时间
  string ctime = 23 [(google.api.field_behavior) = OUTPUT_ONLY, (tagger.tags) = "json:ctime,omitempty", (tagger.tags) = "gorm:autoCtime"];

  // 更新时间
  string mtime = 24 [(google.api.field_behavior) = OUTPUT_ONLY, (tagger.tags) = "json:mtime,omitempty", (tagger.tags) = "gorm:autoMtime"];

  // 创建人
  string creator = 25 [(tagger.tags) = "gorm:type:varchar(64);comment:创建人", (tagger.tags) = "json:creator"];

  // 最近修改人
  string editor = 26 [(tagger.tags) = "gorm:type:varchar(64);comment:最近修改人", (tagger.tags) = "json:editor"];

  // isTimeLimit
  bool is_time_limit = 27 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:is_time_limit"];

  // 用户答卷数量
  int64 all_answered_user_count = 28 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:all_answered_user_count"];

  // 状态
  int32 status = 29 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:status"];

  // 答卷统计
  int64 full_valid_uid = 30 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:full_valid_uid"];

  QuestionStatisticsData question_statistics_data = 31 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:question_statistics_data"];
  repeated WebPath web_path_list = 32 [(tagger.tags) = "gorm:-", (tagger.tags) = "json:web_path_list"];
}

message QuestionStatisticsData {
  option (tagger.disable_omitempty) = true;
  int64 valid_answer_total = 1;
  uint32 valid_user_total = 2;
}

// 回收站
message SurveyRecycleListRes {
  int64 id = 1;
  string name = 2;
  int64 full_valid_uid = 3;
  string ctime = 4;
}

message RecoverSurveyRecycleReq {
  repeated int64 list = 1;
  int64 client_id = 2;
}

// Settings message
message Setting {
  BaseRuleConfig baseRuleConfig = 1 [json_name = "baseRuleConfig"];
  GiftConfig giftConfig = 2 [json_name = "giftConfig"];
  AnswerLimitConfig answerLimitConfig = 3 [json_name = "answerLimitConfig"];
  repeated int32 zoneIds = 4 [json_name = "zoneIds"];
  MaterialsConfig materialsConfig = 5 [json_name = "materialsConfig"];
  FooterConfig footerConfig = 6 [json_name = "footerConfig"];
  SourceConfig sourceConfig = 7 [json_name = "sourceConfig"];
}
message WebSetting {
  string loginType = 1 [json_name = "loginType"];
  bool isEndPreview = 2 [json_name = "isEndPreview"];
  bool isGoOnAnswer = 3 [json_name = "isGoOnAnswer"];
  repeated string languageList = 4 [json_name = "languageList"];
  MaterialsConfig materialsConfig = 5 [json_name = "materialsConfig"];
  FooterConfig footerConfig = 6 [json_name = "footerConfig"];
  SourceConfig sourceConfig = 7 [json_name = "sourceConfig"];
}
message BaseRuleConfig {
  string loginType = 1 [json_name = "loginType"];
  TimeLimitConfig timeLimitConfig = 2 [json_name = "timeLimitConfig"];
  bool isEndPreview = 3 [json_name = "isEndPreview"];
  bool isGoOnAnswer = 4 [json_name = "isGoOnAnswer"];
  AnswerTimesConfig answerTimesConfig = 5 [json_name = "answerTimesConfig"];
  repeated string languageList = 6 [json_name = "languageList"];
  repeated string deliverList = 7 [json_name = "deliverList"];
}

message TimeLimitConfig {
  bool isTimeLimit = 1 [json_name = "isTimeLimit"];
  string stime = 2 [json_name = "stime"];
  string etime = 3 [json_name = "etime"];
}

message AnswerTimesConfig {
  int32 limitType = 1 [json_name = "limitType"];
  int32 times = 2 [json_name = "times"];
}

message GiftConfig {
  bool isGiveOutByCms = 1 [json_name = "isGiveOutByCms"];
  string giveOutType = 2 [json_name = "giveOutType"];
  PreAwardConfig preAwardConfig = 3 [json_name = "preAwardConfig"];
  RedeemConfig redeemConfig = 4 [json_name = "redeemConfig"];
}

message RedeemConfig {
  string  redeemHead = 1 [json_name = "redeemConfig"];
}

message PreAwardConfig {
  string id = 1 [json_name = "id"];
}

message AnswerLimitConfig {
  string limitType = 1 [json_name = "limitType"];
}

message MaterialsConfig {
  bool autoLatestMaterial = 1 [json_name = "autoLatestMaterial"];
  string materialVersion = 2 [json_name = "materialVersion"];
}

message FooterConfig {
  string url = 1 [json_name = "url"];
  string name = 2 [json_name = "name"];
}

message SourceConfig {
  string cityUrl = 1 [json_name = "cityUrl"];
  repeated Agreement agreements = 2 [json_name = "agreements"];
}

message Agreement {
  string image = 1 [json_name = "image"];
  string text = 2 [json_name = "text"];
  string link = 3 [json_name = "link"];
}

// ------------------------------------------

message Schema {
  string version = 1;
  repeated ComponentMap componentsMap = 2;
  repeated ComponentTree componentsTree = 3;
  I18N i18n = 4;
  Config config = 5;
  Meta meta = 6;
  int64 client_id = 7;
}

message I18N {
  // Intentionally left empty; include fields if needed.
}

message ComponentMap {
  string package_name = 1;
  string version = 2;
  string export_name = 3;
  string main = 4;
  bool destructuring = 5;
  string sub_name = 6;
  string component_name = 7;
  string dev_mode = 8;
}

message MetaTree {
  string title = 1;
  string router = 2;
}

message SurveySettings {
  string is_custom_skin_url = 1;
  string skin_url = 2;
  bool show_question_serial_number = 3;
  bool answer_question_process_can_back = 4;
}

message SkinConfig {
  string is_custom_skin_url = 1;
  string skin_url = 2;
  string bg_color = 3;
}

message SettingsProps {
  BaseRuleConfig base_rule_config = 1;
  SkinConfig skin_config = 2;
  MaterialsConfig materials_config = 3;
  AnswerLimitConfig answer_limit_config = 4;
  GiftConfig gift_config = 5;
  bool engine_is_init = 6;
}

message PropsTree {
  int32 spacing = 1;
  string wrapper_main_title = 2;
  string wrapper_sub_title = 3;
  bool show_question_serial_number = 4;
  string skin_url = 5;
  bool answer_question_process_can_back = 6;
  SurveySettings survey_settings = 7;
  int32 question_wrapper_active_key = 8;
  bool engine_is_init = 9;
  SettingsProps settings_props = 10;
  int64 client_id = 11;
}

message ComponentTree {
  string component_name = 1;
  string id = 2;
  string doc_id = 3;
  MetaTree meta = 4;
  string file_name = 5;
  bool hidden = 6;
  string title = 7;
  bool is_locked = 8;
  bool condition = 9;
  string condition_group = 10;
  repeated ComponentTree children = 11;
}

message Config {
  string history_mode = 1;
  string target_root_id = 2;
  Layout layout = 3;
}

message Meta {
  string name = 1;
  string project_name = 2;
  string description = 3;
  string spma = 4;
  string creator = 5;
}

message Layout {
  string component_name = 1;
  PropsLayout props = 2;
}

message PropsLayout {
  string logo = 1;
  string name = 2;
}

message UpdateSurveyRequest {
  int64 id = 1 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  string schema = 4 [(google.api.field_behavior) = REQUIRED];
  string languages = 5 [(google.api.field_behavior) = REQUIRED];
  string web_settings = 6;
  string key_value = 7;
  string font = 8;
}

message UpdateSurveyResponse {
  bool success = 1;
}

// 问卷详情
message SurveyDetailRequest {
  int64 survey_id = 1 [(google.api.field_behavior) = REQUIRED];
  int64 client_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyDetailResponse {
  repeated QuestionList question_list = 1;
  SurveyConfig survey_config = 2;
  OuterQuestionStatisticsData question_statistics_data = 3;
}

message SurveyConfig {
  int64 id = 1;                                     // 问卷ID
  int64 client_id = 2 [(tagger.tags) = "json:client_id"];  // 客户端ID
  string name = 3 [(tagger.tags) = "json:name"];           // 问卷名称
  int32 is_closed = 4 [(tagger.tags) = "json:is_closed"];  // 是否关闭
  int32 is_pause = 5 [(tagger.tags) = "json:is_pause"];    // 是否暂停
  int32 is_publish = 6 [(tagger.tags) = "json:is_publish"];
  int32 is_modify_unpublish = 7 [(tagger.tags) = "json:is_modify_unpublish"];                              // 是否允许修改未发布
  int32 is_opened = 8 [(tagger.tags) = "json:is_opened"];                                                  // 是否开放
  string stime = 9 [(tagger.tags) = "json:stime"];                                                         // 开始时间
  string etime = 10 [(tagger.tags) = "json:etime"];                                                        // 结束时间
  int32 type = 11 [(tagger.tags) = "json:type"];                                                           // 类型
  string schema = 12 [(tagger.tags) = "json:schema"];                                                      // 问卷架构
  string preview_schema = 13 [(tagger.tags) = "json:preview_schema"];                                      // 预览架构
  string settings = 14 [(tagger.tags) = "json:settings"];                                                  // 设置，使用Any类型来存储任意数据结构
  string web_settings = 15 [(tagger.tags) = "json:IsTimeLimit"];                                           // 网络设置
  string languages = 16 [(tagger.tags) = "json:languages"];                                                // 语言设置，使用Any类型来存储任意数据结构
  string hash_code = 17 [(tagger.tags) = "json:hash_code"];                                                // 哈希码
  int32 is_delete = 18 [(tagger.tags) = "json:is_delete"];                                                 // 是否删除
  string deltime = 19 [(tagger.tags) = "json:deltime"];                                                    // 删除时间
  string remark = 20 [(tagger.tags) = "json:remark"];                                                      // 备注
  string ctime = 21 [(tagger.tags) = "json:ctime"];                                                        // 创建时间
  string mtime = 22 [(tagger.tags) = "json:mtime"];                                                        // 修改时间
  string creator = 23 [(tagger.tags) = "json:creator"];                                                    // 创建者
  string editor = 24 [(tagger.tags) = "json:editor"];                                                      // 编辑者
  bool is_time_limit = 25 [(tagger.tags) = "json:is_time_limit"];                                          // 是否有时间限制
  repeated WebPath web_path_list = 26 [(tagger.tags) = "json:web_path_list"];                              // 网络路径列表
  int32 status = 27 [(tagger.tags) = "json:status"];                                                       // 状态
  QuestionStatisticsData question_statistics_data = 28 [(tagger.tags) = "json:question_statistics_data"];  // 问题统计数据，使用Any类型来存储任意数据结构
}

message WebPath {
  string region = 1;  // 地区或区域名称
  string web_path = 2;  // 网络路径
}

message QuestionBaseConfig {
  Title questionTitle = 1;
  Desc questionDesc = 2;
  Tip questionTip = 3;
}

message Title {
  string value = 1;
  string languKey = 2;
}

message Desc {
  string value = 1;
  string languKey = 2;
}

message Tip {
  string value = 1;
  string languKey = 2;
}

message ConfigProps {
  bool is_question = 1;
  string question_type = 2;
  string value_type = 3;
  string unique_key = 4;
  int32 question_id = 5;
  string statistics_method = 6;
  bool is_address = 7;
}

message QuestionLogicalConfig {
  VisibleSwitch visibleSwitch = 1;
  string conditionDesc = 2;
  DisplayLogic displayLogic = 3;
}

message VisibleSwitch {
}

message DisplayLogic {
  string currentQuestionId = 1;
  repeated Rule rules = 2;
  string relation = 3;
}

message RelatedContentConfig {
  string relation = 1;
  string chooseType = 2;
  repeated int32 content = 3;
}

message Rule {
  string key = 1;
  string id = 2;
  RelatedContentConfig relatedContentConfig = 3;
}

message QuestionComponentConfig {
  HeaderContent headerContent = 1;
  FooterContent FooterContent = 2;
  int32 dimension = 3;
  int32 startValue = 4;
  int32 sort = 5;
}

message HeaderContent {
  string value = 1;
  string languKey = 2;
}

message FooterContent {
  string value = 1;
  string languKey = 2;
}

message QuestionSelectConfig {
  int32 maxSelected = 1;
  repeated SelectOption selectOptions = 2;
  bool selectItemLayout = 3;
  bool selectItemRandomSort = 4;
}

message SelectOption {
  string value = 1;
}

message QuestionList {
  string componentName = 1;
  string componentTitle = 2;
  QuestionBaseConfig questionBaseConfig = 3;
  ConfigProps configProps = 4;
  QuestionLogicalConfig questionLogicalConfig = 5;
  QuestionComponentConfig questionComponentConfig = 6;
  QuestionSelectConfig questionSelectConfig = 7;
  RequestConfig requestConfig = 8;
  int32 wrapperIndex = 9;
}

message RequestConfig {
  string question_unique_key = 1;
  ConfigProps config_props = 2;
  string option_value = 3;
}

message QuestionConfig {
  RequestConfig request_config = 1;
  bool success = 2;
}

message PublishSurveyRequest {
  int64 id = 1;
  int64 client_id = 2;
}

message PublishSurveyResponse {
  bool success = 1;
}

message ShowSurveyRequest {
  int64 id = 1;
  int64 client_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message ShowSurveyResponse {
  Survey data = 2;
  bool success = 3;
}

message SurveyInputMethodListRequest {
  // 当前页码，默认1
  int32 page = 1;
  // 每页条数，默认10
  int32 page_size = 2;
  int64 survey_id = 3 [(google.api.field_behavior) = REQUIRED];
  RequestConfig request_config = 4;
  int64 client_id = 5 [(google.api.field_behavior) = REQUIRED];
  // 过滤空
  int32 filter_null_value = 6;
}

message SurveyInputMethodListResponse {
  repeated InputMethodListResponseData list = 1;
  int32 totalCounts = 2;
  int32 currentPage = 3;
}

message InputMethodListResponseData {
  // 设备ID
  string device_id = 1 [
    (tagger.tags) = "gorm:type:varchar;comment:设备ID",
    (tagger.tags) = "json:device_id"
  ];
  // 记录ID
  int64 id = 2 [
    (tagger.tags) = "gorm:type:varchar;comment:记录ID",
    (tagger.tags) = "json:id"
  ];
  // IP地址
  string ip = 3 [
    (tagger.tags) = "gorm:type:varchar;comment:IP地址",
    (tagger.tags) = "json:ip"
  ];
  // 用户openid
  string openid = 4 [
    (tagger.tags) = "gorm:type:varchar;comment:用户openid",
    (tagger.tags) = "json:openid"
  ];
  // 题目选项
  string option = 5 [
    (tagger.tags) = "gorm:type:varchar;comment:题目选项",
    (tagger.tags) = "json:option"
  ];
  // 题目ID
  string question = 6 [
    (tagger.tags) = "gorm:type:varchar;comment:题目ID",
    (tagger.tags) = "json:question"
  ];
  // 用户roleid
  string role_id = 7 [
    (tagger.tags) = "gorm:type:varchar;comment:用户roleid",
    (tagger.tags) = "json:role_id"
  ];
  // 问卷记录ID
  int64 survey_record_id = 8 [
    (tagger.tags) = "gorm:type:varchar;comment:问卷记录ID",
    (tagger.tags) = "json:survey_record_id"
  ];
  // 用户输入的文本
  string text = 9 [
    (tagger.tags) = "gorm:type:varchar;comment:用户输入的文本",
    (tagger.tags) = "json:text"
  ];
}

message OuterQuestionStatisticsData {
  int32 valid_answer_total = 1;
  int32 valid_user_total = 2;
  repeated Detail detail = 3;
}

message Detail {
  string uniqueKey = 1;
  string count = 2;
  string selectCount = 3;
  repeated OptionDetail detail = 4;
}

message OptionDetail {
  string value = 1;
  Label label = 2;
  int32 image_position = 3;
  string blank_fill = 4;
  Image image = 5;
  int32 count = 6;
  string proportion = 7;
}

message Label {
  string value = 1;
  string languKey = 2;
}

message Image {
  string languKey = 1;
}

message SurveyOverwriteSendReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷ID
  int64 survey_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyOverwriteSyncReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷
  string survey = 2 [(google.api.field_behavior) = REQUIRED];
}