// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_deliver.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取投放列表
type GetDeliverListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
}

func (x *GetDeliverListRequest) Reset() {
	*x = GetDeliverListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeliverListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeliverListRequest) ProtoMessage() {}

func (x *GetDeliverListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeliverListRequest.ProtoReflect.Descriptor instead.
func (*GetDeliverListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{0}
}

func (x *GetDeliverListRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

// 投放列表
type GetDeliverListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Deliver `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetDeliverListResponse) Reset() {
	*x = GetDeliverListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDeliverListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeliverListResponse) ProtoMessage() {}

func (x *GetDeliverListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeliverListResponse.ProtoReflect.Descriptor instead.
func (*GetDeliverListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{1}
}

func (x *GetDeliverListResponse) GetList() []*Deliver {
	if x != nil {
		return x.List
	}
	return nil
}

type Deliver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultLanguage     string `protobuf:"bytes,1,opt,name=default_language,proto3" json:"default_language,omitempty"`
	Region              string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	OfficialWebsiteHost string `protobuf:"bytes,3,opt,name=official_website_host,proto3" json:"official_website_host,omitempty"`
	SurveyHost          string `protobuf:"bytes,4,opt,name=survey_host,proto3" json:"survey_host,omitempty"`
}

func (x *Deliver) Reset() {
	*x = Deliver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deliver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deliver) ProtoMessage() {}

func (x *Deliver) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deliver.ProtoReflect.Descriptor instead.
func (*Deliver) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{2}
}

func (x *Deliver) GetDefaultLanguage() string {
	if x != nil {
		return x.DefaultLanguage
	}
	return ""
}

func (x *Deliver) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Deliver) GetOfficialWebsiteHost() string {
	if x != nil {
		return x.OfficialWebsiteHost
	}
	return ""
}

func (x *Deliver) GetSurveyHost() string {
	if x != nil {
		return x.SurveyHost
	}
	return ""
}

type GetZoneListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,proto3" json:"client_id,omitempty"`
}

func (x *GetZoneListRequest) Reset() {
	*x = GetZoneListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetZoneListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZoneListRequest) ProtoMessage() {}

func (x *GetZoneListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZoneListRequest.ProtoReflect.Descriptor instead.
func (*GetZoneListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{3}
}

func (x *GetZoneListRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type GetZoneListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DataItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetZoneListResponse) Reset() {
	*x = GetZoneListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetZoneListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetZoneListResponse) ProtoMessage() {}

func (x *GetZoneListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetZoneListResponse.ProtoReflect.Descriptor instead.
func (*GetZoneListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{4}
}

func (x *GetZoneListResponse) GetList() []*DataItem {
	if x != nil {
		return x.List
	}
	return nil
}

type DataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value int32  `protobuf:"varint,1,opt,name=value,proto3" json:"value"`
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
}

func (x *DataItem) Reset() {
	*x = DataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_deliver_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataItem) ProtoMessage() {}

func (x *DataItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_deliver_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataItem.ProtoReflect.Descriptor instead.
func (*DataItem) Descriptor() ([]byte, []int) {
	return file_proto_survey_deliver_proto_rawDescGZIP(), []int{5}
}

func (x *DataItem) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *DataItem) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

var File_proto_survey_deliver_proto protoreflect.FileDescriptor

var file_proto_survey_deliver_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74,
	0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x35, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x22, 0x4f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0xa5, 0x01, 0x0a, 0x07, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x12,
	0x2a, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x5f,
	0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x77, 0x65, 0x62,
	0x73, 0x69, 0x74, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22,
	0x4d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73,
	0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x58,
	0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x25, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0f, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_deliver_proto_rawDescOnce sync.Once
	file_proto_survey_deliver_proto_rawDescData = file_proto_survey_deliver_proto_rawDesc
)

func file_proto_survey_deliver_proto_rawDescGZIP() []byte {
	file_proto_survey_deliver_proto_rawDescOnce.Do(func() {
		file_proto_survey_deliver_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_deliver_proto_rawDescData)
	})
	return file_proto_survey_deliver_proto_rawDescData
}

var file_proto_survey_deliver_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_survey_deliver_proto_goTypes = []any{
	(*GetDeliverListRequest)(nil),  // 0: papegames.sparrow.survey.GetDeliverListRequest
	(*GetDeliverListResponse)(nil), // 1: papegames.sparrow.survey.GetDeliverListResponse
	(*Deliver)(nil),                // 2: papegames.sparrow.survey.Deliver
	(*GetZoneListRequest)(nil),     // 3: papegames.sparrow.survey.GetZoneListRequest
	(*GetZoneListResponse)(nil),    // 4: papegames.sparrow.survey.GetZoneListResponse
	(*DataItem)(nil),               // 5: papegames.sparrow.survey.DataItem
}
var file_proto_survey_deliver_proto_depIdxs = []int32{
	2, // 0: papegames.sparrow.survey.GetDeliverListResponse.list:type_name -> papegames.sparrow.survey.Deliver
	5, // 1: papegames.sparrow.survey.GetZoneListResponse.list:type_name -> papegames.sparrow.survey.DataItem
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_survey_deliver_proto_init() }
func file_proto_survey_deliver_proto_init() {
	if File_proto_survey_deliver_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_deliver_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*GetDeliverListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_deliver_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetDeliverListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_deliver_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Deliver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_deliver_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetZoneListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_deliver_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*GetZoneListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_deliver_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_deliver_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_deliver_proto_goTypes,
		DependencyIndexes: file_proto_survey_deliver_proto_depIdxs,
		MessageInfos:      file_proto_survey_deliver_proto_msgTypes,
	}.Build()
	File_proto_survey_deliver_proto = out.File
	file_proto_survey_deliver_proto_rawDesc = nil
	file_proto_survey_deliver_proto_goTypes = nil
	file_proto_survey_deliver_proto_depIdxs = nil
}
