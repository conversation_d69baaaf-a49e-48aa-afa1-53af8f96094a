syntax = "proto3";

package papegames.sparrow.survey;

// import "google/api/field_behavior.proto";
// import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message SurveyRecordDetailListRes {
  option (tagger.disable_omitempty) = true;
  repeated SurveyRecordDetail list = 1;
}

message SurveyRecordDetail {
  option (tagger.disable_omitempty) = true;

  // ID，自增主键，创建时此参数不需要传
  int64 id = 1 [
    (tagger.tags) = "gorm:primaryKey;type:int(11) default 0;comment:主键ID"
  ];

  // 用户UID
  string uid = 2 [
    (tagger.tags) = "gorm:type:varchar(128);comment:用户UID",
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:uid"
  ];

  // 用户roleid
  string roleid = 3 [
    (tagger.tags) = "gorm:type:varchar(128);comment:用户roleid",
    (tagger.tags) = "json:roleid"
  ];

  // 问卷记录ID
  int64 survey_record_id = 4 [
    (tagger.tags) = "gorm:type:int(11);comment:问卷记录ID",
    (tagger.tags) = "json:survey_record_id"
  ];

  // 问卷题目ID
  string question = 5 [
    (tagger.tags) = "gorm:type:varchar;comment:问卷题目ID",
    (tagger.tags) = "json:question"
  ];

  // 选择类题目用户选项
  string option = 6 [
    (tagger.tags) = "gorm:type:varchar;comment:选择类题目用户选项",
    (tagger.tags) = "json:device_id"
  ];

  // 输入类题目/自定义选项类题目，用户输入内容
  string text = 7 [
    (tagger.tags) = "gorm:type:varchar;comment:输入类题目/自定义选项类题目，用户输入内容",
    (tagger.tags) = "json:text"
  ];
}

message WebSettings {
  string login_type = 1;
  bool is_end_preview = 2;
  bool is_go_on_answer = 3;
  repeated string language_list = 4;
  Materialsconfig materials_config = 5;
}

message Settings {
  message Timelimitconfig {
    bool isTimeLimit = 1;
  }

  message Answertimesconfig {
    uint32 limitType = 1;
    uint32 times = 2;
  }

  // 频率空配置
  message PeriodicControl {
    // 开关，默认关闭
    bool enable = 1;
    // 循环值
    int32 interval = 2;
    // unit
    enum Unit {
      Unit_Unknown = 0;
      Unit_Minute = 1;
      Unit_Hour = 2;
      Unit_Day = 3;
    }
    // 时间单位
    Unit unit = 3;
  }

  message Baseruleconfig {
    string loginType = 1;
    Timelimitconfig timeLimitConfig = 2;
    bool isEndPreview = 3;
    bool isGoOnAnswer = 4;
    Answertimesconfig answerTimesConfig = 5;
    repeated string languageList = 6;
    repeated string deliverList = 7;
    PeriodicControl periodicControl = 8;
  }

  message Answerlimitconfig {
    string limitType = 1;
  }

  Baseruleconfig baseRuleConfig = 1;
  Materialsconfig materialsConfig = 4;

}

message Materialsconfig {
  bool auto_latest_material = 1;
  string material_version = 2;
}

