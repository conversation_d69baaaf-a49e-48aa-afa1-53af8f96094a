// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v4.25.1
// source: proto/survey.proto

package proto

import (
	context "context"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	SurveyService_Health_FullMethodName                        = "/papegames.sparrow.survey.SurveyService/Health"
	SurveyService_SurveyExportTaskList_FullMethodName          = "/papegames.sparrow.survey.SurveyService/SurveyExportTaskList"
	SurveyService_CreateSurveyExportTask_FullMethodName        = "/papegames.sparrow.survey.SurveyService/CreateSurveyExportTask"
	SurveyService_DelSurveyExportTask_FullMethodName           = "/papegames.sparrow.survey.SurveyService/DelSurveyExportTask"
	SurveyService_ResetSurveyExportTaskStatus_FullMethodName   = "/papegames.sparrow.survey.SurveyService/ResetSurveyExportTaskStatus"
	SurveyService_SurveyRecordList_FullMethodName              = "/papegames.sparrow.survey.SurveyService/SurveyRecordList"
	SurveyService_SurveyRecordListV2_FullMethodName            = "/papegames.sparrow.survey.SurveyService/SurveyRecordListV2"
	SurveyService_DelSurveyRecord_FullMethodName               = "/papegames.sparrow.survey.SurveyService/DelSurveyRecord"
	SurveyService_SetValidSurveyRecord_FullMethodName          = "/papegames.sparrow.survey.SurveyService/SetValidSurveyRecord"
	SurveyService_SetInvalidSurveyRecord_FullMethodName        = "/papegames.sparrow.survey.SurveyService/SetInvalidSurveyRecord"
	SurveyService_InValidSurveyRecordList_FullMethodName       = "/papegames.sparrow.survey.SurveyService/InValidSurveyRecordList"
	SurveyService_SurveyPreview_FullMethodName                 = "/papegames.sparrow.survey.SurveyService/SurveyPreview"
	SurveyService_SurveyRecordDetail_FullMethodName            = "/papegames.sparrow.survey.SurveyService/SurveyRecordDetail"
	SurveyService_SurveyList_FullMethodName                    = "/papegames.sparrow.survey.SurveyService/SurveyList"
	SurveyService_CreateSurvey_FullMethodName                  = "/papegames.sparrow.survey.SurveyService/CreateSurvey"
	SurveyService_SurveyStatistics_FullMethodName              = "/papegames.sparrow.survey.SurveyService/SurveyStatistics"
	SurveyService_GetSurveyStatisticsList_FullMethodName       = "/papegames.sparrow.survey.SurveyService/GetSurveyStatisticsList"
	SurveyService_SurveyShow_FullMethodName                    = "/papegames.sparrow.survey.SurveyService/SurveyShow"
	SurveyService_DeleteSurvey_FullMethodName                  = "/papegames.sparrow.survey.SurveyService/DeleteSurvey"
	SurveyService_CopySurvey_FullMethodName                    = "/papegames.sparrow.survey.SurveyService/CopySurvey"
	SurveyService_SyncSurvey_FullMethodName                    = "/papegames.sparrow.survey.SurveyService/SyncSurvey"
	SurveyService_ImpSurvey_FullMethodName                     = "/papegames.sparrow.survey.SurveyService/ImpSurvey"
	SurveyService_SetStatusSurvey_FullMethodName               = "/papegames.sparrow.survey.SurveyService/SetStatusSurvey"
	SurveyService_SurveyRecycleList_FullMethodName             = "/papegames.sparrow.survey.SurveyService/SurveyRecycleList"
	SurveyService_DeleteSurveyRecycle_FullMethodName           = "/papegames.sparrow.survey.SurveyService/DeleteSurveyRecycle"
	SurveyService_RecoverSurveyRecycle_FullMethodName          = "/papegames.sparrow.survey.SurveyService/RecoverSurveyRecycle"
	SurveyService_ClearAllSurveyRecycle_FullMethodName         = "/papegames.sparrow.survey.SurveyService/ClearAllSurveyRecycle"
	SurveyService_RecoverAllSurveyRecycle_FullMethodName       = "/papegames.sparrow.survey.SurveyService/RecoverAllSurveyRecycle"
	SurveyService_GetDeliverList_FullMethodName                = "/papegames.sparrow.survey.SurveyService/GetDeliverList"
	SurveyService_GetZoneList_FullMethodName                   = "/papegames.sparrow.survey.SurveyService/GetZoneList"
	SurveyService_UpdateSurvey_FullMethodName                  = "/papegames.sparrow.survey.SurveyService/UpdateSurvey"
	SurveyService_PublishSurvey_FullMethodName                 = "/papegames.sparrow.survey.SurveyService/PublishSurvey"
	SurveyService_GetInputMethodList_FullMethodName            = "/papegames.sparrow.survey.SurveyService/GetInputMethodList"
	SurveyService_SurveyStatisticsDetail_FullMethodName        = "/papegames.sparrow.survey.SurveyService/SurveyStatisticsDetail"
	SurveyService_SurveyStatisticsDetailOld_FullMethodName     = "/papegames.sparrow.survey.SurveyService/SurveyStatisticsDetailOld"
	SurveyService_GetUserInfo_FullMethodName                   = "/papegames.sparrow.survey.SurveyService/getUserInfo"
	SurveyService_UserCheck_FullMethodName                     = "/papegames.sparrow.survey.SurveyService/userCheck"
	SurveyService_StatisticsUpdate_FullMethodName              = "/papegames.sparrow.survey.SurveyService/StatisticsUpdate"
	SurveyService_GetLatestSurveyBySurveyId_FullMethodName     = "/papegames.sparrow.survey.SurveyService/GetLatestSurveyBySurveyId"
	SurveyService_GetValidRedeemConfigList_FullMethodName      = "/papegames.sparrow.survey.SurveyService/GetValidRedeemConfigList"
	SurveyService_GetPreAwardTemplateList_FullMethodName       = "/papegames.sparrow.survey.SurveyService/GetPreAwardTemplateList"
	SurveyService_Upload_FullMethodName                        = "/papegames.sparrow.survey.SurveyService/Upload"
	SurveyService_SurveyViewCreate_FullMethodName              = "/papegames.sparrow.survey.SurveyService/SurveyViewCreate"
	SurveyService_SurveyViewList_FullMethodName                = "/papegames.sparrow.survey.SurveyService/SurveyViewList"
	SurveyService_SurveyViewUpdate_FullMethodName              = "/papegames.sparrow.survey.SurveyService/SurveyViewUpdate"
	SurveyService_SurveyViewDelete_FullMethodName              = "/papegames.sparrow.survey.SurveyService/SurveyViewDelete"
	SurveyService_SurveyGroupCreate_FullMethodName             = "/papegames.sparrow.survey.SurveyService/SurveyGroupCreate"
	SurveyService_SurveyGroupDetail_FullMethodName             = "/papegames.sparrow.survey.SurveyService/SurveyGroupDetail"
	SurveyService_SurveyGroupUpdate_FullMethodName             = "/papegames.sparrow.survey.SurveyService/SurveyGroupUpdate"
	SurveyService_SurveyGroupList_FullMethodName               = "/papegames.sparrow.survey.SurveyService/SurveyGroupList"
	SurveyService_SurveyGroupSubUpdate_FullMethodName          = "/papegames.sparrow.survey.SurveyService/SurveyGroupSubUpdate"
	SurveyService_SurveyOverwriteSend_FullMethodName           = "/papegames.sparrow.survey.SurveyService/SurveyOverwriteSend"
	SurveyService_SurveyOverwriteSync_FullMethodName           = "/papegames.sparrow.survey.SurveyService/SurveyOverwriteSync"
	SurveyService_SurveyGroupOverwriteSend_FullMethodName      = "/papegames.sparrow.survey.SurveyService/SurveyGroupOverwriteSend"
	SurveyService_SurveyGroupOverwriteSync_FullMethodName      = "/papegames.sparrow.survey.SurveyService/SurveyGroupOverwriteSync"
	SurveyService_SurveyExportUserClusterSubmit_FullMethodName = "/papegames.sparrow.survey.SurveyService/SurveyExportUserClusterSubmit"
	SurveyService_SurveyExportHeaders_FullMethodName           = "/papegames.sparrow.survey.SurveyService/SurveyExportHeaders"
)

// SurveyServiceClient is the client API for SurveyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// This API represents survey service.
type SurveyServiceClient interface {
	Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 导出任务列表
	SurveyExportTaskList(ctx context.Context, in *SurveyExportTaskListRequest, opts ...grpc.CallOption) (*SurveyExportTaskListResponse, error)
	// 创建导出任务
	CreateSurveyExportTask(ctx context.Context, in *SurveyExportTask, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 删除导出任务
	DelSurveyExportTask(ctx context.Context, in *DelSurveyExportTaskReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 重置导出任务状态
	ResetSurveyExportTaskStatus(ctx context.Context, in *SurveyExportTaskDetailsReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 回收答卷列表
	SurveyRecordList(ctx context.Context, in *SurveyRecordListV2Request, opts ...grpc.CallOption) (*SurveyRecordListResponse, error)
	// 回收答卷列表
	SurveyRecordListV2(ctx context.Context, in *SurveyRecordListV2Request, opts ...grpc.CallOption) (*xtype.RawMessage, error)
	// 答卷列表删除
	DelSurveyRecord(ctx context.Context, in *SurveyRecordDetailsRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 标记答卷记录有效
	SetValidSurveyRecord(ctx context.Context, in *SurveyRecordsRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 标记答卷记录无效
	SetInvalidSurveyRecord(ctx context.Context, in *SetValidSurveyRecordRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 无效答卷列表
	InValidSurveyRecordList(ctx context.Context, in *SurveyRecordListRequest, opts ...grpc.CallOption) (*SurveyRecordListResponse, error)
	// 问卷预览
	SurveyPreview(ctx context.Context, in *SurveyPreviewReq, opts ...grpc.CallOption) (*Survey, error)
	// 查看答卷-问卷详情
	SurveyRecordDetail(ctx context.Context, in *SurveyRecordConfDetailsReq, opts ...grpc.CallOption) (*SurveyRecordConfDetailsRes, error)
	// 问卷列表
	SurveyList(ctx context.Context, in *SurveyListRequest, opts ...grpc.CallOption) (*SurveyListResponse, error)
	// 问卷创建
	CreateSurvey(ctx context.Context, in *CreateSurveyRequest, opts ...grpc.CallOption) (*SurveyResponse, error)
	// 问卷规则-问卷管理-问卷详情
	SurveyStatistics(ctx context.Context, in *SurveyStatisticsRequest, opts ...grpc.CallOption) (*SurveyStatisticsResponse, error)
	// 问卷统计报告-列表
	GetSurveyStatisticsList(ctx context.Context, in *SurveyDetailRequest, opts ...grpc.CallOption) (*SurveyDetailResponse, error)
	// 问卷展示
	SurveyShow(ctx context.Context, in *ShowSurveyRequest, opts ...grpc.CallOption) (*Survey, error)
	// 问卷删除
	DeleteSurvey(ctx context.Context, in *SurveyDelRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 复制问卷
	CopySurvey(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*SurveyResponse, error)
	// 同步问卷
	SyncSurvey(ctx context.Context, in *SyncSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 同步问卷写入接口
	ImpSurvey(ctx context.Context, in *ImpSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 问卷暂停/开启作答
	SetStatusSurvey(ctx context.Context, in *SurveySetStatusRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 回收站-问卷列表
	SurveyRecycleList(ctx context.Context, in *SurveyListRequest, opts ...grpc.CallOption) (*SurveyListResponse, error)
	// 彻底删除
	DeleteSurveyRecycle(ctx context.Context, in *SurveyDelRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 恢复问卷
	RecoverSurveyRecycle(ctx context.Context, in *RecoverSurveyRecycleReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 清空回收站
	ClearAllSurveyRecycle(ctx context.Context, in *ClearAllSurveyRecycleRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 恢复所有问卷
	RecoverAllSurveyRecycle(ctx context.Context, in *Survey, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 获取投放列表
	GetDeliverList(ctx context.Context, in *GetDeliverListRequest, opts ...grpc.CallOption) (*GetDeliverListResponse, error)
	// 获取区服列表
	GetZoneList(ctx context.Context, in *GetZoneListRequest, opts ...grpc.CallOption) (*GetZoneListResponse, error)
	UpdateSurvey(ctx context.Context, in *UpdateSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	PublishSurvey(ctx context.Context, in *PublishSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	GetInputMethodList(ctx context.Context, in *SurveyInputMethodListRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error)
	// 问卷统计详情
	SurveyStatisticsDetail(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error)
	// 问卷统计详情 - v2
	SurveyStatisticsDetailOld(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error)
	GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoResponse, error)
	UserCheck(ctx context.Context, in *UserCheckRequest, opts ...grpc.CallOption) (*UserCheckResponse, error)
	StatisticsUpdate(ctx context.Context, in *StatisticsUpdateRequest, opts ...grpc.CallOption) (*xtype.Empty, error)
	GetLatestSurveyBySurveyId(ctx context.Context, in *GetLatestSurveyBySurveyIdRequest, opts ...grpc.CallOption) (*GetLatestSurveyBySurveyIdResponse, error)
	// 获取兑换码列表
	GetValidRedeemConfigList(ctx context.Context, in *ValidRedeemConfigRequest, opts ...grpc.CallOption) (*ValidRedeemConfigResponse, error)
	// 获取平台邮件模板列表
	GetPreAwardTemplateList(ctx context.Context, in *PreAwardTemplateRequest, opts ...grpc.CallOption) (*PreAwardTemplateResponse, error)
	Upload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*UploadResponse, error)
	// 问卷筛选器创建
	SurveyViewCreate(ctx context.Context, in *SurveyViewCreateReq, opts ...grpc.CallOption) (*SurveyViewCreateRes, error)
	// 问卷筛选器列表
	SurveyViewList(ctx context.Context, in *SurveyViewListReq, opts ...grpc.CallOption) (*SurveyViewListRes, error)
	// 问卷筛选器编辑
	SurveyViewUpdate(ctx context.Context, in *SurveyView, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 问卷筛选器删除
	SurveyViewDelete(ctx context.Context, in *SurveyViewDeleteReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 问卷组 - 新建
	SurveyGroupCreate(ctx context.Context, in *SurveyGroupCreateReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 问卷组 - 详情
	SurveyGroupDetail(ctx context.Context, in *SurveyGroupDetailReq, opts ...grpc.CallOption) (*CmsSurveyGroupInfo, error)
	// 问卷组 - 编辑
	SurveyGroupUpdate(ctx context.Context, in *SurveyGroupUpdateReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	// 问卷组 - 列表
	SurveyGroupList(ctx context.Context, in *SurveyGroupListReq, opts ...grpc.CallOption) (*SurveyGroupListRes, error)
	// 问卷组 - 部分字段更新
	SurveyGroupSubUpdate(ctx context.Context, in *SurveyGroupSubUpdateReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyOverwriteSend(ctx context.Context, in *SurveyOverwriteSendReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyOverwriteSync(ctx context.Context, in *SurveyOverwriteSyncReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyGroupOverwriteSend(ctx context.Context, in *SurveyGroupOverwriteSendReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyGroupOverwriteSync(ctx context.Context, in *SurveyGroupOverwriteSyncReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyExportUserClusterSubmit(ctx context.Context, in *SurveyExportUserClusterSubmitReq, opts ...grpc.CallOption) (*xtype.Empty, error)
	SurveyExportHeaders(ctx context.Context, in *SurveyExportHeadersReq, opts ...grpc.CallOption) (*SurveyExportHeadersRes, error)
}

type surveyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurveyServiceClient(cc grpc.ClientConnInterface) SurveyServiceClient {
	return &surveyServiceClient{cc}
}

func (c *surveyServiceClient) Health(ctx context.Context, in *HealthRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_Health_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyExportTaskList(ctx context.Context, in *SurveyExportTaskListRequest, opts ...grpc.CallOption) (*SurveyExportTaskListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyExportTaskListResponse)
	err := c.cc.Invoke(ctx, SurveyService_SurveyExportTaskList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) CreateSurveyExportTask(ctx context.Context, in *SurveyExportTask, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_CreateSurveyExportTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) DelSurveyExportTask(ctx context.Context, in *DelSurveyExportTaskReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_DelSurveyExportTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) ResetSurveyExportTaskStatus(ctx context.Context, in *SurveyExportTaskDetailsReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_ResetSurveyExportTaskStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyRecordList(ctx context.Context, in *SurveyRecordListV2Request, opts ...grpc.CallOption) (*SurveyRecordListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyRecordListResponse)
	err := c.cc.Invoke(ctx, SurveyService_SurveyRecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyRecordListV2(ctx context.Context, in *SurveyRecordListV2Request, opts ...grpc.CallOption) (*xtype.RawMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.RawMessage)
	err := c.cc.Invoke(ctx, SurveyService_SurveyRecordListV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) DelSurveyRecord(ctx context.Context, in *SurveyRecordDetailsRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_DelSurveyRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SetValidSurveyRecord(ctx context.Context, in *SurveyRecordsRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SetValidSurveyRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SetInvalidSurveyRecord(ctx context.Context, in *SetValidSurveyRecordRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SetInvalidSurveyRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) InValidSurveyRecordList(ctx context.Context, in *SurveyRecordListRequest, opts ...grpc.CallOption) (*SurveyRecordListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyRecordListResponse)
	err := c.cc.Invoke(ctx, SurveyService_InValidSurveyRecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyPreview(ctx context.Context, in *SurveyPreviewReq, opts ...grpc.CallOption) (*Survey, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Survey)
	err := c.cc.Invoke(ctx, SurveyService_SurveyPreview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyRecordDetail(ctx context.Context, in *SurveyRecordConfDetailsReq, opts ...grpc.CallOption) (*SurveyRecordConfDetailsRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyRecordConfDetailsRes)
	err := c.cc.Invoke(ctx, SurveyService_SurveyRecordDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyList(ctx context.Context, in *SurveyListRequest, opts ...grpc.CallOption) (*SurveyListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyListResponse)
	err := c.cc.Invoke(ctx, SurveyService_SurveyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) CreateSurvey(ctx context.Context, in *CreateSurveyRequest, opts ...grpc.CallOption) (*SurveyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyResponse)
	err := c.cc.Invoke(ctx, SurveyService_CreateSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyStatistics(ctx context.Context, in *SurveyStatisticsRequest, opts ...grpc.CallOption) (*SurveyStatisticsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyStatisticsResponse)
	err := c.cc.Invoke(ctx, SurveyService_SurveyStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetSurveyStatisticsList(ctx context.Context, in *SurveyDetailRequest, opts ...grpc.CallOption) (*SurveyDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyDetailResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetSurveyStatisticsList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyShow(ctx context.Context, in *ShowSurveyRequest, opts ...grpc.CallOption) (*Survey, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Survey)
	err := c.cc.Invoke(ctx, SurveyService_SurveyShow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) DeleteSurvey(ctx context.Context, in *SurveyDelRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_DeleteSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) CopySurvey(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*SurveyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyResponse)
	err := c.cc.Invoke(ctx, SurveyService_CopySurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SyncSurvey(ctx context.Context, in *SyncSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SyncSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) ImpSurvey(ctx context.Context, in *ImpSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_ImpSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SetStatusSurvey(ctx context.Context, in *SurveySetStatusRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SetStatusSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyRecycleList(ctx context.Context, in *SurveyListRequest, opts ...grpc.CallOption) (*SurveyListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyListResponse)
	err := c.cc.Invoke(ctx, SurveyService_SurveyRecycleList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) DeleteSurveyRecycle(ctx context.Context, in *SurveyDelRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_DeleteSurveyRecycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) RecoverSurveyRecycle(ctx context.Context, in *RecoverSurveyRecycleReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_RecoverSurveyRecycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) ClearAllSurveyRecycle(ctx context.Context, in *ClearAllSurveyRecycleRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_ClearAllSurveyRecycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) RecoverAllSurveyRecycle(ctx context.Context, in *Survey, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_RecoverAllSurveyRecycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetDeliverList(ctx context.Context, in *GetDeliverListRequest, opts ...grpc.CallOption) (*GetDeliverListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeliverListResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetDeliverList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetZoneList(ctx context.Context, in *GetZoneListRequest, opts ...grpc.CallOption) (*GetZoneListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetZoneListResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetZoneList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) UpdateSurvey(ctx context.Context, in *UpdateSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_UpdateSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) PublishSurvey(ctx context.Context, in *PublishSurveyRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_PublishSurvey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetInputMethodList(ctx context.Context, in *SurveyInputMethodListRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.RawMessage)
	err := c.cc.Invoke(ctx, SurveyService_GetInputMethodList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyStatisticsDetail(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.RawMessage)
	err := c.cc.Invoke(ctx, SurveyService_SurveyStatisticsDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyStatisticsDetailOld(ctx context.Context, in *SurveyRequest, opts ...grpc.CallOption) (*xtype.RawMessage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.RawMessage)
	err := c.cc.Invoke(ctx, SurveyService_SurveyStatisticsDetailOld_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) UserCheck(ctx context.Context, in *UserCheckRequest, opts ...grpc.CallOption) (*UserCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserCheckResponse)
	err := c.cc.Invoke(ctx, SurveyService_UserCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) StatisticsUpdate(ctx context.Context, in *StatisticsUpdateRequest, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_StatisticsUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetLatestSurveyBySurveyId(ctx context.Context, in *GetLatestSurveyBySurveyIdRequest, opts ...grpc.CallOption) (*GetLatestSurveyBySurveyIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLatestSurveyBySurveyIdResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetLatestSurveyBySurveyId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetValidRedeemConfigList(ctx context.Context, in *ValidRedeemConfigRequest, opts ...grpc.CallOption) (*ValidRedeemConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidRedeemConfigResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetValidRedeemConfigList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) GetPreAwardTemplateList(ctx context.Context, in *PreAwardTemplateRequest, opts ...grpc.CallOption) (*PreAwardTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreAwardTemplateResponse)
	err := c.cc.Invoke(ctx, SurveyService_GetPreAwardTemplateList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) Upload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*UploadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadResponse)
	err := c.cc.Invoke(ctx, SurveyService_Upload_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyViewCreate(ctx context.Context, in *SurveyViewCreateReq, opts ...grpc.CallOption) (*SurveyViewCreateRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyViewCreateRes)
	err := c.cc.Invoke(ctx, SurveyService_SurveyViewCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyViewList(ctx context.Context, in *SurveyViewListReq, opts ...grpc.CallOption) (*SurveyViewListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyViewListRes)
	err := c.cc.Invoke(ctx, SurveyService_SurveyViewList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyViewUpdate(ctx context.Context, in *SurveyView, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyViewUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyViewDelete(ctx context.Context, in *SurveyViewDeleteReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyViewDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupCreate(ctx context.Context, in *SurveyGroupCreateReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupDetail(ctx context.Context, in *SurveyGroupDetailReq, opts ...grpc.CallOption) (*CmsSurveyGroupInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CmsSurveyGroupInfo)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupUpdate(ctx context.Context, in *SurveyGroupUpdateReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupList(ctx context.Context, in *SurveyGroupListReq, opts ...grpc.CallOption) (*SurveyGroupListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyGroupListRes)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupSubUpdate(ctx context.Context, in *SurveyGroupSubUpdateReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupSubUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyOverwriteSend(ctx context.Context, in *SurveyOverwriteSendReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyOverwriteSend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyOverwriteSync(ctx context.Context, in *SurveyOverwriteSyncReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyOverwriteSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupOverwriteSend(ctx context.Context, in *SurveyGroupOverwriteSendReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupOverwriteSend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyGroupOverwriteSync(ctx context.Context, in *SurveyGroupOverwriteSyncReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyGroupOverwriteSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyExportUserClusterSubmit(ctx context.Context, in *SurveyExportUserClusterSubmitReq, opts ...grpc.CallOption) (*xtype.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(xtype.Empty)
	err := c.cc.Invoke(ctx, SurveyService_SurveyExportUserClusterSubmit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyServiceClient) SurveyExportHeaders(ctx context.Context, in *SurveyExportHeadersReq, opts ...grpc.CallOption) (*SurveyExportHeadersRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurveyExportHeadersRes)
	err := c.cc.Invoke(ctx, SurveyService_SurveyExportHeaders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurveyServiceServer is the server API for SurveyService service.
// All implementations must embed UnimplementedSurveyServiceServer
// for forward compatibility
//
// This API represents survey service.
type SurveyServiceServer interface {
	Health(context.Context, *HealthRequest) (*xtype.Empty, error)
	// 导出任务列表
	SurveyExportTaskList(context.Context, *SurveyExportTaskListRequest) (*SurveyExportTaskListResponse, error)
	// 创建导出任务
	CreateSurveyExportTask(context.Context, *SurveyExportTask) (*xtype.Empty, error)
	// 删除导出任务
	DelSurveyExportTask(context.Context, *DelSurveyExportTaskReq) (*xtype.Empty, error)
	// 重置导出任务状态
	ResetSurveyExportTaskStatus(context.Context, *SurveyExportTaskDetailsReq) (*xtype.Empty, error)
	// 回收答卷列表
	SurveyRecordList(context.Context, *SurveyRecordListV2Request) (*SurveyRecordListResponse, error)
	// 回收答卷列表
	SurveyRecordListV2(context.Context, *SurveyRecordListV2Request) (*xtype.RawMessage, error)
	// 答卷列表删除
	DelSurveyRecord(context.Context, *SurveyRecordDetailsRequest) (*xtype.Empty, error)
	// 标记答卷记录有效
	SetValidSurveyRecord(context.Context, *SurveyRecordsRequest) (*xtype.Empty, error)
	// 标记答卷记录无效
	SetInvalidSurveyRecord(context.Context, *SetValidSurveyRecordRequest) (*xtype.Empty, error)
	// 无效答卷列表
	InValidSurveyRecordList(context.Context, *SurveyRecordListRequest) (*SurveyRecordListResponse, error)
	// 问卷预览
	SurveyPreview(context.Context, *SurveyPreviewReq) (*Survey, error)
	// 查看答卷-问卷详情
	SurveyRecordDetail(context.Context, *SurveyRecordConfDetailsReq) (*SurveyRecordConfDetailsRes, error)
	// 问卷列表
	SurveyList(context.Context, *SurveyListRequest) (*SurveyListResponse, error)
	// 问卷创建
	CreateSurvey(context.Context, *CreateSurveyRequest) (*SurveyResponse, error)
	// 问卷规则-问卷管理-问卷详情
	SurveyStatistics(context.Context, *SurveyStatisticsRequest) (*SurveyStatisticsResponse, error)
	// 问卷统计报告-列表
	GetSurveyStatisticsList(context.Context, *SurveyDetailRequest) (*SurveyDetailResponse, error)
	// 问卷展示
	SurveyShow(context.Context, *ShowSurveyRequest) (*Survey, error)
	// 问卷删除
	DeleteSurvey(context.Context, *SurveyDelRequest) (*xtype.Empty, error)
	// 复制问卷
	CopySurvey(context.Context, *SurveyRequest) (*SurveyResponse, error)
	// 同步问卷
	SyncSurvey(context.Context, *SyncSurveyRequest) (*xtype.Empty, error)
	// 同步问卷写入接口
	ImpSurvey(context.Context, *ImpSurveyRequest) (*xtype.Empty, error)
	// 问卷暂停/开启作答
	SetStatusSurvey(context.Context, *SurveySetStatusRequest) (*xtype.Empty, error)
	// 回收站-问卷列表
	SurveyRecycleList(context.Context, *SurveyListRequest) (*SurveyListResponse, error)
	// 彻底删除
	DeleteSurveyRecycle(context.Context, *SurveyDelRequest) (*xtype.Empty, error)
	// 恢复问卷
	RecoverSurveyRecycle(context.Context, *RecoverSurveyRecycleReq) (*xtype.Empty, error)
	// 清空回收站
	ClearAllSurveyRecycle(context.Context, *ClearAllSurveyRecycleRequest) (*xtype.Empty, error)
	// 恢复所有问卷
	RecoverAllSurveyRecycle(context.Context, *Survey) (*xtype.Empty, error)
	// 获取投放列表
	GetDeliverList(context.Context, *GetDeliverListRequest) (*GetDeliverListResponse, error)
	// 获取区服列表
	GetZoneList(context.Context, *GetZoneListRequest) (*GetZoneListResponse, error)
	UpdateSurvey(context.Context, *UpdateSurveyRequest) (*xtype.Empty, error)
	PublishSurvey(context.Context, *PublishSurveyRequest) (*xtype.Empty, error)
	GetInputMethodList(context.Context, *SurveyInputMethodListRequest) (*xtype.RawMessage, error)
	// 问卷统计详情
	SurveyStatisticsDetail(context.Context, *SurveyRequest) (*xtype.RawMessage, error)
	// 问卷统计详情 - v2
	SurveyStatisticsDetailOld(context.Context, *SurveyRequest) (*xtype.RawMessage, error)
	GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoResponse, error)
	UserCheck(context.Context, *UserCheckRequest) (*UserCheckResponse, error)
	StatisticsUpdate(context.Context, *StatisticsUpdateRequest) (*xtype.Empty, error)
	GetLatestSurveyBySurveyId(context.Context, *GetLatestSurveyBySurveyIdRequest) (*GetLatestSurveyBySurveyIdResponse, error)
	// 获取兑换码列表
	GetValidRedeemConfigList(context.Context, *ValidRedeemConfigRequest) (*ValidRedeemConfigResponse, error)
	// 获取平台邮件模板列表
	GetPreAwardTemplateList(context.Context, *PreAwardTemplateRequest) (*PreAwardTemplateResponse, error)
	Upload(context.Context, *UploadRequest) (*UploadResponse, error)
	// 问卷筛选器创建
	SurveyViewCreate(context.Context, *SurveyViewCreateReq) (*SurveyViewCreateRes, error)
	// 问卷筛选器列表
	SurveyViewList(context.Context, *SurveyViewListReq) (*SurveyViewListRes, error)
	// 问卷筛选器编辑
	SurveyViewUpdate(context.Context, *SurveyView) (*xtype.Empty, error)
	// 问卷筛选器删除
	SurveyViewDelete(context.Context, *SurveyViewDeleteReq) (*xtype.Empty, error)
	// 问卷组 - 新建
	SurveyGroupCreate(context.Context, *SurveyGroupCreateReq) (*xtype.Empty, error)
	// 问卷组 - 详情
	SurveyGroupDetail(context.Context, *SurveyGroupDetailReq) (*CmsSurveyGroupInfo, error)
	// 问卷组 - 编辑
	SurveyGroupUpdate(context.Context, *SurveyGroupUpdateReq) (*xtype.Empty, error)
	// 问卷组 - 列表
	SurveyGroupList(context.Context, *SurveyGroupListReq) (*SurveyGroupListRes, error)
	// 问卷组 - 部分字段更新
	SurveyGroupSubUpdate(context.Context, *SurveyGroupSubUpdateReq) (*xtype.Empty, error)
	SurveyOverwriteSend(context.Context, *SurveyOverwriteSendReq) (*xtype.Empty, error)
	SurveyOverwriteSync(context.Context, *SurveyOverwriteSyncReq) (*xtype.Empty, error)
	SurveyGroupOverwriteSend(context.Context, *SurveyGroupOverwriteSendReq) (*xtype.Empty, error)
	SurveyGroupOverwriteSync(context.Context, *SurveyGroupOverwriteSyncReq) (*xtype.Empty, error)
	SurveyExportUserClusterSubmit(context.Context, *SurveyExportUserClusterSubmitReq) (*xtype.Empty, error)
	SurveyExportHeaders(context.Context, *SurveyExportHeadersReq) (*SurveyExportHeadersRes, error)
	mustEmbedUnimplementedSurveyServiceServer()
}

// UnimplementedSurveyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSurveyServiceServer struct {
}

func (UnimplementedSurveyServiceServer) Health(context.Context, *HealthRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Health not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyExportTaskList(context.Context, *SurveyExportTaskListRequest) (*SurveyExportTaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyExportTaskList not implemented")
}
func (UnimplementedSurveyServiceServer) CreateSurveyExportTask(context.Context, *SurveyExportTask) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSurveyExportTask not implemented")
}
func (UnimplementedSurveyServiceServer) DelSurveyExportTask(context.Context, *DelSurveyExportTaskReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelSurveyExportTask not implemented")
}
func (UnimplementedSurveyServiceServer) ResetSurveyExportTaskStatus(context.Context, *SurveyExportTaskDetailsReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetSurveyExportTaskStatus not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyRecordList(context.Context, *SurveyRecordListV2Request) (*SurveyRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyRecordList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyRecordListV2(context.Context, *SurveyRecordListV2Request) (*xtype.RawMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyRecordListV2 not implemented")
}
func (UnimplementedSurveyServiceServer) DelSurveyRecord(context.Context, *SurveyRecordDetailsRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelSurveyRecord not implemented")
}
func (UnimplementedSurveyServiceServer) SetValidSurveyRecord(context.Context, *SurveyRecordsRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetValidSurveyRecord not implemented")
}
func (UnimplementedSurveyServiceServer) SetInvalidSurveyRecord(context.Context, *SetValidSurveyRecordRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetInvalidSurveyRecord not implemented")
}
func (UnimplementedSurveyServiceServer) InValidSurveyRecordList(context.Context, *SurveyRecordListRequest) (*SurveyRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InValidSurveyRecordList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyPreview(context.Context, *SurveyPreviewReq) (*Survey, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyPreview not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyRecordDetail(context.Context, *SurveyRecordConfDetailsReq) (*SurveyRecordConfDetailsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyRecordDetail not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyList(context.Context, *SurveyListRequest) (*SurveyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyList not implemented")
}
func (UnimplementedSurveyServiceServer) CreateSurvey(context.Context, *CreateSurveyRequest) (*SurveyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyStatistics(context.Context, *SurveyStatisticsRequest) (*SurveyStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyStatistics not implemented")
}
func (UnimplementedSurveyServiceServer) GetSurveyStatisticsList(context.Context, *SurveyDetailRequest) (*SurveyDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSurveyStatisticsList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyShow(context.Context, *ShowSurveyRequest) (*Survey, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyShow not implemented")
}
func (UnimplementedSurveyServiceServer) DeleteSurvey(context.Context, *SurveyDelRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) CopySurvey(context.Context, *SurveyRequest) (*SurveyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopySurvey not implemented")
}
func (UnimplementedSurveyServiceServer) SyncSurvey(context.Context, *SyncSurveyRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) ImpSurvey(context.Context, *ImpSurveyRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImpSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) SetStatusSurvey(context.Context, *SurveySetStatusRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStatusSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyRecycleList(context.Context, *SurveyListRequest) (*SurveyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyRecycleList not implemented")
}
func (UnimplementedSurveyServiceServer) DeleteSurveyRecycle(context.Context, *SurveyDelRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSurveyRecycle not implemented")
}
func (UnimplementedSurveyServiceServer) RecoverSurveyRecycle(context.Context, *RecoverSurveyRecycleReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverSurveyRecycle not implemented")
}
func (UnimplementedSurveyServiceServer) ClearAllSurveyRecycle(context.Context, *ClearAllSurveyRecycleRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearAllSurveyRecycle not implemented")
}
func (UnimplementedSurveyServiceServer) RecoverAllSurveyRecycle(context.Context, *Survey) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverAllSurveyRecycle not implemented")
}
func (UnimplementedSurveyServiceServer) GetDeliverList(context.Context, *GetDeliverListRequest) (*GetDeliverListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeliverList not implemented")
}
func (UnimplementedSurveyServiceServer) GetZoneList(context.Context, *GetZoneListRequest) (*GetZoneListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetZoneList not implemented")
}
func (UnimplementedSurveyServiceServer) UpdateSurvey(context.Context, *UpdateSurveyRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) PublishSurvey(context.Context, *PublishSurveyRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishSurvey not implemented")
}
func (UnimplementedSurveyServiceServer) GetInputMethodList(context.Context, *SurveyInputMethodListRequest) (*xtype.RawMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInputMethodList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyStatisticsDetail(context.Context, *SurveyRequest) (*xtype.RawMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyStatisticsDetail not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyStatisticsDetailOld(context.Context, *SurveyRequest) (*xtype.RawMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyStatisticsDetailOld not implemented")
}
func (UnimplementedSurveyServiceServer) GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedSurveyServiceServer) UserCheck(context.Context, *UserCheckRequest) (*UserCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCheck not implemented")
}
func (UnimplementedSurveyServiceServer) StatisticsUpdate(context.Context, *StatisticsUpdateRequest) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StatisticsUpdate not implemented")
}
func (UnimplementedSurveyServiceServer) GetLatestSurveyBySurveyId(context.Context, *GetLatestSurveyBySurveyIdRequest) (*GetLatestSurveyBySurveyIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestSurveyBySurveyId not implemented")
}
func (UnimplementedSurveyServiceServer) GetValidRedeemConfigList(context.Context, *ValidRedeemConfigRequest) (*ValidRedeemConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetValidRedeemConfigList not implemented")
}
func (UnimplementedSurveyServiceServer) GetPreAwardTemplateList(context.Context, *PreAwardTemplateRequest) (*PreAwardTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreAwardTemplateList not implemented")
}
func (UnimplementedSurveyServiceServer) Upload(context.Context, *UploadRequest) (*UploadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Upload not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyViewCreate(context.Context, *SurveyViewCreateReq) (*SurveyViewCreateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyViewCreate not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyViewList(context.Context, *SurveyViewListReq) (*SurveyViewListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyViewList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyViewUpdate(context.Context, *SurveyView) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyViewUpdate not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyViewDelete(context.Context, *SurveyViewDeleteReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyViewDelete not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupCreate(context.Context, *SurveyGroupCreateReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupCreate not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupDetail(context.Context, *SurveyGroupDetailReq) (*CmsSurveyGroupInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupDetail not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupUpdate(context.Context, *SurveyGroupUpdateReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupUpdate not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupList(context.Context, *SurveyGroupListReq) (*SurveyGroupListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupList not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupSubUpdate(context.Context, *SurveyGroupSubUpdateReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupSubUpdate not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyOverwriteSend(context.Context, *SurveyOverwriteSendReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyOverwriteSend not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyOverwriteSync(context.Context, *SurveyOverwriteSyncReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyOverwriteSync not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupOverwriteSend(context.Context, *SurveyGroupOverwriteSendReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupOverwriteSend not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyGroupOverwriteSync(context.Context, *SurveyGroupOverwriteSyncReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGroupOverwriteSync not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyExportUserClusterSubmit(context.Context, *SurveyExportUserClusterSubmitReq) (*xtype.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyExportUserClusterSubmit not implemented")
}
func (UnimplementedSurveyServiceServer) SurveyExportHeaders(context.Context, *SurveyExportHeadersReq) (*SurveyExportHeadersRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyExportHeaders not implemented")
}
func (UnimplementedSurveyServiceServer) mustEmbedUnimplementedSurveyServiceServer() {}

// UnsafeSurveyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurveyServiceServer will
// result in compilation errors.
type UnsafeSurveyServiceServer interface {
	mustEmbedUnimplementedSurveyServiceServer()
}

func RegisterSurveyServiceServer(s grpc.ServiceRegistrar, srv SurveyServiceServer) {
	s.RegisterService(&SurveyService_ServiceDesc, srv)
}

func _SurveyService_Health_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Health(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Health_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Health(ctx, req.(*HealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyExportTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyExportTaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyExportTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyExportTaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyExportTaskList(ctx, req.(*SurveyExportTaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_CreateSurveyExportTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyExportTask)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).CreateSurveyExportTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_CreateSurveyExportTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).CreateSurveyExportTask(ctx, req.(*SurveyExportTask))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_DelSurveyExportTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSurveyExportTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).DelSurveyExportTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_DelSurveyExportTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).DelSurveyExportTask(ctx, req.(*DelSurveyExportTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_ResetSurveyExportTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyExportTaskDetailsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).ResetSurveyExportTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_ResetSurveyExportTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).ResetSurveyExportTaskStatus(ctx, req.(*SurveyExportTaskDetailsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordListV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyRecordList(ctx, req.(*SurveyRecordListV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyRecordListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordListV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyRecordListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyRecordListV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyRecordListV2(ctx, req.(*SurveyRecordListV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_DelSurveyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).DelSurveyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_DelSurveyRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).DelSurveyRecord(ctx, req.(*SurveyRecordDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SetValidSurveyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SetValidSurveyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SetValidSurveyRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SetValidSurveyRecord(ctx, req.(*SurveyRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SetInvalidSurveyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetValidSurveyRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SetInvalidSurveyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SetInvalidSurveyRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SetInvalidSurveyRecord(ctx, req.(*SetValidSurveyRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_InValidSurveyRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).InValidSurveyRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_InValidSurveyRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).InValidSurveyRecordList(ctx, req.(*SurveyRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyPreview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyPreviewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyPreview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyPreview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyPreview(ctx, req.(*SurveyPreviewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRecordConfDetailsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyRecordDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyRecordDetail(ctx, req.(*SurveyRecordConfDetailsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyList(ctx, req.(*SurveyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_CreateSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).CreateSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_CreateSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).CreateSurvey(ctx, req.(*CreateSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyStatistics(ctx, req.(*SurveyStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetSurveyStatisticsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetSurveyStatisticsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetSurveyStatisticsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetSurveyStatisticsList(ctx, req.(*SurveyDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyShow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyShow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyShow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyShow(ctx, req.(*ShowSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_DeleteSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyDelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).DeleteSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_DeleteSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).DeleteSurvey(ctx, req.(*SurveyDelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_CopySurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).CopySurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_CopySurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).CopySurvey(ctx, req.(*SurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SyncSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SyncSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SyncSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SyncSurvey(ctx, req.(*SyncSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_ImpSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImpSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).ImpSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_ImpSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).ImpSurvey(ctx, req.(*ImpSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SetStatusSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveySetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SetStatusSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SetStatusSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SetStatusSurvey(ctx, req.(*SurveySetStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyRecycleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyRecycleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyRecycleList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyRecycleList(ctx, req.(*SurveyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_DeleteSurveyRecycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyDelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).DeleteSurveyRecycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_DeleteSurveyRecycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).DeleteSurveyRecycle(ctx, req.(*SurveyDelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_RecoverSurveyRecycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverSurveyRecycleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).RecoverSurveyRecycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_RecoverSurveyRecycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).RecoverSurveyRecycle(ctx, req.(*RecoverSurveyRecycleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_ClearAllSurveyRecycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearAllSurveyRecycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).ClearAllSurveyRecycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_ClearAllSurveyRecycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).ClearAllSurveyRecycle(ctx, req.(*ClearAllSurveyRecycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_RecoverAllSurveyRecycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Survey)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).RecoverAllSurveyRecycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_RecoverAllSurveyRecycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).RecoverAllSurveyRecycle(ctx, req.(*Survey))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetDeliverList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeliverListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetDeliverList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetDeliverList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetDeliverList(ctx, req.(*GetDeliverListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetZoneList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetZoneListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetZoneList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetZoneList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetZoneList(ctx, req.(*GetZoneListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_UpdateSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).UpdateSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_UpdateSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).UpdateSurvey(ctx, req.(*UpdateSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_PublishSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).PublishSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_PublishSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).PublishSurvey(ctx, req.(*PublishSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetInputMethodList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyInputMethodListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetInputMethodList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetInputMethodList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetInputMethodList(ctx, req.(*SurveyInputMethodListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyStatisticsDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyStatisticsDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyStatisticsDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyStatisticsDetail(ctx, req.(*SurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyStatisticsDetailOld_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyStatisticsDetailOld(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyStatisticsDetailOld_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyStatisticsDetailOld(ctx, req.(*SurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetUserInfo(ctx, req.(*GetUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_UserCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).UserCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_UserCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).UserCheck(ctx, req.(*UserCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_StatisticsUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatisticsUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).StatisticsUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_StatisticsUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).StatisticsUpdate(ctx, req.(*StatisticsUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetLatestSurveyBySurveyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestSurveyBySurveyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetLatestSurveyBySurveyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetLatestSurveyBySurveyId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetLatestSurveyBySurveyId(ctx, req.(*GetLatestSurveyBySurveyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetValidRedeemConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidRedeemConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetValidRedeemConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetValidRedeemConfigList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetValidRedeemConfigList(ctx, req.(*ValidRedeemConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_GetPreAwardTemplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreAwardTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).GetPreAwardTemplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_GetPreAwardTemplateList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).GetPreAwardTemplateList(ctx, req.(*PreAwardTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_Upload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).Upload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_Upload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).Upload(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyViewCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyViewCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyViewCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyViewCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyViewCreate(ctx, req.(*SurveyViewCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyViewList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyViewListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyViewList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyViewList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyViewList(ctx, req.(*SurveyViewListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyViewUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyView)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyViewUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyViewUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyViewUpdate(ctx, req.(*SurveyView))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyViewDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyViewDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyViewDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyViewDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyViewDelete(ctx, req.(*SurveyViewDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupCreate(ctx, req.(*SurveyGroupCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupDetail(ctx, req.(*SurveyGroupDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupUpdate(ctx, req.(*SurveyGroupUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupList(ctx, req.(*SurveyGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupSubUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupSubUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupSubUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupSubUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupSubUpdate(ctx, req.(*SurveyGroupSubUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyOverwriteSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyOverwriteSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyOverwriteSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyOverwriteSend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyOverwriteSend(ctx, req.(*SurveyOverwriteSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyOverwriteSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyOverwriteSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyOverwriteSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyOverwriteSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyOverwriteSync(ctx, req.(*SurveyOverwriteSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupOverwriteSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupOverwriteSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupOverwriteSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupOverwriteSend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupOverwriteSend(ctx, req.(*SurveyGroupOverwriteSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyGroupOverwriteSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGroupOverwriteSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyGroupOverwriteSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyGroupOverwriteSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyGroupOverwriteSync(ctx, req.(*SurveyGroupOverwriteSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyExportUserClusterSubmit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyExportUserClusterSubmitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyExportUserClusterSubmit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyExportUserClusterSubmit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyExportUserClusterSubmit(ctx, req.(*SurveyExportUserClusterSubmitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyService_SurveyExportHeaders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyExportHeadersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyServiceServer).SurveyExportHeaders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyService_SurveyExportHeaders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyServiceServer).SurveyExportHeaders(ctx, req.(*SurveyExportHeadersReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SurveyService_ServiceDesc is the grpc.ServiceDesc for SurveyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurveyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.survey.SurveyService",
	HandlerType: (*SurveyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Health",
			Handler:    _SurveyService_Health_Handler,
		},
		{
			MethodName: "SurveyExportTaskList",
			Handler:    _SurveyService_SurveyExportTaskList_Handler,
		},
		{
			MethodName: "CreateSurveyExportTask",
			Handler:    _SurveyService_CreateSurveyExportTask_Handler,
		},
		{
			MethodName: "DelSurveyExportTask",
			Handler:    _SurveyService_DelSurveyExportTask_Handler,
		},
		{
			MethodName: "ResetSurveyExportTaskStatus",
			Handler:    _SurveyService_ResetSurveyExportTaskStatus_Handler,
		},
		{
			MethodName: "SurveyRecordList",
			Handler:    _SurveyService_SurveyRecordList_Handler,
		},
		{
			MethodName: "SurveyRecordListV2",
			Handler:    _SurveyService_SurveyRecordListV2_Handler,
		},
		{
			MethodName: "DelSurveyRecord",
			Handler:    _SurveyService_DelSurveyRecord_Handler,
		},
		{
			MethodName: "SetValidSurveyRecord",
			Handler:    _SurveyService_SetValidSurveyRecord_Handler,
		},
		{
			MethodName: "SetInvalidSurveyRecord",
			Handler:    _SurveyService_SetInvalidSurveyRecord_Handler,
		},
		{
			MethodName: "InValidSurveyRecordList",
			Handler:    _SurveyService_InValidSurveyRecordList_Handler,
		},
		{
			MethodName: "SurveyPreview",
			Handler:    _SurveyService_SurveyPreview_Handler,
		},
		{
			MethodName: "SurveyRecordDetail",
			Handler:    _SurveyService_SurveyRecordDetail_Handler,
		},
		{
			MethodName: "SurveyList",
			Handler:    _SurveyService_SurveyList_Handler,
		},
		{
			MethodName: "CreateSurvey",
			Handler:    _SurveyService_CreateSurvey_Handler,
		},
		{
			MethodName: "SurveyStatistics",
			Handler:    _SurveyService_SurveyStatistics_Handler,
		},
		{
			MethodName: "GetSurveyStatisticsList",
			Handler:    _SurveyService_GetSurveyStatisticsList_Handler,
		},
		{
			MethodName: "SurveyShow",
			Handler:    _SurveyService_SurveyShow_Handler,
		},
		{
			MethodName: "DeleteSurvey",
			Handler:    _SurveyService_DeleteSurvey_Handler,
		},
		{
			MethodName: "CopySurvey",
			Handler:    _SurveyService_CopySurvey_Handler,
		},
		{
			MethodName: "SyncSurvey",
			Handler:    _SurveyService_SyncSurvey_Handler,
		},
		{
			MethodName: "ImpSurvey",
			Handler:    _SurveyService_ImpSurvey_Handler,
		},
		{
			MethodName: "SetStatusSurvey",
			Handler:    _SurveyService_SetStatusSurvey_Handler,
		},
		{
			MethodName: "SurveyRecycleList",
			Handler:    _SurveyService_SurveyRecycleList_Handler,
		},
		{
			MethodName: "DeleteSurveyRecycle",
			Handler:    _SurveyService_DeleteSurveyRecycle_Handler,
		},
		{
			MethodName: "RecoverSurveyRecycle",
			Handler:    _SurveyService_RecoverSurveyRecycle_Handler,
		},
		{
			MethodName: "ClearAllSurveyRecycle",
			Handler:    _SurveyService_ClearAllSurveyRecycle_Handler,
		},
		{
			MethodName: "RecoverAllSurveyRecycle",
			Handler:    _SurveyService_RecoverAllSurveyRecycle_Handler,
		},
		{
			MethodName: "GetDeliverList",
			Handler:    _SurveyService_GetDeliverList_Handler,
		},
		{
			MethodName: "GetZoneList",
			Handler:    _SurveyService_GetZoneList_Handler,
		},
		{
			MethodName: "UpdateSurvey",
			Handler:    _SurveyService_UpdateSurvey_Handler,
		},
		{
			MethodName: "PublishSurvey",
			Handler:    _SurveyService_PublishSurvey_Handler,
		},
		{
			MethodName: "GetInputMethodList",
			Handler:    _SurveyService_GetInputMethodList_Handler,
		},
		{
			MethodName: "SurveyStatisticsDetail",
			Handler:    _SurveyService_SurveyStatisticsDetail_Handler,
		},
		{
			MethodName: "SurveyStatisticsDetailOld",
			Handler:    _SurveyService_SurveyStatisticsDetailOld_Handler,
		},
		{
			MethodName: "getUserInfo",
			Handler:    _SurveyService_GetUserInfo_Handler,
		},
		{
			MethodName: "userCheck",
			Handler:    _SurveyService_UserCheck_Handler,
		},
		{
			MethodName: "StatisticsUpdate",
			Handler:    _SurveyService_StatisticsUpdate_Handler,
		},
		{
			MethodName: "GetLatestSurveyBySurveyId",
			Handler:    _SurveyService_GetLatestSurveyBySurveyId_Handler,
		},
		{
			MethodName: "GetValidRedeemConfigList",
			Handler:    _SurveyService_GetValidRedeemConfigList_Handler,
		},
		{
			MethodName: "GetPreAwardTemplateList",
			Handler:    _SurveyService_GetPreAwardTemplateList_Handler,
		},
		{
			MethodName: "Upload",
			Handler:    _SurveyService_Upload_Handler,
		},
		{
			MethodName: "SurveyViewCreate",
			Handler:    _SurveyService_SurveyViewCreate_Handler,
		},
		{
			MethodName: "SurveyViewList",
			Handler:    _SurveyService_SurveyViewList_Handler,
		},
		{
			MethodName: "SurveyViewUpdate",
			Handler:    _SurveyService_SurveyViewUpdate_Handler,
		},
		{
			MethodName: "SurveyViewDelete",
			Handler:    _SurveyService_SurveyViewDelete_Handler,
		},
		{
			MethodName: "SurveyGroupCreate",
			Handler:    _SurveyService_SurveyGroupCreate_Handler,
		},
		{
			MethodName: "SurveyGroupDetail",
			Handler:    _SurveyService_SurveyGroupDetail_Handler,
		},
		{
			MethodName: "SurveyGroupUpdate",
			Handler:    _SurveyService_SurveyGroupUpdate_Handler,
		},
		{
			MethodName: "SurveyGroupList",
			Handler:    _SurveyService_SurveyGroupList_Handler,
		},
		{
			MethodName: "SurveyGroupSubUpdate",
			Handler:    _SurveyService_SurveyGroupSubUpdate_Handler,
		},
		{
			MethodName: "SurveyOverwriteSend",
			Handler:    _SurveyService_SurveyOverwriteSend_Handler,
		},
		{
			MethodName: "SurveyOverwriteSync",
			Handler:    _SurveyService_SurveyOverwriteSync_Handler,
		},
		{
			MethodName: "SurveyGroupOverwriteSend",
			Handler:    _SurveyService_SurveyGroupOverwriteSend_Handler,
		},
		{
			MethodName: "SurveyGroupOverwriteSync",
			Handler:    _SurveyService_SurveyGroupOverwriteSync_Handler,
		},
		{
			MethodName: "SurveyExportUserClusterSubmit",
			Handler:    _SurveyService_SurveyExportUserClusterSubmit_Handler,
		},
		{
			MethodName: "SurveyExportHeaders",
			Handler:    _SurveyService_SurveyExportHeaders_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/survey.proto",
}
