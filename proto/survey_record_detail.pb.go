// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_record_detail.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// unit
type Settings_PeriodicControl_Unit int32

const (
	Settings_PeriodicControl_Unit_Unknown Settings_PeriodicControl_Unit = 0
	Settings_PeriodicControl_Unit_Minute  Settings_PeriodicControl_Unit = 1
	Settings_PeriodicControl_Unit_Hour    Settings_PeriodicControl_Unit = 2
	Settings_PeriodicControl_Unit_Day     Settings_PeriodicControl_Unit = 3
)

// Enum value maps for Settings_PeriodicControl_Unit.
var (
	Settings_PeriodicControl_Unit_name = map[int32]string{
		0: "Unit_Unknown",
		1: "Unit_Minute",
		2: "Unit_Hour",
		3: "Unit_Day",
	}
	Settings_PeriodicControl_Unit_value = map[string]int32{
		"Unit_Unknown": 0,
		"Unit_Minute":  1,
		"Unit_Hour":    2,
		"Unit_Day":     3,
	}
)

func (x Settings_PeriodicControl_Unit) Enum() *Settings_PeriodicControl_Unit {
	p := new(Settings_PeriodicControl_Unit)
	*p = x
	return p
}

func (x Settings_PeriodicControl_Unit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Settings_PeriodicControl_Unit) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_survey_record_detail_proto_enumTypes[0].Descriptor()
}

func (Settings_PeriodicControl_Unit) Type() protoreflect.EnumType {
	return &file_proto_survey_record_detail_proto_enumTypes[0]
}

func (x Settings_PeriodicControl_Unit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Settings_PeriodicControl_Unit.Descriptor instead.
func (Settings_PeriodicControl_Unit) EnumDescriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 2, 0}
}

type SurveyRecordDetailListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SurveyRecordDetail `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
}

func (x *SurveyRecordDetailListRes) Reset() {
	*x = SurveyRecordDetailListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordDetailListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordDetailListRes) ProtoMessage() {}

func (x *SurveyRecordDetailListRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordDetailListRes.ProtoReflect.Descriptor instead.
func (*SurveyRecordDetailListRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyRecordDetailListRes) GetList() []*SurveyRecordDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type SurveyRecordDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:主键ID"`
	// 用户UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid" gorm:"type:varchar(128);comment:用户UID"`
	// 用户roleid
	Roleid string `protobuf:"bytes,3,opt,name=roleid,proto3" json:"roleid" gorm:"type:varchar(128);comment:用户roleid"`
	// 问卷记录ID
	SurveyRecordId int64 `protobuf:"varint,4,opt,name=survey_record_id,json=surveyRecordId,proto3" json:"survey_record_id" gorm:"type:int(11);comment:问卷记录ID"`
	// 问卷题目ID
	Question string `protobuf:"bytes,5,opt,name=question,proto3" json:"question" gorm:"type:varchar;comment:问卷题目ID"`
	// 选择类题目用户选项
	Option string `protobuf:"bytes,6,opt,name=option,proto3" json:"device_id" gorm:"type:varchar;comment:选择类题目用户选项"`
	// 输入类题目/自定义选项类题目，用户输入内容
	Text string `protobuf:"bytes,7,opt,name=text,proto3" json:"text" gorm:"type:varchar;comment:输入类题目/自定义选项类题目，用户输入内容"`
}

func (x *SurveyRecordDetail) Reset() {
	*x = SurveyRecordDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordDetail) ProtoMessage() {}

func (x *SurveyRecordDetail) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordDetail.ProtoReflect.Descriptor instead.
func (*SurveyRecordDetail) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyRecordDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecordDetail) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SurveyRecordDetail) GetRoleid() string {
	if x != nil {
		return x.Roleid
	}
	return ""
}

func (x *SurveyRecordDetail) GetSurveyRecordId() int64 {
	if x != nil {
		return x.SurveyRecordId
	}
	return 0
}

func (x *SurveyRecordDetail) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SurveyRecordDetail) GetOption() string {
	if x != nil {
		return x.Option
	}
	return ""
}

func (x *SurveyRecordDetail) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type WebSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType       string           `protobuf:"bytes,1,opt,name=login_type,json=loginType,proto3" json:"login_type,omitempty"`
	IsEndPreview    bool             `protobuf:"varint,2,opt,name=is_end_preview,json=isEndPreview,proto3" json:"is_end_preview,omitempty"`
	IsGoOnAnswer    bool             `protobuf:"varint,3,opt,name=is_go_on_answer,json=isGoOnAnswer,proto3" json:"is_go_on_answer,omitempty"`
	LanguageList    []string         `protobuf:"bytes,4,rep,name=language_list,json=languageList,proto3" json:"language_list,omitempty"`
	MaterialsConfig *Materialsconfig `protobuf:"bytes,5,opt,name=materials_config,json=materialsConfig,proto3" json:"materials_config,omitempty"`
}

func (x *WebSettings) Reset() {
	*x = WebSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSettings) ProtoMessage() {}

func (x *WebSettings) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSettings.ProtoReflect.Descriptor instead.
func (*WebSettings) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{2}
}

func (x *WebSettings) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *WebSettings) GetIsEndPreview() bool {
	if x != nil {
		return x.IsEndPreview
	}
	return false
}

func (x *WebSettings) GetIsGoOnAnswer() bool {
	if x != nil {
		return x.IsGoOnAnswer
	}
	return false
}

func (x *WebSettings) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *WebSettings) GetMaterialsConfig() *Materialsconfig {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

type Settings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseRuleConfig  *Settings_Baseruleconfig `protobuf:"bytes,1,opt,name=baseRuleConfig,proto3" json:"baseRuleConfig,omitempty"`
	MaterialsConfig *Materialsconfig         `protobuf:"bytes,4,opt,name=materialsConfig,proto3" json:"materialsConfig,omitempty"`
}

func (x *Settings) Reset() {
	*x = Settings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings) ProtoMessage() {}

func (x *Settings) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings.ProtoReflect.Descriptor instead.
func (*Settings) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3}
}

func (x *Settings) GetBaseRuleConfig() *Settings_Baseruleconfig {
	if x != nil {
		return x.BaseRuleConfig
	}
	return nil
}

func (x *Settings) GetMaterialsConfig() *Materialsconfig {
	if x != nil {
		return x.MaterialsConfig
	}
	return nil
}

type Materialsconfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AutoLatestMaterial bool   `protobuf:"varint,1,opt,name=auto_latest_material,json=autoLatestMaterial,proto3" json:"auto_latest_material,omitempty"`
	MaterialVersion    string `protobuf:"bytes,2,opt,name=material_version,json=materialVersion,proto3" json:"material_version,omitempty"`
}

func (x *Materialsconfig) Reset() {
	*x = Materialsconfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Materialsconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Materialsconfig) ProtoMessage() {}

func (x *Materialsconfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Materialsconfig.ProtoReflect.Descriptor instead.
func (*Materialsconfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{4}
}

func (x *Materialsconfig) GetAutoLatestMaterial() bool {
	if x != nil {
		return x.AutoLatestMaterial
	}
	return false
}

func (x *Materialsconfig) GetMaterialVersion() string {
	if x != nil {
		return x.MaterialVersion
	}
	return ""
}

type Settings_Timelimitconfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsTimeLimit bool `protobuf:"varint,1,opt,name=isTimeLimit,proto3" json:"isTimeLimit,omitempty"`
}

func (x *Settings_Timelimitconfig) Reset() {
	*x = Settings_Timelimitconfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings_Timelimitconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings_Timelimitconfig) ProtoMessage() {}

func (x *Settings_Timelimitconfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings_Timelimitconfig.ProtoReflect.Descriptor instead.
func (*Settings_Timelimitconfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Settings_Timelimitconfig) GetIsTimeLimit() bool {
	if x != nil {
		return x.IsTimeLimit
	}
	return false
}

type Settings_Answertimesconfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LimitType uint32 `protobuf:"varint,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
	Times     uint32 `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
}

func (x *Settings_Answertimesconfig) Reset() {
	*x = Settings_Answertimesconfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings_Answertimesconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings_Answertimesconfig) ProtoMessage() {}

func (x *Settings_Answertimesconfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings_Answertimesconfig.ProtoReflect.Descriptor instead.
func (*Settings_Answertimesconfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Settings_Answertimesconfig) GetLimitType() uint32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *Settings_Answertimesconfig) GetTimes() uint32 {
	if x != nil {
		return x.Times
	}
	return 0
}

// 频率空配置
type Settings_PeriodicControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 开关，默认关闭
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// 循环值
	Interval int32 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// 时间单位
	Unit Settings_PeriodicControl_Unit `protobuf:"varint,3,opt,name=unit,proto3,enum=papegames.sparrow.survey.Settings_PeriodicControl_Unit" json:"unit,omitempty"`
}

func (x *Settings_PeriodicControl) Reset() {
	*x = Settings_PeriodicControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings_PeriodicControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings_PeriodicControl) ProtoMessage() {}

func (x *Settings_PeriodicControl) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings_PeriodicControl.ProtoReflect.Descriptor instead.
func (*Settings_PeriodicControl) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 2}
}

func (x *Settings_PeriodicControl) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Settings_PeriodicControl) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *Settings_PeriodicControl) GetUnit() Settings_PeriodicControl_Unit {
	if x != nil {
		return x.Unit
	}
	return Settings_PeriodicControl_Unit_Unknown
}

type Settings_Baseruleconfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType         string                      `protobuf:"bytes,1,opt,name=loginType,proto3" json:"loginType,omitempty"`
	TimeLimitConfig   *Settings_Timelimitconfig   `protobuf:"bytes,2,opt,name=timeLimitConfig,proto3" json:"timeLimitConfig,omitempty"`
	IsEndPreview      bool                        `protobuf:"varint,3,opt,name=isEndPreview,proto3" json:"isEndPreview,omitempty"`
	IsGoOnAnswer      bool                        `protobuf:"varint,4,opt,name=isGoOnAnswer,proto3" json:"isGoOnAnswer,omitempty"`
	AnswerTimesConfig *Settings_Answertimesconfig `protobuf:"bytes,5,opt,name=answerTimesConfig,proto3" json:"answerTimesConfig,omitempty"`
	LanguageList      []string                    `protobuf:"bytes,6,rep,name=languageList,proto3" json:"languageList,omitempty"`
	DeliverList       []string                    `protobuf:"bytes,7,rep,name=deliverList,proto3" json:"deliverList,omitempty"`
	PeriodicControl   *Settings_PeriodicControl   `protobuf:"bytes,8,opt,name=periodicControl,proto3" json:"periodicControl,omitempty"`
}

func (x *Settings_Baseruleconfig) Reset() {
	*x = Settings_Baseruleconfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings_Baseruleconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings_Baseruleconfig) ProtoMessage() {}

func (x *Settings_Baseruleconfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings_Baseruleconfig.ProtoReflect.Descriptor instead.
func (*Settings_Baseruleconfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 3}
}

func (x *Settings_Baseruleconfig) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *Settings_Baseruleconfig) GetTimeLimitConfig() *Settings_Timelimitconfig {
	if x != nil {
		return x.TimeLimitConfig
	}
	return nil
}

func (x *Settings_Baseruleconfig) GetIsEndPreview() bool {
	if x != nil {
		return x.IsEndPreview
	}
	return false
}

func (x *Settings_Baseruleconfig) GetIsGoOnAnswer() bool {
	if x != nil {
		return x.IsGoOnAnswer
	}
	return false
}

func (x *Settings_Baseruleconfig) GetAnswerTimesConfig() *Settings_Answertimesconfig {
	if x != nil {
		return x.AnswerTimesConfig
	}
	return nil
}

func (x *Settings_Baseruleconfig) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *Settings_Baseruleconfig) GetDeliverList() []string {
	if x != nil {
		return x.DeliverList
	}
	return nil
}

func (x *Settings_Baseruleconfig) GetPeriodicControl() *Settings_PeriodicControl {
	if x != nil {
		return x.PeriodicControl
	}
	return nil
}

type Settings_Answerlimitconfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LimitType string `protobuf:"bytes,1,opt,name=limitType,proto3" json:"limitType,omitempty"`
}

func (x *Settings_Answerlimitconfig) Reset() {
	*x = Settings_Answerlimitconfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_detail_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings_Answerlimitconfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings_Answerlimitconfig) ProtoMessage() {}

func (x *Settings_Answerlimitconfig) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_detail_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings_Answerlimitconfig.ProtoReflect.Descriptor instead.
func (*Settings_Answerlimitconfig) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_detail_proto_rawDescGZIP(), []int{3, 4}
}

func (x *Settings_Answerlimitconfig) GetLimitType() string {
	if x != nil {
		return x.LimitType
	}
	return ""
}

var File_proto_survey_record_detail_proto protoreflect.FileDescriptor

var file_proto_survey_record_detail_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1b, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65,
	0x72, 0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64,
	0x0a, 0x19, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x05, 0xc8,
	0xa7, 0x86, 0x07, 0x01, 0x22, 0xce, 0x05, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4c, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x3c, 0xd2, 0xa7, 0x86, 0x07, 0x37, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x74, 0x79,
	0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x20, 0x30, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe4, 0xb8, 0xbb,
	0xe9, 0x94, 0xae, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x40, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7,
	0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x55, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x08,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x75, 0x69, 0x64, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x58, 0x0a,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x40, 0xd2,
	0xa7, 0x86, 0x07, 0x2b, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x72, 0x6f, 0x6c, 0x65, 0x69, 0x64, 0xd2,
	0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x69, 0x64, 0x52,
	0x06, 0x72, 0x6f, 0x6c, 0x65, 0x69, 0x64, 0x12, 0x71, 0x0a, 0x10, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x47, 0xd2, 0xa7, 0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x49, 0x44,
	0xd2, 0xa7, 0x86, 0x07, 0x15, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x52, 0x0e, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x08, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3f, 0xd2, 0xa7,
	0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x97, 0xae,
	0xe5, 0x8d, 0xb7, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0d,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d, 0xd2, 0xa7, 0x86, 0x07, 0x35, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x3b,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0xb1,
	0xbb, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x80, 0x89,
	0xe9, 0xa1, 0xb9, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7e,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x6a, 0xd2, 0xa7,
	0x86, 0x07, 0x57, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xbe, 0x93,
	0xe5, 0x85, 0xa5, 0xe7, 0xb1, 0xbb, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0x2f, 0xe8, 0x87, 0xaa,
	0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0xe7, 0xb1, 0xbb, 0xe9,
	0xa2, 0x98, 0xe7, 0x9b, 0xae, 0xef, 0xbc, 0x8c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0xbe,
	0x93, 0xe5, 0x85, 0xa5, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x74, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x3a, 0x05,
	0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0xf4, 0x01, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73,
	0x5f, 0x67, 0x6f, 0x5f, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x73, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xa7, 0x08, 0x0a,
	0x08, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x59, 0x0a, 0x0e, 0x62, 0x61, 0x73,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x72, 0x75, 0x6c, 0x65, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x62, 0x61, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x53, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x33, 0x0a, 0x0f, 0x54, 0x69, 0x6d,
	0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b,
	0x69, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x1a, 0x47,
	0x0a, 0x11, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0xda, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12,
	0x4b, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e,
	0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f,
	0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x46, 0x0a, 0x04,
	0x55, 0x6e, 0x69, 0x74, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x4d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x69, 0x74, 0x5f,
	0x48, 0x6f, 0x75, 0x72, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x6e, 0x69, 0x74, 0x5f, 0x44,
	0x61, 0x79, 0x10, 0x03, 0x1a, 0xdc, 0x03, 0x0a, 0x0e, 0x42, 0x61, 0x73, 0x65, 0x72, 0x75, 0x6c,
	0x65, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x47, 0x6f, 0x4f,
	0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69,
	0x73, 0x47, 0x6f, 0x4f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x11, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5c, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69,
	0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x1a, 0x31, 0x0a, 0x11, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6e, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x73, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x4c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61,
	0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_survey_record_detail_proto_rawDescOnce sync.Once
	file_proto_survey_record_detail_proto_rawDescData = file_proto_survey_record_detail_proto_rawDesc
)

func file_proto_survey_record_detail_proto_rawDescGZIP() []byte {
	file_proto_survey_record_detail_proto_rawDescOnce.Do(func() {
		file_proto_survey_record_detail_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_record_detail_proto_rawDescData)
	})
	return file_proto_survey_record_detail_proto_rawDescData
}

var file_proto_survey_record_detail_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_survey_record_detail_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_survey_record_detail_proto_goTypes = []any{
	(Settings_PeriodicControl_Unit)(0), // 0: papegames.sparrow.survey.Settings.PeriodicControl.Unit
	(*SurveyRecordDetailListRes)(nil),  // 1: papegames.sparrow.survey.SurveyRecordDetailListRes
	(*SurveyRecordDetail)(nil),         // 2: papegames.sparrow.survey.SurveyRecordDetail
	(*WebSettings)(nil),                // 3: papegames.sparrow.survey.WebSettings
	(*Settings)(nil),                   // 4: papegames.sparrow.survey.Settings
	(*Materialsconfig)(nil),            // 5: papegames.sparrow.survey.Materialsconfig
	(*Settings_Timelimitconfig)(nil),   // 6: papegames.sparrow.survey.Settings.Timelimitconfig
	(*Settings_Answertimesconfig)(nil), // 7: papegames.sparrow.survey.Settings.Answertimesconfig
	(*Settings_PeriodicControl)(nil),   // 8: papegames.sparrow.survey.Settings.PeriodicControl
	(*Settings_Baseruleconfig)(nil),    // 9: papegames.sparrow.survey.Settings.Baseruleconfig
	(*Settings_Answerlimitconfig)(nil), // 10: papegames.sparrow.survey.Settings.Answerlimitconfig
}
var file_proto_survey_record_detail_proto_depIdxs = []int32{
	2, // 0: papegames.sparrow.survey.SurveyRecordDetailListRes.list:type_name -> papegames.sparrow.survey.SurveyRecordDetail
	5, // 1: papegames.sparrow.survey.WebSettings.materials_config:type_name -> papegames.sparrow.survey.Materialsconfig
	9, // 2: papegames.sparrow.survey.Settings.baseRuleConfig:type_name -> papegames.sparrow.survey.Settings.Baseruleconfig
	5, // 3: papegames.sparrow.survey.Settings.materialsConfig:type_name -> papegames.sparrow.survey.Materialsconfig
	0, // 4: papegames.sparrow.survey.Settings.PeriodicControl.unit:type_name -> papegames.sparrow.survey.Settings.PeriodicControl.Unit
	6, // 5: papegames.sparrow.survey.Settings.Baseruleconfig.timeLimitConfig:type_name -> papegames.sparrow.survey.Settings.Timelimitconfig
	7, // 6: papegames.sparrow.survey.Settings.Baseruleconfig.answerTimesConfig:type_name -> papegames.sparrow.survey.Settings.Answertimesconfig
	8, // 7: papegames.sparrow.survey.Settings.Baseruleconfig.periodicControl:type_name -> papegames.sparrow.survey.Settings.PeriodicControl
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_proto_survey_record_detail_proto_init() }
func file_proto_survey_record_detail_proto_init() {
	if File_proto_survey_record_detail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_record_detail_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordDetailListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*WebSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Settings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Materialsconfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Settings_Timelimitconfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*Settings_Answertimesconfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Settings_PeriodicControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Settings_Baseruleconfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_detail_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Settings_Answerlimitconfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_record_detail_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_record_detail_proto_goTypes,
		DependencyIndexes: file_proto_survey_record_detail_proto_depIdxs,
		EnumInfos:         file_proto_survey_record_detail_proto_enumTypes,
		MessageInfos:      file_proto_survey_record_detail_proto_msgTypes,
	}.Build()
	File_proto_survey_record_detail_proto = out.File
	file_proto_survey_record_detail_proto_rawDesc = nil
	file_proto_survey_record_detail_proto_goTypes = nil
	file_proto_survey_record_detail_proto_depIdxs = nil
}
