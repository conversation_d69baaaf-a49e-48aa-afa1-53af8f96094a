// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_group.proto

package proto

func (x *CmsSurveyGroupInfo) Validate() error {
	return nil
}

func (x *SurveyGroupCreateReq) Validate() error {
	if len(x.GetName()) == 0 {
		return SurveyGroupCreateReqValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSettings()) == 0 {
		return SurveyGroupCreateReqValidationError{
			field:   "Settings",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyGroupDetailReq) Validate() error {
	return nil
}

func (x *SurveyGroupUpdateReq) Validate() error {
	if len(x.GetName()) == 0 {
		return SurveyGroupUpdateReqValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSettings()) == 0 {
		return SurveyGroupUpdateReqValidationError{
			field:   "Settings",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyGroupListReq) Validate() error {
	return nil
}

func (x *SurveyGroupListRes) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyGroupListResValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyGroupSubUpdateReq) Validate() error {
	return nil
}

func (x *SurveyGroupOverwriteSendReq) Validate() error {
	return nil
}

func (x *SurveyGroupOverwriteSyncReq) Validate() error {
	if len(x.GetSurveyGroup()) == 0 {
		return SurveyGroupOverwriteSyncReqValidationError{
			field:   "SurveyGroup",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

type CmsSurveyGroupInfoValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CmsSurveyGroupInfoValidationError) Field() string { return e.field }

func (e CmsSurveyGroupInfoValidationError) Reason() string { return e.reason }

func (e CmsSurveyGroupInfoValidationError) Message() string { return e.message }

func (e CmsSurveyGroupInfoValidationError) Cause() error { return e.cause }

func (e CmsSurveyGroupInfoValidationError) ErrorName() string {
	return "CmsSurveyGroupInfoValidationError"
}

func (e CmsSurveyGroupInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CmsSurveyGroupInfo." + e.field + ": " + e.message + cause
}

type SurveyGroupCreateReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupCreateReqValidationError) Field() string { return e.field }

func (e SurveyGroupCreateReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupCreateReqValidationError) Message() string { return e.message }

func (e SurveyGroupCreateReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupCreateReqValidationError) ErrorName() string {
	return "SurveyGroupCreateReqValidationError"
}

func (e SurveyGroupCreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupCreateReq." + e.field + ": " + e.message + cause
}

type SurveyGroupDetailReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupDetailReqValidationError) Field() string { return e.field }

func (e SurveyGroupDetailReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupDetailReqValidationError) Message() string { return e.message }

func (e SurveyGroupDetailReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupDetailReqValidationError) ErrorName() string {
	return "SurveyGroupDetailReqValidationError"
}

func (e SurveyGroupDetailReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupDetailReq." + e.field + ": " + e.message + cause
}

type SurveyGroupUpdateReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupUpdateReqValidationError) Field() string { return e.field }

func (e SurveyGroupUpdateReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupUpdateReqValidationError) Message() string { return e.message }

func (e SurveyGroupUpdateReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupUpdateReqValidationError) ErrorName() string {
	return "SurveyGroupUpdateReqValidationError"
}

func (e SurveyGroupUpdateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupUpdateReq." + e.field + ": " + e.message + cause
}

type SurveyGroupListReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupListReqValidationError) Field() string { return e.field }

func (e SurveyGroupListReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupListReqValidationError) Message() string { return e.message }

func (e SurveyGroupListReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupListReqValidationError) ErrorName() string {
	return "SurveyGroupListReqValidationError"
}

func (e SurveyGroupListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupListReq." + e.field + ": " + e.message + cause
}

type SurveyGroupListResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupListResValidationError) Field() string { return e.field }

func (e SurveyGroupListResValidationError) Reason() string { return e.reason }

func (e SurveyGroupListResValidationError) Message() string { return e.message }

func (e SurveyGroupListResValidationError) Cause() error { return e.cause }

func (e SurveyGroupListResValidationError) ErrorName() string {
	return "SurveyGroupListResValidationError"
}

func (e SurveyGroupListResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupListRes." + e.field + ": " + e.message + cause
}

type SurveyGroupSubUpdateReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupSubUpdateReqValidationError) Field() string { return e.field }

func (e SurveyGroupSubUpdateReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupSubUpdateReqValidationError) Message() string { return e.message }

func (e SurveyGroupSubUpdateReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupSubUpdateReqValidationError) ErrorName() string {
	return "SurveyGroupSubUpdateReqValidationError"
}

func (e SurveyGroupSubUpdateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupSubUpdateReq." + e.field + ": " + e.message + cause
}

type SurveyGroupOverwriteSendReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupOverwriteSendReqValidationError) Field() string { return e.field }

func (e SurveyGroupOverwriteSendReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupOverwriteSendReqValidationError) Message() string { return e.message }

func (e SurveyGroupOverwriteSendReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupOverwriteSendReqValidationError) ErrorName() string {
	return "SurveyGroupOverwriteSendReqValidationError"
}

func (e SurveyGroupOverwriteSendReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupOverwriteSendReq." + e.field + ": " + e.message + cause
}

type SurveyGroupOverwriteSyncReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyGroupOverwriteSyncReqValidationError) Field() string { return e.field }

func (e SurveyGroupOverwriteSyncReqValidationError) Reason() string { return e.reason }

func (e SurveyGroupOverwriteSyncReqValidationError) Message() string { return e.message }

func (e SurveyGroupOverwriteSyncReqValidationError) Cause() error { return e.cause }

func (e SurveyGroupOverwriteSyncReqValidationError) ErrorName() string {
	return "SurveyGroupOverwriteSyncReqValidationError"
}

func (e SurveyGroupOverwriteSyncReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyGroupOverwriteSyncReq." + e.field + ": " + e.message + cause
}
