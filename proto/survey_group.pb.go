// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_group.proto

package proto

import (
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurveyGroupSubUpdateType int32

const (
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Unknown SurveyGroupSubUpdateType = 0
	// 发布
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Publish SurveyGroupSubUpdateType = 1
	// 暂停
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Pause SurveyGroupSubUpdateType = 2
	// 删除
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Delete SurveyGroupSubUpdateType = 3
	// 关闭
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Closed SurveyGroupSubUpdateType = 4
	// 更新setting
	SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Settings SurveyGroupSubUpdateType = 5
)

// Enum value maps for SurveyGroupSubUpdateType.
var (
	SurveyGroupSubUpdateType_name = map[int32]string{
		0: "SurveyGroupSubUpdateType_Unknown",
		1: "SurveyGroupSubUpdateType_Publish",
		2: "SurveyGroupSubUpdateType_Pause",
		3: "SurveyGroupSubUpdateType_Delete",
		4: "SurveyGroupSubUpdateType_Closed",
		5: "SurveyGroupSubUpdateType_Settings",
	}
	SurveyGroupSubUpdateType_value = map[string]int32{
		"SurveyGroupSubUpdateType_Unknown":  0,
		"SurveyGroupSubUpdateType_Publish":  1,
		"SurveyGroupSubUpdateType_Pause":    2,
		"SurveyGroupSubUpdateType_Delete":   3,
		"SurveyGroupSubUpdateType_Closed":   4,
		"SurveyGroupSubUpdateType_Settings": 5,
	}
)

func (x SurveyGroupSubUpdateType) Enum() *SurveyGroupSubUpdateType {
	p := new(SurveyGroupSubUpdateType)
	*p = x
	return p
}

func (x SurveyGroupSubUpdateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SurveyGroupSubUpdateType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_survey_group_proto_enumTypes[0].Descriptor()
}

func (SurveyGroupSubUpdateType) Type() protoreflect.EnumType {
	return &file_proto_survey_group_proto_enumTypes[0]
}

func (x SurveyGroupSubUpdateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SurveyGroupSubUpdateType.Descriptor instead.
func (SurveyGroupSubUpdateType) EnumDescriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{0}
}

// CmsSurveyGroupInfo
type CmsSurveyGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Clientid  string `protobuf:"bytes,2,opt,name=clientid,proto3" json:"clientid"`                     // clientid
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`                             // 问卷组名称
	IsPublish int32  `protobuf:"varint,4,opt,name=is_publish,json=isPublish,proto3" json:"is_publish"` // 是否发布（0: 未发布，1: 已发布）
	LimitType int32  `protobuf:"varint,5,opt,name=limit_type,json=limitType,proto3" json:"limit_type"` // 是否有修改未发布（0: 无，1: 有）
	Type      int32  `protobuf:"varint,6,opt,name=type,proto3" json:"type"`                            // 问卷组类型 1:ip 2:语言
	Settings  string `protobuf:"bytes,7,opt,name=settings,proto3" json:"settings"`                     // 问卷组设置
	HashCode  string `protobuf:"bytes,8,opt,name=hash_code,json=hashCode,proto3" json:"hash_code"`     // 问卷组id-hash
	IsDelete  int32  `protobuf:"varint,9,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`    // 是否删除（0: 未删除，1: 已删除）
	Ctime     string `protobuf:"bytes,10,opt,name=ctime,proto3" json:"ctime"`                          // 创建时间
	Mtime     string `protobuf:"bytes,11,opt,name=mtime,proto3" json:"mtime"`                          // 最近修改时间
	Creator   string `protobuf:"bytes,12,opt,name=creator,proto3" json:"creator"`                      // 创建人
	Editor    string `protobuf:"bytes,13,opt,name=editor,proto3" json:"editor"`                        // 最近修改人
}

func (x *CmsSurveyGroupInfo) Reset() {
	*x = CmsSurveyGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmsSurveyGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmsSurveyGroupInfo) ProtoMessage() {}

func (x *CmsSurveyGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmsSurveyGroupInfo.ProtoReflect.Descriptor instead.
func (*CmsSurveyGroupInfo) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{0}
}

func (x *CmsSurveyGroupInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CmsSurveyGroupInfo) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *CmsSurveyGroupInfo) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *CmsSurveyGroupInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CmsSurveyGroupInfo) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetHashCode() string {
	if x != nil {
		return x.HashCode
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *CmsSurveyGroupInfo) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CmsSurveyGroupInfo) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

type SurveyGroupCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 答题次数限制
	LimitType int32 `protobuf:"varint,3,opt,name=limit_type,json=limitType,proto3" json:"limit_type,omitempty"`
	// 问卷组类型 1:ip 2:语言
	Type int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	// 问卷组设置
	Settings string `protobuf:"bytes,5,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *SurveyGroupCreateReq) Reset() {
	*x = SurveyGroupCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupCreateReq) ProtoMessage() {}

func (x *SurveyGroupCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupCreateReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupCreateReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyGroupCreateReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyGroupCreateReq) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *SurveyGroupCreateReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SurveyGroupCreateReq) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

type SurveyGroupDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 组ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SurveyGroupDetailReq) Reset() {
	*x = SurveyGroupDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupDetailReq) ProtoMessage() {}

func (x *SurveyGroupDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupDetailReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupDetailReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{2}
}

func (x *SurveyGroupDetailReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupDetailReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SurveyGroupUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷组ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 答题次数限制
	LimitType int32 `protobuf:"varint,4,opt,name=limit_type,json=limitType,proto3" json:"limit_type,omitempty"`
	// 问卷组类型 1:ip 2:语言
	Type int32 `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	// 问卷组设置
	Settings string `protobuf:"bytes,6,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *SurveyGroupUpdateReq) Reset() {
	*x = SurveyGroupUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupUpdateReq) ProtoMessage() {}

func (x *SurveyGroupUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupUpdateReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupUpdateReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{3}
}

func (x *SurveyGroupUpdateReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyGroupUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyGroupUpdateReq) GetLimitType() int32 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *SurveyGroupUpdateReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SurveyGroupUpdateReq) GetSettings() string {
	if x != nil {
		return x.Settings
	}
	return ""
}

type SurveyGroupListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷组ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SurveyGroupListReq) Reset() {
	*x = SurveyGroupListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupListReq) ProtoMessage() {}

func (x *SurveyGroupListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupListReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupListReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{4}
}

func (x *SurveyGroupListReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SurveyGroupListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷组列表
	List []*CmsSurveyGroupInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总数
	Total int64 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *SurveyGroupListRes) Reset() {
	*x = SurveyGroupListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupListRes) ProtoMessage() {}

func (x *SurveyGroupListRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupListRes.ProtoReflect.Descriptor instead.
func (*SurveyGroupListRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{5}
}

func (x *SurveyGroupListRes) GetList() []*CmsSurveyGroupInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyGroupListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SurveyGroupSubUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷组ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 操作类型
	Type SurveyGroupSubUpdateType `protobuf:"varint,3,opt,name=type,proto3,enum=papegames.sparrow.survey.SurveyGroupSubUpdateType" json:"type,omitempty"`
	// 要更新的值
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *SurveyGroupSubUpdateReq) Reset() {
	*x = SurveyGroupSubUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupSubUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupSubUpdateReq) ProtoMessage() {}

func (x *SurveyGroupSubUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupSubUpdateReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupSubUpdateReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{6}
}

func (x *SurveyGroupSubUpdateReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupSubUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyGroupSubUpdateReq) GetType() SurveyGroupSubUpdateType {
	if x != nil {
		return x.Type
	}
	return SurveyGroupSubUpdateType_SurveyGroupSubUpdateType_Unknown
}

func (x *SurveyGroupSubUpdateReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type SurveyGroupOverwriteSendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷组ID
	SurveyGroupId int64 `protobuf:"varint,2,opt,name=survey_group_id,json=surveyGroupId,proto3" json:"survey_group_id,omitempty"`
}

func (x *SurveyGroupOverwriteSendReq) Reset() {
	*x = SurveyGroupOverwriteSendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupOverwriteSendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupOverwriteSendReq) ProtoMessage() {}

func (x *SurveyGroupOverwriteSendReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupOverwriteSendReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupOverwriteSendReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{7}
}

func (x *SurveyGroupOverwriteSendReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupOverwriteSendReq) GetSurveyGroupId() int64 {
	if x != nil {
		return x.SurveyGroupId
	}
	return 0
}

type SurveyGroupOverwriteSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 租户ID
	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 问卷组
	SurveyGroup string `protobuf:"bytes,2,opt,name=survey_group,json=surveyGroup,proto3" json:"survey_group,omitempty"`
}

func (x *SurveyGroupOverwriteSyncReq) Reset() {
	*x = SurveyGroupOverwriteSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_group_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyGroupOverwriteSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyGroupOverwriteSyncReq) ProtoMessage() {}

func (x *SurveyGroupOverwriteSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_group_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyGroupOverwriteSyncReq.ProtoReflect.Descriptor instead.
func (*SurveyGroupOverwriteSyncReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_group_proto_rawDescGZIP(), []int{8}
}

func (x *SurveyGroupOverwriteSyncReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyGroupOverwriteSyncReq) GetSurveyGroup() string {
	if x != nil {
		return x.SurveyGroup
	}
	return ""
}

var File_proto_survey_group_proto protoreflect.FileDescriptor

var file_proto_survey_group_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70, 0x65,
	0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61,
	0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe1, 0x02, 0x0a, 0x12, 0x43,
	0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0xb4,
	0x01, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x09,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x4f, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41,
	0x01, 0x02, 0x52, 0x02, 0x69, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x14, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x09, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0x4d, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41,
	0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x6c, 0x0a, 0x12, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x43, 0x6d, 0x73, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xb0, 0x01, 0x0a, 0x17, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x53, 0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x02, 0x69, 0x64, 0x12, 0x46, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e,
	0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x6e, 0x0a, 0x1b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x0d, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x1b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01,
	0x02, 0x52, 0x0b, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2a, 0xfb,
	0x01, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75,
	0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x53, 0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x50, 0x61, 0x75, 0x73, 0x65, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x10, 0x03,
	0x12, 0x23, 0x0a, 0x1f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x10, 0x05, 0x42, 0x41, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70,
	0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_group_proto_rawDescOnce sync.Once
	file_proto_survey_group_proto_rawDescData = file_proto_survey_group_proto_rawDesc
)

func file_proto_survey_group_proto_rawDescGZIP() []byte {
	file_proto_survey_group_proto_rawDescOnce.Do(func() {
		file_proto_survey_group_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_group_proto_rawDescData)
	})
	return file_proto_survey_group_proto_rawDescData
}

var file_proto_survey_group_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_survey_group_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proto_survey_group_proto_goTypes = []any{
	(SurveyGroupSubUpdateType)(0),       // 0: papegames.sparrow.survey.SurveyGroupSubUpdateType
	(*CmsSurveyGroupInfo)(nil),          // 1: papegames.sparrow.survey.CmsSurveyGroupInfo
	(*SurveyGroupCreateReq)(nil),        // 2: papegames.sparrow.survey.SurveyGroupCreateReq
	(*SurveyGroupDetailReq)(nil),        // 3: papegames.sparrow.survey.SurveyGroupDetailReq
	(*SurveyGroupUpdateReq)(nil),        // 4: papegames.sparrow.survey.SurveyGroupUpdateReq
	(*SurveyGroupListReq)(nil),          // 5: papegames.sparrow.survey.SurveyGroupListReq
	(*SurveyGroupListRes)(nil),          // 6: papegames.sparrow.survey.SurveyGroupListRes
	(*SurveyGroupSubUpdateReq)(nil),     // 7: papegames.sparrow.survey.SurveyGroupSubUpdateReq
	(*SurveyGroupOverwriteSendReq)(nil), // 8: papegames.sparrow.survey.SurveyGroupOverwriteSendReq
	(*SurveyGroupOverwriteSyncReq)(nil), // 9: papegames.sparrow.survey.SurveyGroupOverwriteSyncReq
}
var file_proto_survey_group_proto_depIdxs = []int32{
	1, // 0: papegames.sparrow.survey.SurveyGroupListRes.list:type_name -> papegames.sparrow.survey.CmsSurveyGroupInfo
	0, // 1: papegames.sparrow.survey.SurveyGroupSubUpdateReq.type:type_name -> papegames.sparrow.survey.SurveyGroupSubUpdateType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_survey_group_proto_init() }
func file_proto_survey_group_proto_init() {
	if File_proto_survey_group_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_group_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CmsSurveyGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupSubUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupOverwriteSendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_group_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyGroupOverwriteSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_group_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_group_proto_goTypes,
		DependencyIndexes: file_proto_survey_group_proto_depIdxs,
		EnumInfos:         file_proto_survey_group_proto_enumTypes,
		MessageInfos:      file_proto_survey_group_proto_msgTypes,
	}.Build()
	File_proto_survey_group_proto = out.File
	file_proto_survey_group_proto_rawDesc = nil
	file_proto_survey_group_proto_goTypes = nil
	file_proto_survey_group_proto_depIdxs = nil
}
