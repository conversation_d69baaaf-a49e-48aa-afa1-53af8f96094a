syntax = "proto3";

package papegames.sparrow.survey;

import "google/api/field_behavior.proto";
//import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message SurveyExportTaskListRequest {
  // 当前页码，默认1
  int32 page = 1;
  // 每页条数，默认10
  int32 page_size = 2;
  int64 client_id = 3;
  int64 survey_id = 4;
}

message SurveyExportTaskListResponse {
  option (tagger.disable_omitempty) = true;
  repeated SurveyExportTask list = 1;
  int64 total = 2;
}

// 详情
message SurveyExportTaskDetailsReq {
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
  int64 survey_id = 2 [(google.api.field_behavior) = REQUIRED];
  int64 client_id = 3;
}

message DelSurveyExportTaskReq {
  repeated int64 ids = 1 [
    (google.api.field_behavior) = REQUIRED];
  int64 survey_id = 2 [
    (google.api.field_behavior) = REQUIRED];
  int64 client_id = 3 [
    (google.api.field_behavior) = REQUIRED];
}

message SurveyExportTask {
  option (tagger.disable_omitempty) = true;

  // ID，自增主键，创建时此参数不需要传
  int64 id = 1 [
    (tagger.tags) = "gorm:primaryKey;type:int(11) default 0;comment:主键ID"
  ];

  // 租户id
  int64 client_id = 2 [
    (tagger.tags) = "gorm:column:clientid;type:varchar(128);comment:租户id",
    (openapi.v3.property) = {
      max_length: 128,
    },
    (tagger.tags) = "json:client_id"
  ];

  // 问卷id
  int64 survey_id = 3 [
    (tagger.tags) = "gorm:type:int(11);comment:问卷id",
    (google.api.field_behavior) = REQUIRED
  ];

  // 文件名称
  string name = 4 [
    (tagger.tags) = "gorm:type:varchar(125);comment:文件名称",
    (openapi.v3.property) = {
      max_length: 125,
    }
  ];

  // 文件类型
  int32 file_type = 5 [
    (tagger.tags) = "gorm:type:tinyint(64);comment:文件类型 0: CSV 1: EXCEL",
    (tagger.tags) = "json:file_type"
  ];

  // 数据类型
  int32 data_type = 6 [
    (tagger.tags) = "gorm:type:tinyint(4);comment:数据类型 0: 答案编码 1: 答案文本",
    (tagger.tags) = "json:data_type"
  ];

  // 数据有效性
  int32 is_valid = 7 [
    (tagger.tags) = "gorm:type:tinyint(4);comment:数据有效性（0: 有效 1: 无效 2:全部）",
    (tagger.tags) = "json:is_valid"
  ];

  // url
  string url = 8 [
    (tagger.tags) = "gorm:type:tinyint(4);comment:url",
    (tagger.tags) = "json:url"
  ];

  // 状态
  int32 status = 9 [
    (tagger.tags) = "gorm:type:tinyint(4);comment:0: 等待，未处理 1: 处理中 2: 完成 3: 失败",
    (tagger.tags) = "json:status"
  ];

  // 完成时间范围 起始时间
  string start_time = 10 [
    (tagger.tags) = "json:start_time"
  ];

  // 完成时间范围 结束时间
  string end_time = 11 [
    (tagger.tags) = "json:end_time"
  ];

  // 筛选内容
  string view_content = 12 [
    (tagger.tags) = "json:view_content,omitempty",
    (tagger.tags) = "gorm:view_content"
  ];

  // 数据源，0：全部，1：随机
  int32 data_source = 13 [
    (tagger.tags) = "json:data_source,omitempty",
    (tagger.tags) = "gorm:data_source"
  ];

  // 随机数量
  int32 data_num = 14 [
    (tagger.tags) = "json:data_num,omitempty",
    (tagger.tags) = "gorm:data_num"
  ];

  // 拓展字段
  string extra = 15 [
    (tagger.tags) = "json:extra,omitempty",
    (tagger.tags) = "gorm:extra"
  ];

  // 创建时间
  string  create_time = 16 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (tagger.tags) = "json:create_time,omitempty",
    (tagger.tags) = "gorm:autoCreateTime"
  ];

  // 更新时间
  string  update_time = 17 [
    (tagger.tags) = "json:update_time,omitempty",
    (tagger.tags) = "gorm:autoUpdateTime"
  ];

  // 完成时间
  string  complete_time = 18 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (tagger.tags) = "json:complete_time"
  ];
}

message UserStrategy {
  // 用户分群名称
  string cluster_name = 1 [(google.api.field_behavior) = REQUIRED];
  // 用户分群基于的用户维度, 如vroleid, vopenid
  string entity_name = 2 [(google.api.field_behavior) = REQUIRED];
  // 版本
  string version = 3;
}

message SurveyExportUserClusterSubmitReq {
  int64  client_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  // 用户分群信息
  UserStrategy user_strategy = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SurveyExportHeadersReq {
  int64  client_id = 1 [
    (google.api.field_behavior) = REQUIRED
  ];
  int64 survey_id = 2 [
    (google.api.field_behavior) = REQUIRED
  ];
}

message SurveyExportHeadersRes {
  message QuestionRowTitle {
    string row_unique_key = 1;
    string role_title = 2;
  }
  message SelectOptions {
    string value = 1; // 选项 option
    string label = 2; // 选项 title
    bool hasBlank = 3;// 是否有填空
  }
  message Question {
    string question_unique_key = 1;
    string question_title = 2;
    string statistics_method = 3;
    string question_type = 4;
    string select_mode = 5;
    int32  question_id = 6;
    repeated QuestionRowTitle question_row_titles = 7;
    repeated SelectOptions select_options = 8;
  }
  repeated Question list = 1;
  string version = 2;
}

