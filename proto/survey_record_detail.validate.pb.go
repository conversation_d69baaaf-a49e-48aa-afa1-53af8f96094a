// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_record_detail.proto

package proto

func (x *SurveyRecordDetailListRes) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyRecordDetailListResValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyRecordDetail) Validate() error {
	if len(x.GetUid()) > 128 {
		return SurveyRecordDetailValidationError{
			field:   "Uid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	return nil
}

func (x *WebSettings) Validate() error {
	if v, ok := interface{}(x.GetMaterialsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebSettingsValidationError{
				field:   "MaterialsConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Settings) Validate() error {
	if v, ok := interface{}(x.GetBaseRuleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsValidationError{
				field:   "BaseRuleConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMaterialsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsValidationError{
				field:   "MaterialsConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Settings_Timelimitconfig) Validate() error {
	return nil
}

func (x *Settings_Answertimesconfig) Validate() error {
	return nil
}

func (x *Settings_PeriodicControl) Validate() error {
	return nil
}

func (x *Settings_Baseruleconfig) Validate() error {
	if v, ok := interface{}(x.GetTimeLimitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Settings_BaseruleconfigValidationError{
				field:   "TimeLimitConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAnswerTimesConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Settings_BaseruleconfigValidationError{
				field:   "AnswerTimesConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetPeriodicControl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Settings_BaseruleconfigValidationError{
				field:   "PeriodicControl",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Settings_Answerlimitconfig) Validate() error {
	return nil
}

func (x *Materialsconfig) Validate() error {
	return nil
}

type SurveyRecordDetailListResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordDetailListResValidationError) Field() string { return e.field }

func (e SurveyRecordDetailListResValidationError) Reason() string { return e.reason }

func (e SurveyRecordDetailListResValidationError) Message() string { return e.message }

func (e SurveyRecordDetailListResValidationError) Cause() error { return e.cause }

func (e SurveyRecordDetailListResValidationError) ErrorName() string {
	return "SurveyRecordDetailListResValidationError"
}

func (e SurveyRecordDetailListResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordDetailListRes." + e.field + ": " + e.message + cause
}

type SurveyRecordDetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordDetailValidationError) Field() string { return e.field }

func (e SurveyRecordDetailValidationError) Reason() string { return e.reason }

func (e SurveyRecordDetailValidationError) Message() string { return e.message }

func (e SurveyRecordDetailValidationError) Cause() error { return e.cause }

func (e SurveyRecordDetailValidationError) ErrorName() string {
	return "SurveyRecordDetailValidationError"
}

func (e SurveyRecordDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordDetail." + e.field + ": " + e.message + cause
}

type WebSettingsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e WebSettingsValidationError) Field() string { return e.field }

func (e WebSettingsValidationError) Reason() string { return e.reason }

func (e WebSettingsValidationError) Message() string { return e.message }

func (e WebSettingsValidationError) Cause() error { return e.cause }

func (e WebSettingsValidationError) ErrorName() string { return "WebSettingsValidationError" }

func (e WebSettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid WebSettings." + e.field + ": " + e.message + cause
}

type SettingsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SettingsValidationError) Field() string { return e.field }

func (e SettingsValidationError) Reason() string { return e.reason }

func (e SettingsValidationError) Message() string { return e.message }

func (e SettingsValidationError) Cause() error { return e.cause }

func (e SettingsValidationError) ErrorName() string { return "SettingsValidationError" }

func (e SettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings." + e.field + ": " + e.message + cause
}

type Settings_TimelimitconfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e Settings_TimelimitconfigValidationError) Field() string { return e.field }

func (e Settings_TimelimitconfigValidationError) Reason() string { return e.reason }

func (e Settings_TimelimitconfigValidationError) Message() string { return e.message }

func (e Settings_TimelimitconfigValidationError) Cause() error { return e.cause }

func (e Settings_TimelimitconfigValidationError) ErrorName() string {
	return "Settings_TimelimitconfigValidationError"
}

func (e Settings_TimelimitconfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings_Timelimitconfig." + e.field + ": " + e.message + cause
}

type Settings_AnswertimesconfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e Settings_AnswertimesconfigValidationError) Field() string { return e.field }

func (e Settings_AnswertimesconfigValidationError) Reason() string { return e.reason }

func (e Settings_AnswertimesconfigValidationError) Message() string { return e.message }

func (e Settings_AnswertimesconfigValidationError) Cause() error { return e.cause }

func (e Settings_AnswertimesconfigValidationError) ErrorName() string {
	return "Settings_AnswertimesconfigValidationError"
}

func (e Settings_AnswertimesconfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings_Answertimesconfig." + e.field + ": " + e.message + cause
}

type Settings_PeriodicControlValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e Settings_PeriodicControlValidationError) Field() string { return e.field }

func (e Settings_PeriodicControlValidationError) Reason() string { return e.reason }

func (e Settings_PeriodicControlValidationError) Message() string { return e.message }

func (e Settings_PeriodicControlValidationError) Cause() error { return e.cause }

func (e Settings_PeriodicControlValidationError) ErrorName() string {
	return "Settings_PeriodicControlValidationError"
}

func (e Settings_PeriodicControlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings_PeriodicControl." + e.field + ": " + e.message + cause
}

type Settings_BaseruleconfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e Settings_BaseruleconfigValidationError) Field() string { return e.field }

func (e Settings_BaseruleconfigValidationError) Reason() string { return e.reason }

func (e Settings_BaseruleconfigValidationError) Message() string { return e.message }

func (e Settings_BaseruleconfigValidationError) Cause() error { return e.cause }

func (e Settings_BaseruleconfigValidationError) ErrorName() string {
	return "Settings_BaseruleconfigValidationError"
}

func (e Settings_BaseruleconfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings_Baseruleconfig." + e.field + ": " + e.message + cause
}

type Settings_AnswerlimitconfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e Settings_AnswerlimitconfigValidationError) Field() string { return e.field }

func (e Settings_AnswerlimitconfigValidationError) Reason() string { return e.reason }

func (e Settings_AnswerlimitconfigValidationError) Message() string { return e.message }

func (e Settings_AnswerlimitconfigValidationError) Cause() error { return e.cause }

func (e Settings_AnswerlimitconfigValidationError) ErrorName() string {
	return "Settings_AnswerlimitconfigValidationError"
}

func (e Settings_AnswerlimitconfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Settings_Answerlimitconfig." + e.field + ": " + e.message + cause
}

type MaterialsconfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e MaterialsconfigValidationError) Field() string { return e.field }

func (e MaterialsconfigValidationError) Reason() string { return e.reason }

func (e MaterialsconfigValidationError) Message() string { return e.message }

func (e MaterialsconfigValidationError) Cause() error { return e.cause }

func (e MaterialsconfigValidationError) ErrorName() string { return "MaterialsconfigValidationError" }

func (e MaterialsconfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Materialsconfig." + e.field + ": " + e.message + cause
}
