// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_record.proto

package proto

func (x *SurveyRecordListRequest) Validate() error {
	return nil
}

func (x *SurveyRecordListV2Request) Validate() error {
	if x.GetSurveyId() < 1 {
		return SurveyRecordListV2RequestValidationError{
			field:   "SurveyId",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	return nil
}

func (x *Cloumn) Validate() error {
	return nil
}

func (x *SurveyRecordListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyRecordListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyRecordsRequest) Validate() error {
	return nil
}

func (x *SetValidSurveyRecordRequest) Validate() error {
	return nil
}

func (x *SurveyRecordDetailsRequest) Validate() error {
	return nil
}

func (x *SurveyRecord) Validate() error {
	if len(x.GetUid()) > 128 {
		return SurveyRecordValidationError{
			field:   "Uid",
			reason:  "max_length",
			message: "value length must be at most 128 bytes",
		}
	}
	if len(x.GetOpenid()) > 125 {
		return SurveyRecordValidationError{
			field:   "Openid",
			reason:  "max_length",
			message: "value length must be at most 125 bytes",
		}
	}
	return nil
}

func (x *SurveyViewCreateReq) Validate() error {
	return nil
}

func (x *SurveyViewCreateRes) Validate() error {
	return nil
}

func (x *SurveyView) Validate() error {
	if len(x.GetName()) > 50 {
		return SurveyViewValidationError{
			field:   "Name",
			reason:  "max_length",
			message: "value length must be at most 50 bytes",
		}
	}
	return nil
}

func (x *SurveyViewListReq) Validate() error {
	return nil
}

func (x *SurveyViewListRes) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyViewListResValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyViewDeleteReq) Validate() error {
	return nil
}

type SurveyRecordListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordListRequestValidationError) Field() string { return e.field }

func (e SurveyRecordListRequestValidationError) Reason() string { return e.reason }

func (e SurveyRecordListRequestValidationError) Message() string { return e.message }

func (e SurveyRecordListRequestValidationError) Cause() error { return e.cause }

func (e SurveyRecordListRequestValidationError) ErrorName() string {
	return "SurveyRecordListRequestValidationError"
}

func (e SurveyRecordListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordListRequest." + e.field + ": " + e.message + cause
}

type SurveyRecordListV2RequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordListV2RequestValidationError) Field() string { return e.field }

func (e SurveyRecordListV2RequestValidationError) Reason() string { return e.reason }

func (e SurveyRecordListV2RequestValidationError) Message() string { return e.message }

func (e SurveyRecordListV2RequestValidationError) Cause() error { return e.cause }

func (e SurveyRecordListV2RequestValidationError) ErrorName() string {
	return "SurveyRecordListV2RequestValidationError"
}

func (e SurveyRecordListV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordListV2Request." + e.field + ": " + e.message + cause
}

type CloumnValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CloumnValidationError) Field() string { return e.field }

func (e CloumnValidationError) Reason() string { return e.reason }

func (e CloumnValidationError) Message() string { return e.message }

func (e CloumnValidationError) Cause() error { return e.cause }

func (e CloumnValidationError) ErrorName() string { return "CloumnValidationError" }

func (e CloumnValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Cloumn." + e.field + ": " + e.message + cause
}

type SurveyRecordListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordListResponseValidationError) Field() string { return e.field }

func (e SurveyRecordListResponseValidationError) Reason() string { return e.reason }

func (e SurveyRecordListResponseValidationError) Message() string { return e.message }

func (e SurveyRecordListResponseValidationError) Cause() error { return e.cause }

func (e SurveyRecordListResponseValidationError) ErrorName() string {
	return "SurveyRecordListResponseValidationError"
}

func (e SurveyRecordListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordListResponse." + e.field + ": " + e.message + cause
}

type SurveyRecordsRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordsRequestValidationError) Field() string { return e.field }

func (e SurveyRecordsRequestValidationError) Reason() string { return e.reason }

func (e SurveyRecordsRequestValidationError) Message() string { return e.message }

func (e SurveyRecordsRequestValidationError) Cause() error { return e.cause }

func (e SurveyRecordsRequestValidationError) ErrorName() string {
	return "SurveyRecordsRequestValidationError"
}

func (e SurveyRecordsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordsRequest." + e.field + ": " + e.message + cause
}

type SetValidSurveyRecordRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SetValidSurveyRecordRequestValidationError) Field() string { return e.field }

func (e SetValidSurveyRecordRequestValidationError) Reason() string { return e.reason }

func (e SetValidSurveyRecordRequestValidationError) Message() string { return e.message }

func (e SetValidSurveyRecordRequestValidationError) Cause() error { return e.cause }

func (e SetValidSurveyRecordRequestValidationError) ErrorName() string {
	return "SetValidSurveyRecordRequestValidationError"
}

func (e SetValidSurveyRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SetValidSurveyRecordRequest." + e.field + ": " + e.message + cause
}

type SurveyRecordDetailsRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordDetailsRequestValidationError) Field() string { return e.field }

func (e SurveyRecordDetailsRequestValidationError) Reason() string { return e.reason }

func (e SurveyRecordDetailsRequestValidationError) Message() string { return e.message }

func (e SurveyRecordDetailsRequestValidationError) Cause() error { return e.cause }

func (e SurveyRecordDetailsRequestValidationError) ErrorName() string {
	return "SurveyRecordDetailsRequestValidationError"
}

func (e SurveyRecordDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordDetailsRequest." + e.field + ": " + e.message + cause
}

type SurveyRecordValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordValidationError) Field() string { return e.field }

func (e SurveyRecordValidationError) Reason() string { return e.reason }

func (e SurveyRecordValidationError) Message() string { return e.message }

func (e SurveyRecordValidationError) Cause() error { return e.cause }

func (e SurveyRecordValidationError) ErrorName() string { return "SurveyRecordValidationError" }

func (e SurveyRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecord." + e.field + ": " + e.message + cause
}

type SurveyViewCreateReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewCreateReqValidationError) Field() string { return e.field }

func (e SurveyViewCreateReqValidationError) Reason() string { return e.reason }

func (e SurveyViewCreateReqValidationError) Message() string { return e.message }

func (e SurveyViewCreateReqValidationError) Cause() error { return e.cause }

func (e SurveyViewCreateReqValidationError) ErrorName() string {
	return "SurveyViewCreateReqValidationError"
}

func (e SurveyViewCreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyViewCreateReq." + e.field + ": " + e.message + cause
}

type SurveyViewCreateResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewCreateResValidationError) Field() string { return e.field }

func (e SurveyViewCreateResValidationError) Reason() string { return e.reason }

func (e SurveyViewCreateResValidationError) Message() string { return e.message }

func (e SurveyViewCreateResValidationError) Cause() error { return e.cause }

func (e SurveyViewCreateResValidationError) ErrorName() string {
	return "SurveyViewCreateResValidationError"
}

func (e SurveyViewCreateResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyViewCreateRes." + e.field + ": " + e.message + cause
}

type SurveyViewValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewValidationError) Field() string { return e.field }

func (e SurveyViewValidationError) Reason() string { return e.reason }

func (e SurveyViewValidationError) Message() string { return e.message }

func (e SurveyViewValidationError) Cause() error { return e.cause }

func (e SurveyViewValidationError) ErrorName() string { return "SurveyViewValidationError" }

func (e SurveyViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyView." + e.field + ": " + e.message + cause
}

type SurveyViewListReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewListReqValidationError) Field() string { return e.field }

func (e SurveyViewListReqValidationError) Reason() string { return e.reason }

func (e SurveyViewListReqValidationError) Message() string { return e.message }

func (e SurveyViewListReqValidationError) Cause() error { return e.cause }

func (e SurveyViewListReqValidationError) ErrorName() string {
	return "SurveyViewListReqValidationError"
}

func (e SurveyViewListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyViewListReq." + e.field + ": " + e.message + cause
}

type SurveyViewListResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewListResValidationError) Field() string { return e.field }

func (e SurveyViewListResValidationError) Reason() string { return e.reason }

func (e SurveyViewListResValidationError) Message() string { return e.message }

func (e SurveyViewListResValidationError) Cause() error { return e.cause }

func (e SurveyViewListResValidationError) ErrorName() string {
	return "SurveyViewListResValidationError"
}

func (e SurveyViewListResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyViewListRes." + e.field + ": " + e.message + cause
}

type SurveyViewDeleteReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyViewDeleteReqValidationError) Field() string { return e.field }

func (e SurveyViewDeleteReqValidationError) Reason() string { return e.reason }

func (e SurveyViewDeleteReqValidationError) Message() string { return e.message }

func (e SurveyViewDeleteReqValidationError) Cause() error { return e.cause }

func (e SurveyViewDeleteReqValidationError) ErrorName() string {
	return "SurveyViewDeleteReqValidationError"
}

func (e SurveyViewDeleteReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyViewDeleteReq." + e.field + ": " + e.message + cause
}
