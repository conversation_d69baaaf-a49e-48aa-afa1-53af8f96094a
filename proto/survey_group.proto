syntax = "proto3";

package papegames.sparrow.survey;

import "google/api/field_behavior.proto";
import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// CmsSurveyGroupInfo
message CmsSurveyGroupInfo {
  option (tagger.disable_omitempty) = true;
  uint64 id = 1;
  string clientid = 2; // clientid
  string name = 3; // 问卷组名称
  int32 is_publish = 4; // 是否发布（0: 未发布，1: 已发布）
  int32 limit_type = 5; // 是否有修改未发布（0: 无，1: 有）
  int32 type = 6; // 问卷组类型 1:ip 2:语言
  string settings = 7; // 问卷组设置
  string hash_code = 8; // 问卷组id-hash
  int32 is_delete = 9; // 是否删除（0: 未删除，1: 已删除）
  string ctime = 10; // 创建时间
  string mtime = 11; // 最近修改时间
  string creator = 12; // 创建人
  string editor = 13; // 最近修改人
}

message SurveyGroupCreateReq {
  // 租户ID
  int64 	client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 名称
  string 	name = 2 [(google.api.field_behavior) = REQUIRED];
  // 答题次数限制
  int32 limit_type = 3 [(google.api.field_behavior) = REQUIRED];
  // 问卷组类型 1:ip 2:语言
  int32 type = 4 [(google.api.field_behavior) = REQUIRED];
  // 问卷组设置
  string settings = 5 [(google.api.field_behavior) = REQUIRED];
}

message SurveyGroupDetailReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 组ID
  int64 id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyGroupUpdateReq {
  // 租户ID
  int64 	client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷组ID
  int64 	id = 2 [(google.api.field_behavior) = REQUIRED];
  // 名称
  string 	name = 3 [(google.api.field_behavior) = REQUIRED];
  // 答题次数限制
  int32 limit_type = 4 [(google.api.field_behavior) = REQUIRED];
  // 问卷组类型 1:ip 2:语言
  int32 type = 5 [(google.api.field_behavior) = REQUIRED];
  // 问卷组设置
  string settings = 6 [(google.api.field_behavior) = REQUIRED];
}

message SurveyGroupListReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷组ID
  int64 	id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyGroupListRes {
  // 问卷组列表
  repeated CmsSurveyGroupInfo list = 1;
  // 总数
  int64 total = 2;
}

enum SurveyGroupSubUpdateType {
  SurveyGroupSubUpdateType_Unknown = 0;
  // 发布
  SurveyGroupSubUpdateType_Publish = 1;
  // 暂停
  SurveyGroupSubUpdateType_Pause = 2;
  // 删除
  SurveyGroupSubUpdateType_Delete = 3;
  // 关闭
  SurveyGroupSubUpdateType_Closed = 4;
  // 更新setting
  SurveyGroupSubUpdateType_Settings = 5;
}

message SurveyGroupSubUpdateReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷组ID
  int64 	id = 2 [(google.api.field_behavior) = REQUIRED];
  // 操作类型
  SurveyGroupSubUpdateType type = 3;
  // 要更新的值
  string value = 4;
}

message SurveyGroupOverwriteSendReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷组ID
  int64 survey_group_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message SurveyGroupOverwriteSyncReq {
  // 租户ID
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  // 问卷组
  string survey_group = 2 [(google.api.field_behavior) = REQUIRED];
}