// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_export_task.proto

package proto

func (x *SurveyExportTaskListRequest) Validate() error {
	return nil
}

func (x *SurveyExportTaskListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyExportTaskListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyExportTaskDetailsReq) Validate() error {
	return nil
}

func (x *DelSurveyExportTaskReq) Validate() error {
	if len(x.GetIds()) == 0 {
		return DelSurveyExportTaskReqValidationError{
			field:   "Ids",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyExportTask) Validate() error {
	if len(x.GetName()) > 125 {
		return SurveyExportTaskValidationError{
			field:   "Name",
			reason:  "max_length",
			message: "value length must be at most 125 bytes",
		}
	}
	return nil
}

func (x *UserStrategy) Validate() error {
	if len(x.GetClusterName()) == 0 {
		return UserStrategyValidationError{
			field:   "ClusterName",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetEntityName()) == 0 {
		return UserStrategyValidationError{
			field:   "EntityName",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyExportUserClusterSubmitReq) Validate() error {
	if x.GetUserStrategy() == nil {
		return SurveyExportUserClusterSubmitReqValidationError{
			field:   "UserStrategy",
			reason:  "required",
			message: "value is required",
		}
	}
	if v, ok := interface{}(x.GetUserStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyExportUserClusterSubmitReqValidationError{
				field:   "UserStrategy",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *SurveyExportHeadersReq) Validate() error {
	return nil
}

func (x *SurveyExportHeadersRes) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyExportHeadersResValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyExportHeadersRes_QuestionRowTitle) Validate() error {
	return nil
}

func (x *SurveyExportHeadersRes_SelectOptions) Validate() error {
	return nil
}

func (x *SurveyExportHeadersRes_Question) Validate() error {
	for _, item := range x.GetQuestionRowTitles() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyExportHeadersRes_QuestionValidationError{
					field:   "QuestionRowTitles",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	for _, item := range x.GetSelectOptions() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyExportHeadersRes_QuestionValidationError{
					field:   "SelectOptions",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

type SurveyExportTaskListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportTaskListRequestValidationError) Field() string { return e.field }

func (e SurveyExportTaskListRequestValidationError) Reason() string { return e.reason }

func (e SurveyExportTaskListRequestValidationError) Message() string { return e.message }

func (e SurveyExportTaskListRequestValidationError) Cause() error { return e.cause }

func (e SurveyExportTaskListRequestValidationError) ErrorName() string {
	return "SurveyExportTaskListRequestValidationError"
}

func (e SurveyExportTaskListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportTaskListRequest." + e.field + ": " + e.message + cause
}

type SurveyExportTaskListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportTaskListResponseValidationError) Field() string { return e.field }

func (e SurveyExportTaskListResponseValidationError) Reason() string { return e.reason }

func (e SurveyExportTaskListResponseValidationError) Message() string { return e.message }

func (e SurveyExportTaskListResponseValidationError) Cause() error { return e.cause }

func (e SurveyExportTaskListResponseValidationError) ErrorName() string {
	return "SurveyExportTaskListResponseValidationError"
}

func (e SurveyExportTaskListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportTaskListResponse." + e.field + ": " + e.message + cause
}

type SurveyExportTaskDetailsReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportTaskDetailsReqValidationError) Field() string { return e.field }

func (e SurveyExportTaskDetailsReqValidationError) Reason() string { return e.reason }

func (e SurveyExportTaskDetailsReqValidationError) Message() string { return e.message }

func (e SurveyExportTaskDetailsReqValidationError) Cause() error { return e.cause }

func (e SurveyExportTaskDetailsReqValidationError) ErrorName() string {
	return "SurveyExportTaskDetailsReqValidationError"
}

func (e SurveyExportTaskDetailsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportTaskDetailsReq." + e.field + ": " + e.message + cause
}

type DelSurveyExportTaskReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DelSurveyExportTaskReqValidationError) Field() string { return e.field }

func (e DelSurveyExportTaskReqValidationError) Reason() string { return e.reason }

func (e DelSurveyExportTaskReqValidationError) Message() string { return e.message }

func (e DelSurveyExportTaskReqValidationError) Cause() error { return e.cause }

func (e DelSurveyExportTaskReqValidationError) ErrorName() string {
	return "DelSurveyExportTaskReqValidationError"
}

func (e DelSurveyExportTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DelSurveyExportTaskReq." + e.field + ": " + e.message + cause
}

type SurveyExportTaskValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportTaskValidationError) Field() string { return e.field }

func (e SurveyExportTaskValidationError) Reason() string { return e.reason }

func (e SurveyExportTaskValidationError) Message() string { return e.message }

func (e SurveyExportTaskValidationError) Cause() error { return e.cause }

func (e SurveyExportTaskValidationError) ErrorName() string { return "SurveyExportTaskValidationError" }

func (e SurveyExportTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportTask." + e.field + ": " + e.message + cause
}

type UserStrategyValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UserStrategyValidationError) Field() string { return e.field }

func (e UserStrategyValidationError) Reason() string { return e.reason }

func (e UserStrategyValidationError) Message() string { return e.message }

func (e UserStrategyValidationError) Cause() error { return e.cause }

func (e UserStrategyValidationError) ErrorName() string { return "UserStrategyValidationError" }

func (e UserStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UserStrategy." + e.field + ": " + e.message + cause
}

type SurveyExportUserClusterSubmitReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportUserClusterSubmitReqValidationError) Field() string { return e.field }

func (e SurveyExportUserClusterSubmitReqValidationError) Reason() string { return e.reason }

func (e SurveyExportUserClusterSubmitReqValidationError) Message() string { return e.message }

func (e SurveyExportUserClusterSubmitReqValidationError) Cause() error { return e.cause }

func (e SurveyExportUserClusterSubmitReqValidationError) ErrorName() string {
	return "SurveyExportUserClusterSubmitReqValidationError"
}

func (e SurveyExportUserClusterSubmitReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportUserClusterSubmitReq." + e.field + ": " + e.message + cause
}

type SurveyExportHeadersReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportHeadersReqValidationError) Field() string { return e.field }

func (e SurveyExportHeadersReqValidationError) Reason() string { return e.reason }

func (e SurveyExportHeadersReqValidationError) Message() string { return e.message }

func (e SurveyExportHeadersReqValidationError) Cause() error { return e.cause }

func (e SurveyExportHeadersReqValidationError) ErrorName() string {
	return "SurveyExportHeadersReqValidationError"
}

func (e SurveyExportHeadersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportHeadersReq." + e.field + ": " + e.message + cause
}

type SurveyExportHeadersResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportHeadersResValidationError) Field() string { return e.field }

func (e SurveyExportHeadersResValidationError) Reason() string { return e.reason }

func (e SurveyExportHeadersResValidationError) Message() string { return e.message }

func (e SurveyExportHeadersResValidationError) Cause() error { return e.cause }

func (e SurveyExportHeadersResValidationError) ErrorName() string {
	return "SurveyExportHeadersResValidationError"
}

func (e SurveyExportHeadersResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportHeadersRes." + e.field + ": " + e.message + cause
}

type SurveyExportHeadersRes_QuestionRowTitleValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) Field() string { return e.field }

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) Reason() string { return e.reason }

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) Message() string { return e.message }

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) Cause() error { return e.cause }

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) ErrorName() string {
	return "SurveyExportHeadersRes_QuestionRowTitleValidationError"
}

func (e SurveyExportHeadersRes_QuestionRowTitleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportHeadersRes_QuestionRowTitle." + e.field + ": " + e.message + cause
}

type SurveyExportHeadersRes_SelectOptionsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportHeadersRes_SelectOptionsValidationError) Field() string { return e.field }

func (e SurveyExportHeadersRes_SelectOptionsValidationError) Reason() string { return e.reason }

func (e SurveyExportHeadersRes_SelectOptionsValidationError) Message() string { return e.message }

func (e SurveyExportHeadersRes_SelectOptionsValidationError) Cause() error { return e.cause }

func (e SurveyExportHeadersRes_SelectOptionsValidationError) ErrorName() string {
	return "SurveyExportHeadersRes_SelectOptionsValidationError"
}

func (e SurveyExportHeadersRes_SelectOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportHeadersRes_SelectOptions." + e.field + ": " + e.message + cause
}

type SurveyExportHeadersRes_QuestionValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyExportHeadersRes_QuestionValidationError) Field() string { return e.field }

func (e SurveyExportHeadersRes_QuestionValidationError) Reason() string { return e.reason }

func (e SurveyExportHeadersRes_QuestionValidationError) Message() string { return e.message }

func (e SurveyExportHeadersRes_QuestionValidationError) Cause() error { return e.cause }

func (e SurveyExportHeadersRes_QuestionValidationError) ErrorName() string {
	return "SurveyExportHeadersRes_QuestionValidationError"
}

func (e SurveyExportHeadersRes_QuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyExportHeadersRes_Question." + e.field + ": " + e.message + cause
}
