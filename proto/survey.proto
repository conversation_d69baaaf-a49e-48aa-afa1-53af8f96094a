syntax = "proto3";

package papegames.sparrow.survey;
import "papegames/type/empty.proto";
import "papegames/type/raw_message.proto";

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "proto/survey_export_task.proto";
import "proto/survey_record.proto";
import "proto/survey_conf.proto";
import "proto/survey_deliver.proto";
import "proto/settings.proto";
import "proto/survey_group.proto";

import "openapiv3/annotations.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

// This API represents survey service.
service SurveyService {
  option (google.api.default_host) = "survey.papegames.com";

  rpc Health(HealthRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "p0"}}, {name: "x-apifox-folder", value: {yaml: "基础接口"}}]
      operation_id: "健康检查"
    };
    option (google.api.http) = {
      get: "/v1/survey/health"
    };
  }

  // 导出任务列表
  rpc SurveyExportTaskList(SurveyExportTaskListRequest) returns (SurveyExportTaskListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "导出"}}],
      operation_id: "导出任务列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/statistics/user-answer-record/survey-export/list"
    };
    option (google.api.method_signature) = "filter";
  }

  // 创建导出任务
  rpc CreateSurveyExportTask(SurveyExportTask) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "导出"}}],
      operation_id: "创建导出任务"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/survey-export/create",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 删除导出任务
  rpc DelSurveyExportTask(DelSurveyExportTaskReq) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "导出"}}],
      operation_id: "删除导出任务"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/survey-export/del",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 重置导出任务状态
  rpc ResetSurveyExportTaskStatus(SurveyExportTaskDetailsReq) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "导出"}}],
      operation_id: "重置导出状态"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/survey-export/resetStatus",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 回收答卷列表
  rpc SurveyRecordList(SurveyRecordListV2Request) returns (SurveyRecordListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "回收答卷列表"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/list",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }


  // 回收答卷列表
  rpc SurveyRecordListV2(SurveyRecordListV2Request) returns (papegames.type.RawMessage) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "回收答卷列表"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/list-v2",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 答卷列表删除
  rpc DelSurveyRecord(SurveyRecordDetailsRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "答卷列表删除"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/delete",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 标记答卷记录有效
  rpc SetValidSurveyRecord(SurveyRecordsRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "标记答卷记录有效"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/set-valid",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 标记答卷记录无效
  rpc SetInvalidSurveyRecord(SetValidSurveyRecordRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "标记答卷记录无效"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/user-answer-record/set-invalid",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 无效答卷列表
  rpc InValidSurveyRecordList(SurveyRecordListRequest) returns (SurveyRecordListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "无效答卷列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/statistics/user-answer-record/invalid-list"
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷预览
  rpc SurveyPreview(SurveyPreviewReq) returns (Survey) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷预览"
    };
    option (google.api.http) = {
      get: "/v1/survey/preview",  // ?client_id=1008
    };
    option (google.api.method_signature) = "filter";
  }

  // 查看答卷-问卷详情
  rpc SurveyRecordDetail(SurveyRecordConfDetailsReq) returns (SurveyRecordConfDetailsRes) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "查看答卷"
    };
    option (google.api.http) = {
      get: "/v1/survey/statistics/user-answer-record/detail"
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷列表
  rpc SurveyList(SurveyListRequest) returns (SurveyListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/list"
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷创建
  rpc CreateSurvey(CreateSurveyRequest) returns (SurveyResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "创建问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/create",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 问卷规则-问卷管理-问卷详情
  rpc SurveyStatistics(SurveyStatisticsRequest) returns (SurveyStatisticsResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷规则"
    };
    option (google.api.http) = {
      get: "/v1/survey/rule/detail",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷统计报告-列表
  rpc GetSurveyStatisticsList(SurveyDetailRequest) returns (SurveyDetailResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷规则"
    };
    option (google.api.http) = {
      get: "/v1/survey/statistics/list",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷展示
  rpc SurveyShow(ShowSurveyRequest) returns (Survey) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷展示"
    };
    option (google.api.http) = {
      get: "/v1/survey/show",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷删除
  rpc DeleteSurvey(SurveyDelRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷删除"
    };
    option (google.api.http) = {
      post: "/v1/survey/logical-destory",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 复制问卷
  rpc CopySurvey(SurveyRequest) returns (SurveyResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "复制问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/copy",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 同步问卷
  rpc SyncSurvey(SyncSurveyRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "同步问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/sync",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 同步问卷写入接口
  rpc ImpSurvey(ImpSurveyRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷同步写入"
    };
    option (google.api.http) = {
      post: "/v1/survey/imp",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷暂停/开启作答
  rpc SetStatusSurvey(SurveySetStatusRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "答卷列表"}}],
      operation_id: "问卷暂停/开启作答"
    };
    option (google.api.http) = {
      post: "/v1/survey/set-status",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 回收站-问卷列表
  rpc SurveyRecycleList(SurveyListRequest) returns (SurveyListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "回收站"}}],
      operation_id: "问卷列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/recycle-list",
    };
    option (google.api.method_signature) = "filter";
  }

  // 彻底删除
  rpc DeleteSurveyRecycle(SurveyDelRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "回收站"}}],
      operation_id: "彻底删除"
    };
    option (google.api.http) = {
      post: "/v1/survey/delete",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 恢复问卷
  rpc RecoverSurveyRecycle(RecoverSurveyRecycleReq) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "回收站"}}],
      operation_id: "恢复问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/recover-logical-destory",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 清空回收站
  rpc ClearAllSurveyRecycle(ClearAllSurveyRecycleRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "回收站"}}],
      operation_id: "清空回收站"
    };
    option (google.api.http) = {
      post: "/v1/survey/clear-logical-destory",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 恢复所有问卷
  rpc RecoverAllSurveyRecycle(Survey) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "回收站"}}],
      operation_id: "恢复所有问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/recover-all-logical-destory",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  // 获取投放列表
  rpc GetDeliverList(GetDeliverListRequest) returns (GetDeliverListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "投放"}}],
      operation_id: "获取投放列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/general/get-deliver-list",
    };
    option (google.api.method_signature) = "filter";
  }

  // 获取区服列表
  rpc GetZoneList(GetZoneListRequest) returns (GetZoneListResponse) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "biz"}}],
      operation_id: "获取区服列表"
    };
    option (google.api.http) = {
      get: "/v1/survey/general/get-zone-list",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc UpdateSurvey(UpdateSurveyRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "问卷列表"}}],
      operation_id: "更新问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/update",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  rpc PublishSurvey(PublishSurveyRequest) returns (papegames.type.Empty) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "问卷列表"}}],
      operation_id: "发布问卷"
    };
    option (google.api.http) = {
      post: "/v1/survey/publish",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc GetInputMethodList(SurveyInputMethodListRequest) returns (papegames.type.RawMessage) {
    option (openapi.v3.operation) = {
      specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "问卷列表"}}],
      operation_id: "回收概况"
    };
    option (google.api.http) = {
      post: "/v1/survey/statistics/detail/input-method-list",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

    // 问卷统计详情
    rpc SurveyStatisticsDetail(SurveyRequest) returns (papegames.type.RawMessage) {
        option (openapi.v3.operation) = {
            specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "问卷统计"}}],
            operation_id: "回收概况-问卷统计"
        };
        option (google.api.http) = {
            post: "/v1/survey/statistics/detail",
            body: "*",
        };
        option (google.api.method_signature) = "json";
    }

    // 问卷统计详情 - v2
    rpc SurveyStatisticsDetailOld(SurveyRequest) returns (papegames.type.RawMessage) {
        option (openapi.v3.operation) = {
            specification_extension: [{name: "level", value: {yaml: "P2"}}, {name: "x-apifox-folder", value: {yaml: "问卷统计"}}],
            operation_id: "回收概况-问卷统计"
        };
        option (google.api.http) = {
            post: "/v1/survey/statistics/detail2",
            body: "*",
        };
        option (google.api.method_signature) = "json";
    }

  rpc getUserInfo(GetUserInfoRequest) returns (GetUserInfoResponse) {
    option (google.api.http) = {
      get: "/v1/survey/user/get-info",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  rpc userCheck(UserCheckRequest) returns (UserCheckResponse) {
    option (google.api.http) = {
      get: "/v1/survey/user/check",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc StatisticsUpdate(StatisticsUpdateRequest) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/rule/update",
      body: "*",
    };
    option (google.api.method_signature) = "json";
  }

  rpc GetLatestSurveyBySurveyId(GetLatestSurveyBySurveyIdRequest) returns (GetLatestSurveyBySurveyIdResponse) {
    option (google.api.http) = {
      get: "/v1/survey/get-latest-survey-by-survey-id",
    };
    option (google.api.method_signature) = "filter";
  }

  // 获取兑换码列表
  rpc GetValidRedeemConfigList(ValidRedeemConfigRequest) returns (ValidRedeemConfigResponse) {
    option (google.api.http) = {
      get: "/v1/survey/settings/get-valid-redeem-config-list",
    };
    option (google.api.method_signature) = "filter";
  }

  // 获取平台邮件模板列表
  rpc GetPreAwardTemplateList(PreAwardTemplateRequest) returns (PreAwardTemplateResponse) {
    option (google.api.http) = {
      get: "/v1/survey/settings/get-pre-award-template-list",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc Upload(UploadRequest) returns (UploadResponse) {
    option (google.api.http) = {
      post: "/v1/survey/general/upload",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷筛选器创建
  rpc SurveyViewCreate(SurveyViewCreateReq) returns (SurveyViewCreateRes) {
    option (google.api.http) = {
      post: "/v1/survey/view/create",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷筛选器列表
  rpc SurveyViewList(SurveyViewListReq) returns (SurveyViewListRes) {
    option (google.api.http) = {
      get: "/v1/survey/view/list",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷筛选器编辑
  rpc SurveyViewUpdate(SurveyView) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/view/update",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷筛选器删除
  rpc SurveyViewDelete(SurveyViewDeleteReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/view/delete",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷组 - 新建
  rpc SurveyGroupCreate(SurveyGroupCreateReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/group/create",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷组 - 详情
  rpc SurveyGroupDetail(SurveyGroupDetailReq) returns (CmsSurveyGroupInfo) {
    option (google.api.http) = {
      post: "/v1/survey/group/detail",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷组 - 编辑
  rpc SurveyGroupUpdate(SurveyGroupUpdateReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/group/update",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷组 - 列表
  rpc SurveyGroupList(SurveyGroupListReq) returns (SurveyGroupListRes) {
    option (google.api.http) = {
      post: "/v1/survey/group/list",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  // 问卷组 - 部分字段更新
  rpc SurveyGroupSubUpdate(SurveyGroupSubUpdateReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/group/sub_update",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyOverwriteSend(SurveyOverwriteSendReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/overwrite/send",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyOverwriteSync(SurveyOverwriteSyncReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/overwrite/sync",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyGroupOverwriteSend(SurveyGroupOverwriteSendReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/group/overwrite/send",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyGroupOverwriteSync(SurveyGroupOverwriteSyncReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/group/overwrite/sync",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyExportUserClusterSubmit(SurveyExportUserClusterSubmitReq) returns (papegames.type.Empty) {
    option (google.api.http) = {
      post: "/v1/survey/export/user-cluster-submit",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }

  rpc SurveyExportHeaders(SurveyExportHeadersReq) returns (SurveyExportHeadersRes) {
    option (google.api.http) = {
      post: "/v1/survey/export/headers",
      body: "*",
    };
    option (google.api.method_signature) = "filter";
  }
}

message HealthRequest {
}

message GetUserInfoRequest {
  int64 client_id = 1;
}

message GetUserInfoResponse {
  Userinfo userInfo = 1;
  repeated Clientlist clientList = 2;
  Permission permission = 3;
}

message Userinfo {
  string uid = 1;
  string name = 2;
  string nickname = 3;
  string phone = 4;
  string avatar = 5;
  string email = 6;
  int64 client_id = 8;
}

message Clientlist {
  int64 client_id = 1;
  string memo = 2;
  string default_language = 3;
  string group_id = 4;
  string group_name = 5;
}

message Permission {
  bool surveySummary_get = 1;
  bool surveyManage_add = 2;
  bool surveyManage_edit = 3;
  bool surveySummary_export = 4;
  bool surveyManage_delete = 5;
  bool member_get = 6;
  bool member_add = 7;
  bool member_delete = 8;
}

message JwtUserInfoV1 {
  message Data {
    uint32 user_id = 1;
    string pops_user_id = 2;
    string username = 3;
    string nickname = 4;
    string email = 5;
    bool is_superuser = 6;
  }

  uint32 exp = 1;
  uint32 nbf = 2;
  uint32 iat = 3;
  string iss = 4;
  uint32 id = 5;
  string sub = 6;
  Data data = 7;
}

message UserCheckRequest {
  int64 client_id = 1;
  string name = 2;
}
message UserCheckResponse {
}

message ClearAllSurveyRecycleRequest {
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
}

message CreateSurveyRequest {
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  string settings = 3 [(google.api.field_behavior) = REQUIRED];
  string stime = 4;
  string etime = 5;
}

message StatisticsUpdateRequest {
  int64 id = 6 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  string settings = 3; // 可以只修改name
  string stime = 4;
  string etime = 5;
}

message SurveyStatisticsRequest {
  int64 client_id = 1;
  int64 id = 2;
}

message SurveyStatisticsResponse {
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  string settings = 3 [(google.api.field_behavior) = REQUIRED];
  string stime = 4;
  string etime = 5;
}

message GetLatestSurveyBySurveyIdRequest {
  int64 client_id = 1 [(google.api.field_behavior) = REQUIRED];
  int64 survey_id = 2 [(google.api.field_behavior) = REQUIRED];
}

message GetLatestSurveyBySurveyIdResponse {
  int64 id = 1 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
  int64 survey_id = 2 [(google.api.field_behavior) = REQUIRED];
  string name = 3 [(google.api.field_behavior) = REQUIRED];
  string schema = 4 [(google.api.field_behavior) = REQUIRED];
  string settings = 5 [(google.api.field_behavior) = REQUIRED];
  string web_settings = 6 [(google.api.field_behavior) = REQUIRED];
}

message UploadRequest{
  string file = 1;
}

message UploadResponse{
  string  url = 1;
}
