// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/settings.proto

package proto

func (x *ValidRedeemConfigRequest) Validate() error {
	return nil
}

func (x *ValidRedeemConfigResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ValidRedeemConfigResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *ValidRedeemConfig) Validate() error {
	return nil
}

func (x *PreAwardTemplateRequest) Validate() error {
	return nil
}

func (x *PreAwardTemplateResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PreAwardTemplateResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *PreAwardTemplate) Validate() error {
	return nil
}

type ValidRedeemConfigRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ValidRedeemConfigRequestValidationError) Field() string { return e.field }

func (e ValidRedeemConfigRequestValidationError) Reason() string { return e.reason }

func (e ValidRedeemConfigRequestValidationError) Message() string { return e.message }

func (e ValidRedeemConfigRequestValidationError) Cause() error { return e.cause }

func (e ValidRedeemConfigRequestValidationError) ErrorName() string {
	return "ValidRedeemConfigRequestValidationError"
}

func (e ValidRedeemConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ValidRedeemConfigRequest." + e.field + ": " + e.message + cause
}

type ValidRedeemConfigResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ValidRedeemConfigResponseValidationError) Field() string { return e.field }

func (e ValidRedeemConfigResponseValidationError) Reason() string { return e.reason }

func (e ValidRedeemConfigResponseValidationError) Message() string { return e.message }

func (e ValidRedeemConfigResponseValidationError) Cause() error { return e.cause }

func (e ValidRedeemConfigResponseValidationError) ErrorName() string {
	return "ValidRedeemConfigResponseValidationError"
}

func (e ValidRedeemConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ValidRedeemConfigResponse." + e.field + ": " + e.message + cause
}

type ValidRedeemConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ValidRedeemConfigValidationError) Field() string { return e.field }

func (e ValidRedeemConfigValidationError) Reason() string { return e.reason }

func (e ValidRedeemConfigValidationError) Message() string { return e.message }

func (e ValidRedeemConfigValidationError) Cause() error { return e.cause }

func (e ValidRedeemConfigValidationError) ErrorName() string {
	return "ValidRedeemConfigValidationError"
}

func (e ValidRedeemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ValidRedeemConfig." + e.field + ": " + e.message + cause
}

type PreAwardTemplateRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardTemplateRequestValidationError) Field() string { return e.field }

func (e PreAwardTemplateRequestValidationError) Reason() string { return e.reason }

func (e PreAwardTemplateRequestValidationError) Message() string { return e.message }

func (e PreAwardTemplateRequestValidationError) Cause() error { return e.cause }

func (e PreAwardTemplateRequestValidationError) ErrorName() string {
	return "PreAwardTemplateRequestValidationError"
}

func (e PreAwardTemplateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAwardTemplateRequest." + e.field + ": " + e.message + cause
}

type PreAwardTemplateResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardTemplateResponseValidationError) Field() string { return e.field }

func (e PreAwardTemplateResponseValidationError) Reason() string { return e.reason }

func (e PreAwardTemplateResponseValidationError) Message() string { return e.message }

func (e PreAwardTemplateResponseValidationError) Cause() error { return e.cause }

func (e PreAwardTemplateResponseValidationError) ErrorName() string {
	return "PreAwardTemplateResponseValidationError"
}

func (e PreAwardTemplateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAwardTemplateResponse." + e.field + ": " + e.message + cause
}

type PreAwardTemplateValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardTemplateValidationError) Field() string { return e.field }

func (e PreAwardTemplateValidationError) Reason() string { return e.reason }

func (e PreAwardTemplateValidationError) Message() string { return e.message }

func (e PreAwardTemplateValidationError) Cause() error { return e.cause }

func (e PreAwardTemplateValidationError) ErrorName() string { return "PreAwardTemplateValidationError" }

func (e PreAwardTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAwardTemplate." + e.field + ": " + e.message + cause
}
