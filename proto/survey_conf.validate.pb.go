// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_conf.proto

package proto

func (x *SurveyListRequest) Validate() error {
	return nil
}

func (x *SurveyListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SurveyPreviewReq) Validate() error {
	return nil
}

func (x *SurveyRecordConfDetailsReq) Validate() error {
	return nil
}

func (x *SurveyRecordConfDetailsRes) Validate() error {
	return nil
}

func (x *SyncSurveyRequest) Validate() error {
	return nil
}

func (x *SyncSurveyResponse) Validate() error {
	return nil
}

func (x *SyncSurveyResponseData) Validate() error {
	return nil
}

func (x *ImpSurveyRequest) Validate() error {
	if len(x.GetClientid()) == 0 {
		return ImpSurveyRequestValidationError{
			field:   "Clientid",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveySetStatusRequest) Validate() error {
	return nil
}

func (x *SurveyDelRequest) Validate() error {
	if len(x.GetDelList()) == 0 {
		return SurveyDelRequestValidationError{
			field:   "DelList",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *SurveyRequest) Validate() error {
	return nil
}

func (x *SurveyResponse) Validate() error {
	return nil
}

func (x *Survey) Validate() error {
	if v, ok := interface{}(x.GetQuestionStatisticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyValidationError{
				field:   "QuestionStatisticsData",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	for _, item := range x.GetWebPathList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyValidationError{
					field:   "WebPathList",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *QuestionStatisticsData) Validate() error {
	return nil
}

func (x *SurveyRecycleListRes) Validate() error {
	return nil
}

func (x *RecoverSurveyRecycleReq) Validate() error {
	return nil
}

func (x *Setting) Validate() error {
	if v, ok := interface{}(x.GetBaseRuleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "BaseRuleConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetGiftConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "GiftConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAnswerLimitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "AnswerLimitConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMaterialsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "MaterialsConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetFooterConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "FooterConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetSourceConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingValidationError{
				field:   "SourceConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *WebSetting) Validate() error {
	if v, ok := interface{}(x.GetMaterialsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebSettingValidationError{
				field:   "MaterialsConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetFooterConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebSettingValidationError{
				field:   "FooterConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetSourceConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebSettingValidationError{
				field:   "SourceConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *BaseRuleConfig) Validate() error {
	if v, ok := interface{}(x.GetTimeLimitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseRuleConfigValidationError{
				field:   "TimeLimitConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAnswerTimesConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseRuleConfigValidationError{
				field:   "AnswerTimesConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *TimeLimitConfig) Validate() error {
	return nil
}

func (x *AnswerTimesConfig) Validate() error {
	return nil
}

func (x *GiftConfig) Validate() error {
	if v, ok := interface{}(x.GetPreAwardConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GiftConfigValidationError{
				field:   "PreAwardConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetRedeemConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GiftConfigValidationError{
				field:   "RedeemConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *RedeemConfig) Validate() error {
	return nil
}

func (x *PreAwardConfig) Validate() error {
	return nil
}

func (x *AnswerLimitConfig) Validate() error {
	return nil
}

func (x *MaterialsConfig) Validate() error {
	return nil
}

func (x *FooterConfig) Validate() error {
	return nil
}

func (x *SourceConfig) Validate() error {
	for _, item := range x.GetAgreements() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SourceConfigValidationError{
					field:   "Agreements",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *Agreement) Validate() error {
	return nil
}

func (x *Schema) Validate() error {
	for _, item := range x.GetComponentsMap() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SchemaValidationError{
					field:   "ComponentsMap",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	for _, item := range x.GetComponentsTree() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SchemaValidationError{
					field:   "ComponentsTree",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	if v, ok := interface{}(x.GetI18N()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:   "I18N",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:   "Config",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SchemaValidationError{
				field:   "Meta",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *I18N) Validate() error {
	return nil
}

func (x *ComponentMap) Validate() error {
	return nil
}

func (x *MetaTree) Validate() error {
	return nil
}

func (x *SurveySettings) Validate() error {
	return nil
}

func (x *SkinConfig) Validate() error {
	return nil
}

func (x *SettingsProps) Validate() error {
	if v, ok := interface{}(x.GetBaseRuleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsPropsValidationError{
				field:   "BaseRuleConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetSkinConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsPropsValidationError{
				field:   "SkinConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetMaterialsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsPropsValidationError{
				field:   "MaterialsConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetAnswerLimitConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsPropsValidationError{
				field:   "AnswerLimitConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetGiftConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SettingsPropsValidationError{
				field:   "GiftConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *PropsTree) Validate() error {
	if v, ok := interface{}(x.GetSurveySettings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PropsTreeValidationError{
				field:   "SurveySettings",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetSettingsProps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PropsTreeValidationError{
				field:   "SettingsProps",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ComponentTree) Validate() error {
	if v, ok := interface{}(x.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComponentTreeValidationError{
				field:   "Meta",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	for _, item := range x.GetChildren() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentTreeValidationError{
					field:   "Children",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *Config) Validate() error {
	if v, ok := interface{}(x.GetLayout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfigValidationError{
				field:   "Layout",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Meta) Validate() error {
	return nil
}

func (x *Layout) Validate() error {
	if v, ok := interface{}(x.GetProps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LayoutValidationError{
				field:   "Props",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *PropsLayout) Validate() error {
	return nil
}

func (x *UpdateSurveyRequest) Validate() error {
	if x.GetId() < 1 {
		return UpdateSurveyRequestValidationError{
			field:   "Id",
			reason:  "minimum",
			message: "value must be greater than or equal to 1",
		}
	}
	if len(x.GetName()) == 0 {
		return UpdateSurveyRequestValidationError{
			field:   "Name",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetSchema()) == 0 {
		return UpdateSurveyRequestValidationError{
			field:   "Schema",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetLanguages()) == 0 {
		return UpdateSurveyRequestValidationError{
			field:   "Languages",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

func (x *UpdateSurveyResponse) Validate() error {
	return nil
}

func (x *SurveyDetailRequest) Validate() error {
	return nil
}

func (x *SurveyDetailResponse) Validate() error {
	for _, item := range x.GetQuestionList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyDetailResponseValidationError{
					field:   "QuestionList",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	if v, ok := interface{}(x.GetSurveyConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyDetailResponseValidationError{
				field:   "SurveyConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionStatisticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyDetailResponseValidationError{
				field:   "QuestionStatisticsData",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *SurveyConfig) Validate() error {
	for _, item := range x.GetWebPathList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyConfigValidationError{
					field:   "WebPathList",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionStatisticsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyConfigValidationError{
				field:   "QuestionStatisticsData",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *WebPath) Validate() error {
	return nil
}

func (x *QuestionBaseConfig) Validate() error {
	if v, ok := interface{}(x.GetQuestionTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionBaseConfigValidationError{
				field:   "QuestionTitle",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionDesc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionBaseConfigValidationError{
				field:   "QuestionDesc",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionTip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionBaseConfigValidationError{
				field:   "QuestionTip",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Title) Validate() error {
	return nil
}

func (x *Desc) Validate() error {
	return nil
}

func (x *Tip) Validate() error {
	return nil
}

func (x *ConfigProps) Validate() error {
	return nil
}

func (x *QuestionLogicalConfig) Validate() error {
	if v, ok := interface{}(x.GetVisibleSwitch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionLogicalConfigValidationError{
				field:   "VisibleSwitch",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetDisplayLogic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionLogicalConfigValidationError{
				field:   "DisplayLogic",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *VisibleSwitch) Validate() error {
	return nil
}

func (x *DisplayLogic) Validate() error {
	for _, item := range x.GetRules() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DisplayLogicValidationError{
					field:   "Rules",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *RelatedContentConfig) Validate() error {
	return nil
}

func (x *Rule) Validate() error {
	if v, ok := interface{}(x.GetRelatedContentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleValidationError{
				field:   "RelatedContentConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *QuestionComponentConfig) Validate() error {
	if v, ok := interface{}(x.GetHeaderContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionComponentConfigValidationError{
				field:   "HeaderContent",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetFooterContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionComponentConfigValidationError{
				field:   "FooterContent",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *HeaderContent) Validate() error {
	return nil
}

func (x *FooterContent) Validate() error {
	return nil
}

func (x *QuestionSelectConfig) Validate() error {
	for _, item := range x.GetSelectOptions() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionSelectConfigValidationError{
					field:   "SelectOptions",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *SelectOption) Validate() error {
	return nil
}

func (x *QuestionList) Validate() error {
	if v, ok := interface{}(x.GetQuestionBaseConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "QuestionBaseConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetConfigProps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "ConfigProps",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionLogicalConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "QuestionLogicalConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "QuestionComponentConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetQuestionSelectConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "QuestionSelectConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetRequestConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionListValidationError{
				field:   "RequestConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *RequestConfig) Validate() error {
	if v, ok := interface{}(x.GetConfigProps()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RequestConfigValidationError{
				field:   "ConfigProps",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *QuestionConfig) Validate() error {
	if v, ok := interface{}(x.GetRequestConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuestionConfigValidationError{
				field:   "RequestConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *PublishSurveyRequest) Validate() error {
	return nil
}

func (x *PublishSurveyResponse) Validate() error {
	return nil
}

func (x *ShowSurveyRequest) Validate() error {
	return nil
}

func (x *ShowSurveyResponse) Validate() error {
	if v, ok := interface{}(x.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShowSurveyResponseValidationError{
				field:   "Data",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *SurveyInputMethodListRequest) Validate() error {
	if v, ok := interface{}(x.GetRequestConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SurveyInputMethodListRequestValidationError{
				field:   "RequestConfig",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *SurveyInputMethodListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SurveyInputMethodListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *InputMethodListResponseData) Validate() error {
	return nil
}

func (x *OuterQuestionStatisticsData) Validate() error {
	for _, item := range x.GetDetail() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OuterQuestionStatisticsDataValidationError{
					field:   "Detail",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *Detail) Validate() error {
	for _, item := range x.GetDetail() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailValidationError{
					field:   "Detail",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *OptionDetail) Validate() error {
	if v, ok := interface{}(x.GetLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OptionDetailValidationError{
				field:   "Label",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	if v, ok := interface{}(x.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OptionDetailValidationError{
				field:   "Image",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Label) Validate() error {
	return nil
}

func (x *Image) Validate() error {
	return nil
}

func (x *SurveyOverwriteSendReq) Validate() error {
	return nil
}

func (x *SurveyOverwriteSyncReq) Validate() error {
	if len(x.GetSurvey()) == 0 {
		return SurveyOverwriteSyncReqValidationError{
			field:   "Survey",
			reason:  "required",
			message: "value is required",
		}
	}
	return nil
}

type SurveyListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyListRequestValidationError) Field() string { return e.field }

func (e SurveyListRequestValidationError) Reason() string { return e.reason }

func (e SurveyListRequestValidationError) Message() string { return e.message }

func (e SurveyListRequestValidationError) Cause() error { return e.cause }

func (e SurveyListRequestValidationError) ErrorName() string {
	return "SurveyListRequestValidationError"
}

func (e SurveyListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyListRequest." + e.field + ": " + e.message + cause
}

type SurveyListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyListResponseValidationError) Field() string { return e.field }

func (e SurveyListResponseValidationError) Reason() string { return e.reason }

func (e SurveyListResponseValidationError) Message() string { return e.message }

func (e SurveyListResponseValidationError) Cause() error { return e.cause }

func (e SurveyListResponseValidationError) ErrorName() string {
	return "SurveyListResponseValidationError"
}

func (e SurveyListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyListResponse." + e.field + ": " + e.message + cause
}

type SurveyPreviewReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyPreviewReqValidationError) Field() string { return e.field }

func (e SurveyPreviewReqValidationError) Reason() string { return e.reason }

func (e SurveyPreviewReqValidationError) Message() string { return e.message }

func (e SurveyPreviewReqValidationError) Cause() error { return e.cause }

func (e SurveyPreviewReqValidationError) ErrorName() string { return "SurveyPreviewReqValidationError" }

func (e SurveyPreviewReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyPreviewReq." + e.field + ": " + e.message + cause
}

type SurveyRecordConfDetailsReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordConfDetailsReqValidationError) Field() string { return e.field }

func (e SurveyRecordConfDetailsReqValidationError) Reason() string { return e.reason }

func (e SurveyRecordConfDetailsReqValidationError) Message() string { return e.message }

func (e SurveyRecordConfDetailsReqValidationError) Cause() error { return e.cause }

func (e SurveyRecordConfDetailsReqValidationError) ErrorName() string {
	return "SurveyRecordConfDetailsReqValidationError"
}

func (e SurveyRecordConfDetailsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordConfDetailsReq." + e.field + ": " + e.message + cause
}

type SurveyRecordConfDetailsResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecordConfDetailsResValidationError) Field() string { return e.field }

func (e SurveyRecordConfDetailsResValidationError) Reason() string { return e.reason }

func (e SurveyRecordConfDetailsResValidationError) Message() string { return e.message }

func (e SurveyRecordConfDetailsResValidationError) Cause() error { return e.cause }

func (e SurveyRecordConfDetailsResValidationError) ErrorName() string {
	return "SurveyRecordConfDetailsResValidationError"
}

func (e SurveyRecordConfDetailsResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecordConfDetailsRes." + e.field + ": " + e.message + cause
}

type SyncSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SyncSurveyRequestValidationError) Field() string { return e.field }

func (e SyncSurveyRequestValidationError) Reason() string { return e.reason }

func (e SyncSurveyRequestValidationError) Message() string { return e.message }

func (e SyncSurveyRequestValidationError) Cause() error { return e.cause }

func (e SyncSurveyRequestValidationError) ErrorName() string {
	return "SyncSurveyRequestValidationError"
}

func (e SyncSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SyncSurveyRequest." + e.field + ": " + e.message + cause
}

type SyncSurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SyncSurveyResponseValidationError) Field() string { return e.field }

func (e SyncSurveyResponseValidationError) Reason() string { return e.reason }

func (e SyncSurveyResponseValidationError) Message() string { return e.message }

func (e SyncSurveyResponseValidationError) Cause() error { return e.cause }

func (e SyncSurveyResponseValidationError) ErrorName() string {
	return "SyncSurveyResponseValidationError"
}

func (e SyncSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SyncSurveyResponse." + e.field + ": " + e.message + cause
}

type SyncSurveyResponseDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SyncSurveyResponseDataValidationError) Field() string { return e.field }

func (e SyncSurveyResponseDataValidationError) Reason() string { return e.reason }

func (e SyncSurveyResponseDataValidationError) Message() string { return e.message }

func (e SyncSurveyResponseDataValidationError) Cause() error { return e.cause }

func (e SyncSurveyResponseDataValidationError) ErrorName() string {
	return "SyncSurveyResponseDataValidationError"
}

func (e SyncSurveyResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SyncSurveyResponseData." + e.field + ": " + e.message + cause
}

type ImpSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ImpSurveyRequestValidationError) Field() string { return e.field }

func (e ImpSurveyRequestValidationError) Reason() string { return e.reason }

func (e ImpSurveyRequestValidationError) Message() string { return e.message }

func (e ImpSurveyRequestValidationError) Cause() error { return e.cause }

func (e ImpSurveyRequestValidationError) ErrorName() string { return "ImpSurveyRequestValidationError" }

func (e ImpSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ImpSurveyRequest." + e.field + ": " + e.message + cause
}

type SurveySetStatusRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveySetStatusRequestValidationError) Field() string { return e.field }

func (e SurveySetStatusRequestValidationError) Reason() string { return e.reason }

func (e SurveySetStatusRequestValidationError) Message() string { return e.message }

func (e SurveySetStatusRequestValidationError) Cause() error { return e.cause }

func (e SurveySetStatusRequestValidationError) ErrorName() string {
	return "SurveySetStatusRequestValidationError"
}

func (e SurveySetStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveySetStatusRequest." + e.field + ": " + e.message + cause
}

type SurveyDelRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyDelRequestValidationError) Field() string { return e.field }

func (e SurveyDelRequestValidationError) Reason() string { return e.reason }

func (e SurveyDelRequestValidationError) Message() string { return e.message }

func (e SurveyDelRequestValidationError) Cause() error { return e.cause }

func (e SurveyDelRequestValidationError) ErrorName() string { return "SurveyDelRequestValidationError" }

func (e SurveyDelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyDelRequest." + e.field + ": " + e.message + cause
}

type SurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRequestValidationError) Field() string { return e.field }

func (e SurveyRequestValidationError) Reason() string { return e.reason }

func (e SurveyRequestValidationError) Message() string { return e.message }

func (e SurveyRequestValidationError) Cause() error { return e.cause }

func (e SurveyRequestValidationError) ErrorName() string { return "SurveyRequestValidationError" }

func (e SurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRequest." + e.field + ": " + e.message + cause
}

type SurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyResponseValidationError) Field() string { return e.field }

func (e SurveyResponseValidationError) Reason() string { return e.reason }

func (e SurveyResponseValidationError) Message() string { return e.message }

func (e SurveyResponseValidationError) Cause() error { return e.cause }

func (e SurveyResponseValidationError) ErrorName() string { return "SurveyResponseValidationError" }

func (e SurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyResponse." + e.field + ": " + e.message + cause
}

type SurveyValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyValidationError) Field() string { return e.field }

func (e SurveyValidationError) Reason() string { return e.reason }

func (e SurveyValidationError) Message() string { return e.message }

func (e SurveyValidationError) Cause() error { return e.cause }

func (e SurveyValidationError) ErrorName() string { return "SurveyValidationError" }

func (e SurveyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Survey." + e.field + ": " + e.message + cause
}

type QuestionStatisticsDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionStatisticsDataValidationError) Field() string { return e.field }

func (e QuestionStatisticsDataValidationError) Reason() string { return e.reason }

func (e QuestionStatisticsDataValidationError) Message() string { return e.message }

func (e QuestionStatisticsDataValidationError) Cause() error { return e.cause }

func (e QuestionStatisticsDataValidationError) ErrorName() string {
	return "QuestionStatisticsDataValidationError"
}

func (e QuestionStatisticsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionStatisticsData." + e.field + ": " + e.message + cause
}

type SurveyRecycleListResValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyRecycleListResValidationError) Field() string { return e.field }

func (e SurveyRecycleListResValidationError) Reason() string { return e.reason }

func (e SurveyRecycleListResValidationError) Message() string { return e.message }

func (e SurveyRecycleListResValidationError) Cause() error { return e.cause }

func (e SurveyRecycleListResValidationError) ErrorName() string {
	return "SurveyRecycleListResValidationError"
}

func (e SurveyRecycleListResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyRecycleListRes." + e.field + ": " + e.message + cause
}

type RecoverSurveyRecycleReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RecoverSurveyRecycleReqValidationError) Field() string { return e.field }

func (e RecoverSurveyRecycleReqValidationError) Reason() string { return e.reason }

func (e RecoverSurveyRecycleReqValidationError) Message() string { return e.message }

func (e RecoverSurveyRecycleReqValidationError) Cause() error { return e.cause }

func (e RecoverSurveyRecycleReqValidationError) ErrorName() string {
	return "RecoverSurveyRecycleReqValidationError"
}

func (e RecoverSurveyRecycleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RecoverSurveyRecycleReq." + e.field + ": " + e.message + cause
}

type SettingValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SettingValidationError) Field() string { return e.field }

func (e SettingValidationError) Reason() string { return e.reason }

func (e SettingValidationError) Message() string { return e.message }

func (e SettingValidationError) Cause() error { return e.cause }

func (e SettingValidationError) ErrorName() string { return "SettingValidationError" }

func (e SettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Setting." + e.field + ": " + e.message + cause
}

type WebSettingValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e WebSettingValidationError) Field() string { return e.field }

func (e WebSettingValidationError) Reason() string { return e.reason }

func (e WebSettingValidationError) Message() string { return e.message }

func (e WebSettingValidationError) Cause() error { return e.cause }

func (e WebSettingValidationError) ErrorName() string { return "WebSettingValidationError" }

func (e WebSettingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid WebSetting." + e.field + ": " + e.message + cause
}

type BaseRuleConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BaseRuleConfigValidationError) Field() string { return e.field }

func (e BaseRuleConfigValidationError) Reason() string { return e.reason }

func (e BaseRuleConfigValidationError) Message() string { return e.message }

func (e BaseRuleConfigValidationError) Cause() error { return e.cause }

func (e BaseRuleConfigValidationError) ErrorName() string { return "BaseRuleConfigValidationError" }

func (e BaseRuleConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid BaseRuleConfig." + e.field + ": " + e.message + cause
}

type TimeLimitConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e TimeLimitConfigValidationError) Field() string { return e.field }

func (e TimeLimitConfigValidationError) Reason() string { return e.reason }

func (e TimeLimitConfigValidationError) Message() string { return e.message }

func (e TimeLimitConfigValidationError) Cause() error { return e.cause }

func (e TimeLimitConfigValidationError) ErrorName() string { return "TimeLimitConfigValidationError" }

func (e TimeLimitConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid TimeLimitConfig." + e.field + ": " + e.message + cause
}

type AnswerTimesConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AnswerTimesConfigValidationError) Field() string { return e.field }

func (e AnswerTimesConfigValidationError) Reason() string { return e.reason }

func (e AnswerTimesConfigValidationError) Message() string { return e.message }

func (e AnswerTimesConfigValidationError) Cause() error { return e.cause }

func (e AnswerTimesConfigValidationError) ErrorName() string {
	return "AnswerTimesConfigValidationError"
}

func (e AnswerTimesConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AnswerTimesConfig." + e.field + ": " + e.message + cause
}

type GiftConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GiftConfigValidationError) Field() string { return e.field }

func (e GiftConfigValidationError) Reason() string { return e.reason }

func (e GiftConfigValidationError) Message() string { return e.message }

func (e GiftConfigValidationError) Cause() error { return e.cause }

func (e GiftConfigValidationError) ErrorName() string { return "GiftConfigValidationError" }

func (e GiftConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GiftConfig." + e.field + ": " + e.message + cause
}

type RedeemConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RedeemConfigValidationError) Field() string { return e.field }

func (e RedeemConfigValidationError) Reason() string { return e.reason }

func (e RedeemConfigValidationError) Message() string { return e.message }

func (e RedeemConfigValidationError) Cause() error { return e.cause }

func (e RedeemConfigValidationError) ErrorName() string { return "RedeemConfigValidationError" }

func (e RedeemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RedeemConfig." + e.field + ": " + e.message + cause
}

type PreAwardConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PreAwardConfigValidationError) Field() string { return e.field }

func (e PreAwardConfigValidationError) Reason() string { return e.reason }

func (e PreAwardConfigValidationError) Message() string { return e.message }

func (e PreAwardConfigValidationError) Cause() error { return e.cause }

func (e PreAwardConfigValidationError) ErrorName() string { return "PreAwardConfigValidationError" }

func (e PreAwardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PreAwardConfig." + e.field + ": " + e.message + cause
}

type AnswerLimitConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AnswerLimitConfigValidationError) Field() string { return e.field }

func (e AnswerLimitConfigValidationError) Reason() string { return e.reason }

func (e AnswerLimitConfigValidationError) Message() string { return e.message }

func (e AnswerLimitConfigValidationError) Cause() error { return e.cause }

func (e AnswerLimitConfigValidationError) ErrorName() string {
	return "AnswerLimitConfigValidationError"
}

func (e AnswerLimitConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid AnswerLimitConfig." + e.field + ": " + e.message + cause
}

type MaterialsConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e MaterialsConfigValidationError) Field() string { return e.field }

func (e MaterialsConfigValidationError) Reason() string { return e.reason }

func (e MaterialsConfigValidationError) Message() string { return e.message }

func (e MaterialsConfigValidationError) Cause() error { return e.cause }

func (e MaterialsConfigValidationError) ErrorName() string { return "MaterialsConfigValidationError" }

func (e MaterialsConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid MaterialsConfig." + e.field + ": " + e.message + cause
}

type FooterConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FooterConfigValidationError) Field() string { return e.field }

func (e FooterConfigValidationError) Reason() string { return e.reason }

func (e FooterConfigValidationError) Message() string { return e.message }

func (e FooterConfigValidationError) Cause() error { return e.cause }

func (e FooterConfigValidationError) ErrorName() string { return "FooterConfigValidationError" }

func (e FooterConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FooterConfig." + e.field + ": " + e.message + cause
}

type SourceConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SourceConfigValidationError) Field() string { return e.field }

func (e SourceConfigValidationError) Reason() string { return e.reason }

func (e SourceConfigValidationError) Message() string { return e.message }

func (e SourceConfigValidationError) Cause() error { return e.cause }

func (e SourceConfigValidationError) ErrorName() string { return "SourceConfigValidationError" }

func (e SourceConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SourceConfig." + e.field + ": " + e.message + cause
}

type AgreementValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e AgreementValidationError) Field() string { return e.field }

func (e AgreementValidationError) Reason() string { return e.reason }

func (e AgreementValidationError) Message() string { return e.message }

func (e AgreementValidationError) Cause() error { return e.cause }

func (e AgreementValidationError) ErrorName() string { return "AgreementValidationError" }

func (e AgreementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Agreement." + e.field + ": " + e.message + cause
}

type SchemaValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SchemaValidationError) Field() string { return e.field }

func (e SchemaValidationError) Reason() string { return e.reason }

func (e SchemaValidationError) Message() string { return e.message }

func (e SchemaValidationError) Cause() error { return e.cause }

func (e SchemaValidationError) ErrorName() string { return "SchemaValidationError" }

func (e SchemaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Schema." + e.field + ": " + e.message + cause
}

type I18NValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e I18NValidationError) Field() string { return e.field }

func (e I18NValidationError) Reason() string { return e.reason }

func (e I18NValidationError) Message() string { return e.message }

func (e I18NValidationError) Cause() error { return e.cause }

func (e I18NValidationError) ErrorName() string { return "I18NValidationError" }

func (e I18NValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid I18N." + e.field + ": " + e.message + cause
}

type ComponentMapValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ComponentMapValidationError) Field() string { return e.field }

func (e ComponentMapValidationError) Reason() string { return e.reason }

func (e ComponentMapValidationError) Message() string { return e.message }

func (e ComponentMapValidationError) Cause() error { return e.cause }

func (e ComponentMapValidationError) ErrorName() string { return "ComponentMapValidationError" }

func (e ComponentMapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ComponentMap." + e.field + ": " + e.message + cause
}

type MetaTreeValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e MetaTreeValidationError) Field() string { return e.field }

func (e MetaTreeValidationError) Reason() string { return e.reason }

func (e MetaTreeValidationError) Message() string { return e.message }

func (e MetaTreeValidationError) Cause() error { return e.cause }

func (e MetaTreeValidationError) ErrorName() string { return "MetaTreeValidationError" }

func (e MetaTreeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid MetaTree." + e.field + ": " + e.message + cause
}

type SurveySettingsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveySettingsValidationError) Field() string { return e.field }

func (e SurveySettingsValidationError) Reason() string { return e.reason }

func (e SurveySettingsValidationError) Message() string { return e.message }

func (e SurveySettingsValidationError) Cause() error { return e.cause }

func (e SurveySettingsValidationError) ErrorName() string { return "SurveySettingsValidationError" }

func (e SurveySettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveySettings." + e.field + ": " + e.message + cause
}

type SkinConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SkinConfigValidationError) Field() string { return e.field }

func (e SkinConfigValidationError) Reason() string { return e.reason }

func (e SkinConfigValidationError) Message() string { return e.message }

func (e SkinConfigValidationError) Cause() error { return e.cause }

func (e SkinConfigValidationError) ErrorName() string { return "SkinConfigValidationError" }

func (e SkinConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SkinConfig." + e.field + ": " + e.message + cause
}

type SettingsPropsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SettingsPropsValidationError) Field() string { return e.field }

func (e SettingsPropsValidationError) Reason() string { return e.reason }

func (e SettingsPropsValidationError) Message() string { return e.message }

func (e SettingsPropsValidationError) Cause() error { return e.cause }

func (e SettingsPropsValidationError) ErrorName() string { return "SettingsPropsValidationError" }

func (e SettingsPropsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SettingsProps." + e.field + ": " + e.message + cause
}

type PropsTreeValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PropsTreeValidationError) Field() string { return e.field }

func (e PropsTreeValidationError) Reason() string { return e.reason }

func (e PropsTreeValidationError) Message() string { return e.message }

func (e PropsTreeValidationError) Cause() error { return e.cause }

func (e PropsTreeValidationError) ErrorName() string { return "PropsTreeValidationError" }

func (e PropsTreeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PropsTree." + e.field + ": " + e.message + cause
}

type ComponentTreeValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ComponentTreeValidationError) Field() string { return e.field }

func (e ComponentTreeValidationError) Reason() string { return e.reason }

func (e ComponentTreeValidationError) Message() string { return e.message }

func (e ComponentTreeValidationError) Cause() error { return e.cause }

func (e ComponentTreeValidationError) ErrorName() string { return "ComponentTreeValidationError" }

func (e ComponentTreeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ComponentTree." + e.field + ": " + e.message + cause
}

type ConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ConfigValidationError) Field() string { return e.field }

func (e ConfigValidationError) Reason() string { return e.reason }

func (e ConfigValidationError) Message() string { return e.message }

func (e ConfigValidationError) Cause() error { return e.cause }

func (e ConfigValidationError) ErrorName() string { return "ConfigValidationError" }

func (e ConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Config." + e.field + ": " + e.message + cause
}

type MetaValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e MetaValidationError) Field() string { return e.field }

func (e MetaValidationError) Reason() string { return e.reason }

func (e MetaValidationError) Message() string { return e.message }

func (e MetaValidationError) Cause() error { return e.cause }

func (e MetaValidationError) ErrorName() string { return "MetaValidationError" }

func (e MetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Meta." + e.field + ": " + e.message + cause
}

type LayoutValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e LayoutValidationError) Field() string { return e.field }

func (e LayoutValidationError) Reason() string { return e.reason }

func (e LayoutValidationError) Message() string { return e.message }

func (e LayoutValidationError) Cause() error { return e.cause }

func (e LayoutValidationError) ErrorName() string { return "LayoutValidationError" }

func (e LayoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Layout." + e.field + ": " + e.message + cause
}

type PropsLayoutValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PropsLayoutValidationError) Field() string { return e.field }

func (e PropsLayoutValidationError) Reason() string { return e.reason }

func (e PropsLayoutValidationError) Message() string { return e.message }

func (e PropsLayoutValidationError) Cause() error { return e.cause }

func (e PropsLayoutValidationError) ErrorName() string { return "PropsLayoutValidationError" }

func (e PropsLayoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PropsLayout." + e.field + ": " + e.message + cause
}

type UpdateSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UpdateSurveyRequestValidationError) Field() string { return e.field }

func (e UpdateSurveyRequestValidationError) Reason() string { return e.reason }

func (e UpdateSurveyRequestValidationError) Message() string { return e.message }

func (e UpdateSurveyRequestValidationError) Cause() error { return e.cause }

func (e UpdateSurveyRequestValidationError) ErrorName() string {
	return "UpdateSurveyRequestValidationError"
}

func (e UpdateSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UpdateSurveyRequest." + e.field + ": " + e.message + cause
}

type UpdateSurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e UpdateSurveyResponseValidationError) Field() string { return e.field }

func (e UpdateSurveyResponseValidationError) Reason() string { return e.reason }

func (e UpdateSurveyResponseValidationError) Message() string { return e.message }

func (e UpdateSurveyResponseValidationError) Cause() error { return e.cause }

func (e UpdateSurveyResponseValidationError) ErrorName() string {
	return "UpdateSurveyResponseValidationError"
}

func (e UpdateSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid UpdateSurveyResponse." + e.field + ": " + e.message + cause
}

type SurveyDetailRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyDetailRequestValidationError) Field() string { return e.field }

func (e SurveyDetailRequestValidationError) Reason() string { return e.reason }

func (e SurveyDetailRequestValidationError) Message() string { return e.message }

func (e SurveyDetailRequestValidationError) Cause() error { return e.cause }

func (e SurveyDetailRequestValidationError) ErrorName() string {
	return "SurveyDetailRequestValidationError"
}

func (e SurveyDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyDetailRequest." + e.field + ": " + e.message + cause
}

type SurveyDetailResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyDetailResponseValidationError) Field() string { return e.field }

func (e SurveyDetailResponseValidationError) Reason() string { return e.reason }

func (e SurveyDetailResponseValidationError) Message() string { return e.message }

func (e SurveyDetailResponseValidationError) Cause() error { return e.cause }

func (e SurveyDetailResponseValidationError) ErrorName() string {
	return "SurveyDetailResponseValidationError"
}

func (e SurveyDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyDetailResponse." + e.field + ": " + e.message + cause
}

type SurveyConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyConfigValidationError) Field() string { return e.field }

func (e SurveyConfigValidationError) Reason() string { return e.reason }

func (e SurveyConfigValidationError) Message() string { return e.message }

func (e SurveyConfigValidationError) Cause() error { return e.cause }

func (e SurveyConfigValidationError) ErrorName() string { return "SurveyConfigValidationError" }

func (e SurveyConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyConfig." + e.field + ": " + e.message + cause
}

type WebPathValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e WebPathValidationError) Field() string { return e.field }

func (e WebPathValidationError) Reason() string { return e.reason }

func (e WebPathValidationError) Message() string { return e.message }

func (e WebPathValidationError) Cause() error { return e.cause }

func (e WebPathValidationError) ErrorName() string { return "WebPathValidationError" }

func (e WebPathValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid WebPath." + e.field + ": " + e.message + cause
}

type QuestionBaseConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionBaseConfigValidationError) Field() string { return e.field }

func (e QuestionBaseConfigValidationError) Reason() string { return e.reason }

func (e QuestionBaseConfigValidationError) Message() string { return e.message }

func (e QuestionBaseConfigValidationError) Cause() error { return e.cause }

func (e QuestionBaseConfigValidationError) ErrorName() string {
	return "QuestionBaseConfigValidationError"
}

func (e QuestionBaseConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionBaseConfig." + e.field + ": " + e.message + cause
}

type TitleValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e TitleValidationError) Field() string { return e.field }

func (e TitleValidationError) Reason() string { return e.reason }

func (e TitleValidationError) Message() string { return e.message }

func (e TitleValidationError) Cause() error { return e.cause }

func (e TitleValidationError) ErrorName() string { return "TitleValidationError" }

func (e TitleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Title." + e.field + ": " + e.message + cause
}

type DescValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DescValidationError) Field() string { return e.field }

func (e DescValidationError) Reason() string { return e.reason }

func (e DescValidationError) Message() string { return e.message }

func (e DescValidationError) Cause() error { return e.cause }

func (e DescValidationError) ErrorName() string { return "DescValidationError" }

func (e DescValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Desc." + e.field + ": " + e.message + cause
}

type TipValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e TipValidationError) Field() string { return e.field }

func (e TipValidationError) Reason() string { return e.reason }

func (e TipValidationError) Message() string { return e.message }

func (e TipValidationError) Cause() error { return e.cause }

func (e TipValidationError) ErrorName() string { return "TipValidationError" }

func (e TipValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Tip." + e.field + ": " + e.message + cause
}

type ConfigPropsValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ConfigPropsValidationError) Field() string { return e.field }

func (e ConfigPropsValidationError) Reason() string { return e.reason }

func (e ConfigPropsValidationError) Message() string { return e.message }

func (e ConfigPropsValidationError) Cause() error { return e.cause }

func (e ConfigPropsValidationError) ErrorName() string { return "ConfigPropsValidationError" }

func (e ConfigPropsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ConfigProps." + e.field + ": " + e.message + cause
}

type QuestionLogicalConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionLogicalConfigValidationError) Field() string { return e.field }

func (e QuestionLogicalConfigValidationError) Reason() string { return e.reason }

func (e QuestionLogicalConfigValidationError) Message() string { return e.message }

func (e QuestionLogicalConfigValidationError) Cause() error { return e.cause }

func (e QuestionLogicalConfigValidationError) ErrorName() string {
	return "QuestionLogicalConfigValidationError"
}

func (e QuestionLogicalConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionLogicalConfig." + e.field + ": " + e.message + cause
}

type VisibleSwitchValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e VisibleSwitchValidationError) Field() string { return e.field }

func (e VisibleSwitchValidationError) Reason() string { return e.reason }

func (e VisibleSwitchValidationError) Message() string { return e.message }

func (e VisibleSwitchValidationError) Cause() error { return e.cause }

func (e VisibleSwitchValidationError) ErrorName() string { return "VisibleSwitchValidationError" }

func (e VisibleSwitchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid VisibleSwitch." + e.field + ": " + e.message + cause
}

type DisplayLogicValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DisplayLogicValidationError) Field() string { return e.field }

func (e DisplayLogicValidationError) Reason() string { return e.reason }

func (e DisplayLogicValidationError) Message() string { return e.message }

func (e DisplayLogicValidationError) Cause() error { return e.cause }

func (e DisplayLogicValidationError) ErrorName() string { return "DisplayLogicValidationError" }

func (e DisplayLogicValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DisplayLogic." + e.field + ": " + e.message + cause
}

type RelatedContentConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RelatedContentConfigValidationError) Field() string { return e.field }

func (e RelatedContentConfigValidationError) Reason() string { return e.reason }

func (e RelatedContentConfigValidationError) Message() string { return e.message }

func (e RelatedContentConfigValidationError) Cause() error { return e.cause }

func (e RelatedContentConfigValidationError) ErrorName() string {
	return "RelatedContentConfigValidationError"
}

func (e RelatedContentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RelatedContentConfig." + e.field + ": " + e.message + cause
}

type RuleValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RuleValidationError) Field() string { return e.field }

func (e RuleValidationError) Reason() string { return e.reason }

func (e RuleValidationError) Message() string { return e.message }

func (e RuleValidationError) Cause() error { return e.cause }

func (e RuleValidationError) ErrorName() string { return "RuleValidationError" }

func (e RuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Rule." + e.field + ": " + e.message + cause
}

type QuestionComponentConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionComponentConfigValidationError) Field() string { return e.field }

func (e QuestionComponentConfigValidationError) Reason() string { return e.reason }

func (e QuestionComponentConfigValidationError) Message() string { return e.message }

func (e QuestionComponentConfigValidationError) Cause() error { return e.cause }

func (e QuestionComponentConfigValidationError) ErrorName() string {
	return "QuestionComponentConfigValidationError"
}

func (e QuestionComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionComponentConfig." + e.field + ": " + e.message + cause
}

type HeaderContentValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e HeaderContentValidationError) Field() string { return e.field }

func (e HeaderContentValidationError) Reason() string { return e.reason }

func (e HeaderContentValidationError) Message() string { return e.message }

func (e HeaderContentValidationError) Cause() error { return e.cause }

func (e HeaderContentValidationError) ErrorName() string { return "HeaderContentValidationError" }

func (e HeaderContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid HeaderContent." + e.field + ": " + e.message + cause
}

type FooterContentValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e FooterContentValidationError) Field() string { return e.field }

func (e FooterContentValidationError) Reason() string { return e.reason }

func (e FooterContentValidationError) Message() string { return e.message }

func (e FooterContentValidationError) Cause() error { return e.cause }

func (e FooterContentValidationError) ErrorName() string { return "FooterContentValidationError" }

func (e FooterContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid FooterContent." + e.field + ": " + e.message + cause
}

type QuestionSelectConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionSelectConfigValidationError) Field() string { return e.field }

func (e QuestionSelectConfigValidationError) Reason() string { return e.reason }

func (e QuestionSelectConfigValidationError) Message() string { return e.message }

func (e QuestionSelectConfigValidationError) Cause() error { return e.cause }

func (e QuestionSelectConfigValidationError) ErrorName() string {
	return "QuestionSelectConfigValidationError"
}

func (e QuestionSelectConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionSelectConfig." + e.field + ": " + e.message + cause
}

type SelectOptionValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SelectOptionValidationError) Field() string { return e.field }

func (e SelectOptionValidationError) Reason() string { return e.reason }

func (e SelectOptionValidationError) Message() string { return e.message }

func (e SelectOptionValidationError) Cause() error { return e.cause }

func (e SelectOptionValidationError) ErrorName() string { return "SelectOptionValidationError" }

func (e SelectOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SelectOption." + e.field + ": " + e.message + cause
}

type QuestionListValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionListValidationError) Field() string { return e.field }

func (e QuestionListValidationError) Reason() string { return e.reason }

func (e QuestionListValidationError) Message() string { return e.message }

func (e QuestionListValidationError) Cause() error { return e.cause }

func (e QuestionListValidationError) ErrorName() string { return "QuestionListValidationError" }

func (e QuestionListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionList." + e.field + ": " + e.message + cause
}

type RequestConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e RequestConfigValidationError) Field() string { return e.field }

func (e RequestConfigValidationError) Reason() string { return e.reason }

func (e RequestConfigValidationError) Message() string { return e.message }

func (e RequestConfigValidationError) Cause() error { return e.cause }

func (e RequestConfigValidationError) ErrorName() string { return "RequestConfigValidationError" }

func (e RequestConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid RequestConfig." + e.field + ": " + e.message + cause
}

type QuestionConfigValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e QuestionConfigValidationError) Field() string { return e.field }

func (e QuestionConfigValidationError) Reason() string { return e.reason }

func (e QuestionConfigValidationError) Message() string { return e.message }

func (e QuestionConfigValidationError) Cause() error { return e.cause }

func (e QuestionConfigValidationError) ErrorName() string { return "QuestionConfigValidationError" }

func (e QuestionConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid QuestionConfig." + e.field + ": " + e.message + cause
}

type PublishSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PublishSurveyRequestValidationError) Field() string { return e.field }

func (e PublishSurveyRequestValidationError) Reason() string { return e.reason }

func (e PublishSurveyRequestValidationError) Message() string { return e.message }

func (e PublishSurveyRequestValidationError) Cause() error { return e.cause }

func (e PublishSurveyRequestValidationError) ErrorName() string {
	return "PublishSurveyRequestValidationError"
}

func (e PublishSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PublishSurveyRequest." + e.field + ": " + e.message + cause
}

type PublishSurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e PublishSurveyResponseValidationError) Field() string { return e.field }

func (e PublishSurveyResponseValidationError) Reason() string { return e.reason }

func (e PublishSurveyResponseValidationError) Message() string { return e.message }

func (e PublishSurveyResponseValidationError) Cause() error { return e.cause }

func (e PublishSurveyResponseValidationError) ErrorName() string {
	return "PublishSurveyResponseValidationError"
}

func (e PublishSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid PublishSurveyResponse." + e.field + ": " + e.message + cause
}

type ShowSurveyRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShowSurveyRequestValidationError) Field() string { return e.field }

func (e ShowSurveyRequestValidationError) Reason() string { return e.reason }

func (e ShowSurveyRequestValidationError) Message() string { return e.message }

func (e ShowSurveyRequestValidationError) Cause() error { return e.cause }

func (e ShowSurveyRequestValidationError) ErrorName() string {
	return "ShowSurveyRequestValidationError"
}

func (e ShowSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShowSurveyRequest." + e.field + ": " + e.message + cause
}

type ShowSurveyResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShowSurveyResponseValidationError) Field() string { return e.field }

func (e ShowSurveyResponseValidationError) Reason() string { return e.reason }

func (e ShowSurveyResponseValidationError) Message() string { return e.message }

func (e ShowSurveyResponseValidationError) Cause() error { return e.cause }

func (e ShowSurveyResponseValidationError) ErrorName() string {
	return "ShowSurveyResponseValidationError"
}

func (e ShowSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ShowSurveyResponse." + e.field + ": " + e.message + cause
}

type SurveyInputMethodListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyInputMethodListRequestValidationError) Field() string { return e.field }

func (e SurveyInputMethodListRequestValidationError) Reason() string { return e.reason }

func (e SurveyInputMethodListRequestValidationError) Message() string { return e.message }

func (e SurveyInputMethodListRequestValidationError) Cause() error { return e.cause }

func (e SurveyInputMethodListRequestValidationError) ErrorName() string {
	return "SurveyInputMethodListRequestValidationError"
}

func (e SurveyInputMethodListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyInputMethodListRequest." + e.field + ": " + e.message + cause
}

type SurveyInputMethodListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyInputMethodListResponseValidationError) Field() string { return e.field }

func (e SurveyInputMethodListResponseValidationError) Reason() string { return e.reason }

func (e SurveyInputMethodListResponseValidationError) Message() string { return e.message }

func (e SurveyInputMethodListResponseValidationError) Cause() error { return e.cause }

func (e SurveyInputMethodListResponseValidationError) ErrorName() string {
	return "SurveyInputMethodListResponseValidationError"
}

func (e SurveyInputMethodListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyInputMethodListResponse." + e.field + ": " + e.message + cause
}

type InputMethodListResponseDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e InputMethodListResponseDataValidationError) Field() string { return e.field }

func (e InputMethodListResponseDataValidationError) Reason() string { return e.reason }

func (e InputMethodListResponseDataValidationError) Message() string { return e.message }

func (e InputMethodListResponseDataValidationError) Cause() error { return e.cause }

func (e InputMethodListResponseDataValidationError) ErrorName() string {
	return "InputMethodListResponseDataValidationError"
}

func (e InputMethodListResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid InputMethodListResponseData." + e.field + ": " + e.message + cause
}

type OuterQuestionStatisticsDataValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e OuterQuestionStatisticsDataValidationError) Field() string { return e.field }

func (e OuterQuestionStatisticsDataValidationError) Reason() string { return e.reason }

func (e OuterQuestionStatisticsDataValidationError) Message() string { return e.message }

func (e OuterQuestionStatisticsDataValidationError) Cause() error { return e.cause }

func (e OuterQuestionStatisticsDataValidationError) ErrorName() string {
	return "OuterQuestionStatisticsDataValidationError"
}

func (e OuterQuestionStatisticsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid OuterQuestionStatisticsData." + e.field + ": " + e.message + cause
}

type DetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DetailValidationError) Field() string { return e.field }

func (e DetailValidationError) Reason() string { return e.reason }

func (e DetailValidationError) Message() string { return e.message }

func (e DetailValidationError) Cause() error { return e.cause }

func (e DetailValidationError) ErrorName() string { return "DetailValidationError" }

func (e DetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Detail." + e.field + ": " + e.message + cause
}

type OptionDetailValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e OptionDetailValidationError) Field() string { return e.field }

func (e OptionDetailValidationError) Reason() string { return e.reason }

func (e OptionDetailValidationError) Message() string { return e.message }

func (e OptionDetailValidationError) Cause() error { return e.cause }

func (e OptionDetailValidationError) ErrorName() string { return "OptionDetailValidationError" }

func (e OptionDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid OptionDetail." + e.field + ": " + e.message + cause
}

type LabelValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e LabelValidationError) Field() string { return e.field }

func (e LabelValidationError) Reason() string { return e.reason }

func (e LabelValidationError) Message() string { return e.message }

func (e LabelValidationError) Cause() error { return e.cause }

func (e LabelValidationError) ErrorName() string { return "LabelValidationError" }

func (e LabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Label." + e.field + ": " + e.message + cause
}

type ImageValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ImageValidationError) Field() string { return e.field }

func (e ImageValidationError) Reason() string { return e.reason }

func (e ImageValidationError) Message() string { return e.message }

func (e ImageValidationError) Cause() error { return e.cause }

func (e ImageValidationError) ErrorName() string { return "ImageValidationError" }

func (e ImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Image." + e.field + ": " + e.message + cause
}

type SurveyOverwriteSendReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyOverwriteSendReqValidationError) Field() string { return e.field }

func (e SurveyOverwriteSendReqValidationError) Reason() string { return e.reason }

func (e SurveyOverwriteSendReqValidationError) Message() string { return e.message }

func (e SurveyOverwriteSendReqValidationError) Cause() error { return e.cause }

func (e SurveyOverwriteSendReqValidationError) ErrorName() string {
	return "SurveyOverwriteSendReqValidationError"
}

func (e SurveyOverwriteSendReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyOverwriteSendReq." + e.field + ": " + e.message + cause
}

type SurveyOverwriteSyncReqValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e SurveyOverwriteSyncReqValidationError) Field() string { return e.field }

func (e SurveyOverwriteSyncReqValidationError) Reason() string { return e.reason }

func (e SurveyOverwriteSyncReqValidationError) Message() string { return e.message }

func (e SurveyOverwriteSyncReqValidationError) Cause() error { return e.cause }

func (e SurveyOverwriteSyncReqValidationError) ErrorName() string {
	return "SurveyOverwriteSyncReqValidationError"
}

func (e SurveyOverwriteSyncReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid SurveyOverwriteSyncReq." + e.field + ": " + e.message + cause
}
