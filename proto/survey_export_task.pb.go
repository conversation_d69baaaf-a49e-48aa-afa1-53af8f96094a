// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_export_task.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurveyExportTaskListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页码，默认1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，默认10
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	SurveyId int64 `protobuf:"varint,4,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyExportTaskListRequest) Reset() {
	*x = SurveyExportTaskListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportTaskListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportTaskListRequest) ProtoMessage() {}

func (x *SurveyExportTaskListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportTaskListRequest.ProtoReflect.Descriptor instead.
func (*SurveyExportTaskListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyExportTaskListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SurveyExportTaskListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SurveyExportTaskListRequest) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyExportTaskListRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyExportTaskListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*SurveyExportTask `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64               `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *SurveyExportTaskListResponse) Reset() {
	*x = SurveyExportTaskListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportTaskListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportTaskListResponse) ProtoMessage() {}

func (x *SurveyExportTaskListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportTaskListResponse.ProtoReflect.Descriptor instead.
func (*SurveyExportTaskListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyExportTaskListResponse) GetList() []*SurveyExportTask {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyExportTaskListResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 详情
type SurveyExportTaskDetailsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *SurveyExportTaskDetailsReq) Reset() {
	*x = SurveyExportTaskDetailsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportTaskDetailsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportTaskDetailsReq) ProtoMessage() {}

func (x *SurveyExportTaskDetailsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportTaskDetailsReq.ProtoReflect.Descriptor instead.
func (*SurveyExportTaskDetailsReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{2}
}

func (x *SurveyExportTaskDetailsReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyExportTaskDetailsReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyExportTaskDetailsReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type DelSurveyExportTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids      []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	SurveyId int64   `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	ClientId int64   `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *DelSurveyExportTaskReq) Reset() {
	*x = DelSurveyExportTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelSurveyExportTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelSurveyExportTaskReq) ProtoMessage() {}

func (x *DelSurveyExportTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelSurveyExportTaskReq.ProtoReflect.Descriptor instead.
func (*DelSurveyExportTaskReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{3}
}

func (x *DelSurveyExportTaskReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DelSurveyExportTaskReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *DelSurveyExportTaskReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

type SurveyExportTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:主键ID"`
	// 租户id
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id" gorm:"column:clientid;type:varchar(128);comment:租户id"`
	// 问卷id
	SurveyId int64 `protobuf:"varint,3,opt,name=survey_id,json=surveyId,proto3" json:"survey_id" gorm:"type:int(11);comment:问卷id"`
	// 文件名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name" gorm:"type:varchar(125);comment:文件名称"`
	// 文件类型
	FileType int32 `protobuf:"varint,5,opt,name=file_type,json=fileType,proto3" json:"file_type" gorm:"type:tinyint(64);comment:文件类型 0: CSV 1: EXCEL"`
	// 数据类型
	DataType int32 `protobuf:"varint,6,opt,name=data_type,json=dataType,proto3" json:"data_type" gorm:"type:tinyint(4);comment:数据类型 0: 答案编码 1: 答案文本"`
	// 数据有效性
	IsValid int32 `protobuf:"varint,7,opt,name=is_valid,json=isValid,proto3" json:"is_valid" gorm:"type:tinyint(4);comment:数据有效性（0: 有效 1: 无效 2:全部）"`
	// url
	Url string `protobuf:"bytes,8,opt,name=url,proto3" json:"url" gorm:"type:tinyint(4);comment:url"`
	// 状态
	Status int32 `protobuf:"varint,9,opt,name=status,proto3" json:"status" gorm:"type:tinyint(4);comment:0: 等待，未处理 1: 处理中 2: 完成 3: 失败"`
	// 完成时间范围 起始时间
	StartTime string `protobuf:"bytes,10,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 完成时间范围 结束时间
	EndTime string `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 筛选内容
	ViewContent string `protobuf:"bytes,12,opt,name=view_content,json=viewContent,proto3" json:"view_content,omitempty" gorm:"view_content"`
	// 数据源，0：全部，1：随机
	DataSource int32 `protobuf:"varint,13,opt,name=data_source,json=dataSource,proto3" json:"data_source,omitempty" gorm:"data_source"`
	// 随机数量
	DataNum int32 `protobuf:"varint,14,opt,name=data_num,json=dataNum,proto3" json:"data_num,omitempty" gorm:"data_num"`
	// 拓展字段
	Extra string `protobuf:"bytes,15,opt,name=extra,proto3" json:"extra,omitempty" gorm:"extra"`
	// 创建时间
	CreateTime string `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty" gorm:"autoCreateTime"`
	// 更新时间
	UpdateTime string `protobuf:"bytes,17,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty" gorm:"autoUpdateTime"`
	// 完成时间
	CompleteTime string `protobuf:"bytes,18,opt,name=complete_time,json=completeTime,proto3" json:"complete_time"`
}

func (x *SurveyExportTask) Reset() {
	*x = SurveyExportTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportTask) ProtoMessage() {}

func (x *SurveyExportTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportTask.ProtoReflect.Descriptor instead.
func (*SurveyExportTask) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{4}
}

func (x *SurveyExportTask) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyExportTask) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyExportTask) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyExportTask) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyExportTask) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *SurveyExportTask) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *SurveyExportTask) GetIsValid() int32 {
	if x != nil {
		return x.IsValid
	}
	return 0
}

func (x *SurveyExportTask) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SurveyExportTask) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SurveyExportTask) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SurveyExportTask) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SurveyExportTask) GetViewContent() string {
	if x != nil {
		return x.ViewContent
	}
	return ""
}

func (x *SurveyExportTask) GetDataSource() int32 {
	if x != nil {
		return x.DataSource
	}
	return 0
}

func (x *SurveyExportTask) GetDataNum() int32 {
	if x != nil {
		return x.DataNum
	}
	return 0
}

func (x *SurveyExportTask) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *SurveyExportTask) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SurveyExportTask) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *SurveyExportTask) GetCompleteTime() string {
	if x != nil {
		return x.CompleteTime
	}
	return ""
}

type UserStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户分群名称
	ClusterName string `protobuf:"bytes,1,opt,name=cluster_name,json=clusterName,proto3" json:"cluster_name,omitempty"`
	// 用户分群基于的用户维度, 如vroleid, vopenid
	EntityName string `protobuf:"bytes,2,opt,name=entity_name,json=entityName,proto3" json:"entity_name,omitempty"`
	// 版本
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *UserStrategy) Reset() {
	*x = UserStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStrategy) ProtoMessage() {}

func (x *UserStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStrategy.ProtoReflect.Descriptor instead.
func (*UserStrategy) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{5}
}

func (x *UserStrategy) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *UserStrategy) GetEntityName() string {
	if x != nil {
		return x.EntityName
	}
	return ""
}

func (x *UserStrategy) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SurveyExportUserClusterSubmitReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 用户分群信息
	UserStrategy *UserStrategy `protobuf:"bytes,2,opt,name=user_strategy,json=userStrategy,proto3" json:"user_strategy,omitempty"`
}

func (x *SurveyExportUserClusterSubmitReq) Reset() {
	*x = SurveyExportUserClusterSubmitReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportUserClusterSubmitReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportUserClusterSubmitReq) ProtoMessage() {}

func (x *SurveyExportUserClusterSubmitReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportUserClusterSubmitReq.ProtoReflect.Descriptor instead.
func (*SurveyExportUserClusterSubmitReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{6}
}

func (x *SurveyExportUserClusterSubmitReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyExportUserClusterSubmitReq) GetUserStrategy() *UserStrategy {
	if x != nil {
		return x.UserStrategy
	}
	return nil
}

type SurveyExportHeadersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyExportHeadersReq) Reset() {
	*x = SurveyExportHeadersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportHeadersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportHeadersReq) ProtoMessage() {}

func (x *SurveyExportHeadersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportHeadersReq.ProtoReflect.Descriptor instead.
func (*SurveyExportHeadersReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{7}
}

func (x *SurveyExportHeadersReq) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyExportHeadersReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyExportHeadersRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List    []*SurveyExportHeadersRes_Question `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Version string                             `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *SurveyExportHeadersRes) Reset() {
	*x = SurveyExportHeadersRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportHeadersRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportHeadersRes) ProtoMessage() {}

func (x *SurveyExportHeadersRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportHeadersRes.ProtoReflect.Descriptor instead.
func (*SurveyExportHeadersRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{8}
}

func (x *SurveyExportHeadersRes) GetList() []*SurveyExportHeadersRes_Question {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyExportHeadersRes) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SurveyExportHeadersRes_QuestionRowTitle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RowUniqueKey string `protobuf:"bytes,1,opt,name=row_unique_key,json=rowUniqueKey,proto3" json:"row_unique_key,omitempty"`
	RoleTitle    string `protobuf:"bytes,2,opt,name=role_title,json=roleTitle,proto3" json:"role_title,omitempty"`
}

func (x *SurveyExportHeadersRes_QuestionRowTitle) Reset() {
	*x = SurveyExportHeadersRes_QuestionRowTitle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportHeadersRes_QuestionRowTitle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportHeadersRes_QuestionRowTitle) ProtoMessage() {}

func (x *SurveyExportHeadersRes_QuestionRowTitle) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportHeadersRes_QuestionRowTitle.ProtoReflect.Descriptor instead.
func (*SurveyExportHeadersRes_QuestionRowTitle) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{8, 0}
}

func (x *SurveyExportHeadersRes_QuestionRowTitle) GetRowUniqueKey() string {
	if x != nil {
		return x.RowUniqueKey
	}
	return ""
}

func (x *SurveyExportHeadersRes_QuestionRowTitle) GetRoleTitle() string {
	if x != nil {
		return x.RoleTitle
	}
	return ""
}

type SurveyExportHeadersRes_SelectOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value    string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`        // 选项 option
	Label    string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`        // 选项 title
	HasBlank bool   `protobuf:"varint,3,opt,name=hasBlank,proto3" json:"hasBlank,omitempty"` // 是否有填空
}

func (x *SurveyExportHeadersRes_SelectOptions) Reset() {
	*x = SurveyExportHeadersRes_SelectOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportHeadersRes_SelectOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportHeadersRes_SelectOptions) ProtoMessage() {}

func (x *SurveyExportHeadersRes_SelectOptions) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportHeadersRes_SelectOptions.ProtoReflect.Descriptor instead.
func (*SurveyExportHeadersRes_SelectOptions) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{8, 1}
}

func (x *SurveyExportHeadersRes_SelectOptions) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SurveyExportHeadersRes_SelectOptions) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SurveyExportHeadersRes_SelectOptions) GetHasBlank() bool {
	if x != nil {
		return x.HasBlank
	}
	return false
}

type SurveyExportHeadersRes_Question struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionUniqueKey string                                     `protobuf:"bytes,1,opt,name=question_unique_key,json=questionUniqueKey,proto3" json:"question_unique_key,omitempty"`
	QuestionTitle     string                                     `protobuf:"bytes,2,opt,name=question_title,json=questionTitle,proto3" json:"question_title,omitempty"`
	StatisticsMethod  string                                     `protobuf:"bytes,3,opt,name=statistics_method,json=statisticsMethod,proto3" json:"statistics_method,omitempty"`
	QuestionType      string                                     `protobuf:"bytes,4,opt,name=question_type,json=questionType,proto3" json:"question_type,omitempty"`
	SelectMode        string                                     `protobuf:"bytes,5,opt,name=select_mode,json=selectMode,proto3" json:"select_mode,omitempty"`
	QuestionId        int32                                      `protobuf:"varint,6,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	QuestionRowTitles []*SurveyExportHeadersRes_QuestionRowTitle `protobuf:"bytes,7,rep,name=question_row_titles,json=questionRowTitles,proto3" json:"question_row_titles,omitempty"`
	SelectOptions     []*SurveyExportHeadersRes_SelectOptions    `protobuf:"bytes,8,rep,name=select_options,json=selectOptions,proto3" json:"select_options,omitempty"`
}

func (x *SurveyExportHeadersRes_Question) Reset() {
	*x = SurveyExportHeadersRes_Question{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_export_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyExportHeadersRes_Question) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyExportHeadersRes_Question) ProtoMessage() {}

func (x *SurveyExportHeadersRes_Question) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_export_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyExportHeadersRes_Question.ProtoReflect.Descriptor instead.
func (*SurveyExportHeadersRes_Question) Descriptor() ([]byte, []int) {
	return file_proto_survey_export_task_proto_rawDescGZIP(), []int{8, 2}
}

func (x *SurveyExportHeadersRes_Question) GetQuestionUniqueKey() string {
	if x != nil {
		return x.QuestionUniqueKey
	}
	return ""
}

func (x *SurveyExportHeadersRes_Question) GetQuestionTitle() string {
	if x != nil {
		return x.QuestionTitle
	}
	return ""
}

func (x *SurveyExportHeadersRes_Question) GetStatisticsMethod() string {
	if x != nil {
		return x.StatisticsMethod
	}
	return ""
}

func (x *SurveyExportHeadersRes_Question) GetQuestionType() string {
	if x != nil {
		return x.QuestionType
	}
	return ""
}

func (x *SurveyExportHeadersRes_Question) GetSelectMode() string {
	if x != nil {
		return x.SelectMode
	}
	return ""
}

func (x *SurveyExportHeadersRes_Question) GetQuestionId() int32 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *SurveyExportHeadersRes_Question) GetQuestionRowTitles() []*SurveyExportHeadersRes_QuestionRowTitle {
	if x != nil {
		return x.QuestionRowTitles
	}
	return nil
}

func (x *SurveyExportHeadersRes_Question) GetSelectOptions() []*SurveyExportHeadersRes_SelectOptions {
	if x != nil {
		return x.SelectOptions
	}
	return nil
}

var File_proto_survey_export_task_proto protoreflect.FileDescriptor

var file_proto_survey_export_task_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x65,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x76, 0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72,
	0x2f, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x01,
	0x0a, 0x1b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0x7b, 0x0a, 0x1c, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x3a, 0x05,
	0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x72, 0x0a, 0x1a, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04, 0xe2, 0x41,
	0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x16, 0x44, 0x65, 0x6c,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x09, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xc8, 0x0c, 0x0a, 0x10, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x4c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x3c, 0xd2, 0xa7, 0x86, 0x07, 0x37, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e,
	0x74, 0x28, 0x31, 0x31, 0x29, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x20, 0x30, 0x3b,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe4, 0xb8, 0xbb, 0xe9, 0x94, 0xae, 0x49, 0x44,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x72, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x55, 0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2,
	0xa7, 0x86, 0x07, 0x37, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x3a,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x69, 0x64, 0x3b, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe7, 0xa7, 0x9f, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0e,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x52, 0x08,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2b, 0xe2, 0x41, 0x01,
	0x02, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a,
	0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x69, 0x64, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x49, 0x64, 0x12, 0x49, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x35, 0xba, 0x47, 0x02, 0x78, 0x7d, 0xd2, 0xa7, 0x86, 0x07, 0x2b, 0x67, 0x6f, 0x72, 0x6d,
	0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32,
	0x35, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x96, 0x87, 0xe4, 0xbb,
	0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x6f, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x52, 0xd2, 0xa7, 0x86, 0x07, 0x3a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65,
	0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x96, 0x87, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x20, 0x30, 0x3a, 0x20, 0x43, 0x53, 0x56, 0x20, 0x31, 0x3a, 0x20, 0x45, 0x58, 0x43, 0x45,
	0x4c, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x7e,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x61, 0xd2, 0xa7, 0x86, 0x07, 0x49, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x20, 0x30, 0x3a, 0x20, 0xe7, 0xad, 0x94, 0xe6, 0xa1, 0x88, 0xe7, 0xbc, 0x96, 0xe7, 0xa0,
	0x81, 0x20, 0x31, 0x3a, 0x20, 0xe7, 0xad, 0x94, 0xe6, 0xa1, 0x88, 0xe6, 0x96, 0x87, 0xe6, 0x9c,
	0xac, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x80,
	0x01, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x65, 0xd2, 0xa7, 0x86, 0x07, 0x4e, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70,
	0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe6, 0x9c, 0x89, 0xe6, 0x95,
	0x88, 0xe6, 0x80, 0xa7, 0xef, 0xbc, 0x88, 0x30, 0x3a, 0x20, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88,
	0x20, 0x31, 0x3a, 0x20, 0xe6, 0x97, 0xa0, 0xe6, 0x95, 0x88, 0x20, 0x32, 0x3a, 0xe5, 0x85, 0xa8,
	0xe9, 0x83, 0xa8, 0xef, 0xbc, 0x89, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x07, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x12, 0x44, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x32,
	0xd2, 0xa7, 0x86, 0x07, 0x20, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74,
	0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x3a, 0x75, 0x72, 0x6c, 0xd2, 0xa7, 0x86, 0x07, 0x08, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x75,
	0x72, 0x6c, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x80, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x68, 0xd2, 0xa7, 0x86, 0x07, 0x53, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74,
	0x28, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0x30, 0x3a, 0x20, 0xe7,
	0xad, 0x89, 0xe5, 0xbe, 0x85, 0xef, 0xbc, 0x8c, 0xe6, 0x9c, 0xaa, 0xe5, 0xa4, 0x84, 0xe7, 0x90,
	0x86, 0x20, 0x31, 0x3a, 0x20, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe4, 0xb8, 0xad, 0x20, 0x32,
	0x3a, 0x20, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x20, 0x33, 0x3a, 0x20, 0xe5, 0xa4, 0xb1, 0xe8,
	0xb4, 0xa5, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14,
	0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x2d, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x12, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x59,
	0x0a, 0x0c, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x36, 0xd2, 0xa7, 0x86, 0x07, 0x1b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2c, 0x6f, 0x6d, 0x69,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x11, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x76, 0x69,
	0x65, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x0b, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x34,
	0xd2, 0xa7, 0x86, 0x07, 0x1a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2,
	0xa7, 0x86, 0x07, 0x10, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x49, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x2e, 0xd2, 0xa7, 0x86, 0x07, 0x17, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x6e, 0x75, 0x6d, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e,
	0x75, 0x6d, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x75, 0x6d, 0x12, 0x3e, 0x0a, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xd2, 0xa7, 0x86, 0x07,
	0x14, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2c, 0x6f, 0x6d, 0x69, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x5c, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x3b, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x1a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x61,
	0x75, 0x74, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x58, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x37,
	0xd2, 0xa7, 0x86, 0x07, 0x1a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2,
	0xa7, 0x86, 0x07, 0x13, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x61, 0x75, 0x74, 0x6f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1b, 0xe2, 0x41, 0x01, 0x03,
	0xd2, 0xa7, 0x86, 0x07, 0x12, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x78, 0x0a, 0x0c,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x27, 0x0a, 0x0c,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02,
	0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x98, 0x01, 0x0a, 0x20, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x51,
	0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65,
	0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x42, 0x04, 0xe2,
	0x41, 0x01, 0x02, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x22, 0x5e, 0x0a, 0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x04, 0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49,
	0x64, 0x22, 0x85, 0x06, 0x0a, 0x16, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x2e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x57, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x6f, 0x77, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x6f, 0x77,
	0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x72, 0x6f, 0x77, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x1a, 0x57,
	0x0a, 0x0d, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x61, 0x73, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68,
	0x61, 0x73, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x1a, 0xcf, 0x03, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x71, 0x0a, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x6f, 0x77, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70,
	0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77,
	0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x77, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x52,
	0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x77, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x73, 0x12, 0x65, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_export_task_proto_rawDescOnce sync.Once
	file_proto_survey_export_task_proto_rawDescData = file_proto_survey_export_task_proto_rawDesc
)

func file_proto_survey_export_task_proto_rawDescGZIP() []byte {
	file_proto_survey_export_task_proto_rawDescOnce.Do(func() {
		file_proto_survey_export_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_export_task_proto_rawDescData)
	})
	return file_proto_survey_export_task_proto_rawDescData
}

var file_proto_survey_export_task_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_proto_survey_export_task_proto_goTypes = []any{
	(*SurveyExportTaskListRequest)(nil),             // 0: papegames.sparrow.survey.SurveyExportTaskListRequest
	(*SurveyExportTaskListResponse)(nil),            // 1: papegames.sparrow.survey.SurveyExportTaskListResponse
	(*SurveyExportTaskDetailsReq)(nil),              // 2: papegames.sparrow.survey.SurveyExportTaskDetailsReq
	(*DelSurveyExportTaskReq)(nil),                  // 3: papegames.sparrow.survey.DelSurveyExportTaskReq
	(*SurveyExportTask)(nil),                        // 4: papegames.sparrow.survey.SurveyExportTask
	(*UserStrategy)(nil),                            // 5: papegames.sparrow.survey.UserStrategy
	(*SurveyExportUserClusterSubmitReq)(nil),        // 6: papegames.sparrow.survey.SurveyExportUserClusterSubmitReq
	(*SurveyExportHeadersReq)(nil),                  // 7: papegames.sparrow.survey.SurveyExportHeadersReq
	(*SurveyExportHeadersRes)(nil),                  // 8: papegames.sparrow.survey.SurveyExportHeadersRes
	(*SurveyExportHeadersRes_QuestionRowTitle)(nil), // 9: papegames.sparrow.survey.SurveyExportHeadersRes.QuestionRowTitle
	(*SurveyExportHeadersRes_SelectOptions)(nil),    // 10: papegames.sparrow.survey.SurveyExportHeadersRes.SelectOptions
	(*SurveyExportHeadersRes_Question)(nil),         // 11: papegames.sparrow.survey.SurveyExportHeadersRes.Question
}
var file_proto_survey_export_task_proto_depIdxs = []int32{
	4,  // 0: papegames.sparrow.survey.SurveyExportTaskListResponse.list:type_name -> papegames.sparrow.survey.SurveyExportTask
	5,  // 1: papegames.sparrow.survey.SurveyExportUserClusterSubmitReq.user_strategy:type_name -> papegames.sparrow.survey.UserStrategy
	11, // 2: papegames.sparrow.survey.SurveyExportHeadersRes.list:type_name -> papegames.sparrow.survey.SurveyExportHeadersRes.Question
	9,  // 3: papegames.sparrow.survey.SurveyExportHeadersRes.Question.question_row_titles:type_name -> papegames.sparrow.survey.SurveyExportHeadersRes.QuestionRowTitle
	10, // 4: papegames.sparrow.survey.SurveyExportHeadersRes.Question.select_options:type_name -> papegames.sparrow.survey.SurveyExportHeadersRes.SelectOptions
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_proto_survey_export_task_proto_init() }
func file_proto_survey_export_task_proto_init() {
	if File_proto_survey_export_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_export_task_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportTaskListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportTaskListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportTaskDetailsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*DelSurveyExportTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*UserStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportUserClusterSubmitReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportHeadersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportHeadersRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportHeadersRes_QuestionRowTitle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportHeadersRes_SelectOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_export_task_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyExportHeadersRes_Question); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_export_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_export_task_proto_goTypes,
		DependencyIndexes: file_proto_survey_export_task_proto_depIdxs,
		MessageInfos:      file_proto_survey_export_task_proto_msgTypes,
	}.Build()
	File_proto_survey_export_task_proto = out.File
	file_proto_survey_export_task_proto_rawDesc = nil
	file_proto_survey_export_task_proto_goTypes = nil
	file_proto_survey_export_task_proto_depIdxs = nil
}
