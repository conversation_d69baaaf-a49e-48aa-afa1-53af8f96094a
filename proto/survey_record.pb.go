// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/survey_record.proto

package proto

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurveyRecordListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前页码，默认1
	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，默认10
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 答卷id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 平台账号id
	Openid string `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid,omitempty"`
	// 玩家角色id
	RoleId string `protobuf:"bytes,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// IP
	Ip string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
	// 设备id
	DeviceId string `protobuf:"bytes,7,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// sort_id
	SortId string `protobuf:"bytes,8,opt,name=sort_id,json=sortId,proto3" json:"sort_id,omitempty"`
	// sort_end_time
	SortEndTime string `protobuf:"bytes,9,opt,name=sort_end_time,json=sortEndTime,proto3" json:"sort_end_time,omitempty"`
	// sort_second
	SortSecond string `protobuf:"bytes,10,opt,name=sort_second,json=sortSecond,proto3" json:"sort_second,omitempty"`
	SurveyId   int64  `protobuf:"varint,11,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyRecordListRequest) Reset() {
	*x = SurveyRecordListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordListRequest) ProtoMessage() {}

func (x *SurveyRecordListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordListRequest.ProtoReflect.Descriptor instead.
func (*SurveyRecordListRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{0}
}

func (x *SurveyRecordListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SurveyRecordListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SurveyRecordListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecordListRequest) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SurveyRecordListRequest) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SurveyRecordListRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SurveyRecordListRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SurveyRecordListRequest) GetSortId() string {
	if x != nil {
		return x.SortId
	}
	return ""
}

func (x *SurveyRecordListRequest) GetSortEndTime() string {
	if x != nil {
		return x.SortEndTime
	}
	return ""
}

func (x *SurveyRecordListRequest) GetSortSecond() string {
	if x != nil {
		return x.SortSecond
	}
	return ""
}

func (x *SurveyRecordListRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyRecordListV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 问卷ID
	SurveyId int64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 客户端ID
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// 筛选器内容
	ViewContent string `protobuf:"bytes,3,opt,name=view_content,json=viewContent,proto3" json:"view_content,omitempty"`
	// limit
	//
	//	int32 limit = 4 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
	//
	// 当前页码，默认1
	Page int32 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，默认50
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *SurveyRecordListV2Request) Reset() {
	*x = SurveyRecordListV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordListV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordListV2Request) ProtoMessage() {}

func (x *SurveyRecordListV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordListV2Request.ProtoReflect.Descriptor instead.
func (*SurveyRecordListV2Request) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{1}
}

func (x *SurveyRecordListV2Request) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyRecordListV2Request) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *SurveyRecordListV2Request) GetViewContent() string {
	if x != nil {
		return x.ViewContent
	}
	return ""
}

func (x *SurveyRecordListV2Request) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SurveyRecordListV2Request) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type Cloumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *Cloumn) Reset() {
	*x = Cloumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cloumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cloumn) ProtoMessage() {}

func (x *Cloumn) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cloumn.ProtoReflect.Descriptor instead.
func (*Cloumn) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{2}
}

func (x *Cloumn) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Cloumn) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type SurveyRecordListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*SurveyRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64           `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *SurveyRecordListResponse) Reset() {
	*x = SurveyRecordListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordListResponse) ProtoMessage() {}

func (x *SurveyRecordListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordListResponse.ProtoReflect.Descriptor instead.
func (*SurveyRecordListResponse) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{3}
}

func (x *SurveyRecordListResponse) GetList() []*SurveyRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurveyRecordListResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type SurveyRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyRecordsRequest) Reset() {
	*x = SurveyRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordsRequest) ProtoMessage() {}

func (x *SurveyRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordsRequest.ProtoReflect.Descriptor instead.
func (*SurveyRecordsRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{4}
}

func (x *SurveyRecordsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecordsRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SetValidSurveyRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId int64   `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	Ids      []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SetValidSurveyRecordRequest) Reset() {
	*x = SetValidSurveyRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetValidSurveyRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetValidSurveyRecordRequest) ProtoMessage() {}

func (x *SetValidSurveyRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetValidSurveyRecordRequest.ProtoReflect.Descriptor instead.
func (*SetValidSurveyRecordRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{5}
}

func (x *SetValidSurveyRecordRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SetValidSurveyRecordRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type SurveyRecordDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DelList  []int64 `protobuf:"varint,1,rep,packed,name=del_list,json=delList,proto3" json:"del_list,omitempty"`
	SurveyId int64   `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
}

func (x *SurveyRecordDetailsRequest) Reset() {
	*x = SurveyRecordDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecordDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecordDetailsRequest) ProtoMessage() {}

func (x *SurveyRecordDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecordDetailsRequest.ProtoReflect.Descriptor instead.
func (*SurveyRecordDetailsRequest) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{6}
}

func (x *SurveyRecordDetailsRequest) GetDelList() []int64 {
	if x != nil {
		return x.DelList
	}
	return nil
}

func (x *SurveyRecordDetailsRequest) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

type SurveyRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:主键ID"`
	// 用户UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid" gorm:"type:varchar(128);comment:用户UID"`
	// openid
	Openid string `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid" gorm:"type:varchar(64);comment:openid"`
	// 角色ID
	RoleId string `protobuf:"bytes,5,opt,name=role_id,json=roleId,proto3" json:"role_id" gorm:"type:varchar(64);comment:角色ID"`
	// 设备ID
	DeviceId string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id" gorm:"type:varchar(128);comment:设备ID"`
	// ip
	Ip string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip" gorm:"type:varchar(64);comment:ip"`
	// 记录是否有效
	IsValid int32 `protobuf:"varint,8,opt,name=is_valid,json=isValid,proto3" json:"is_valid" gorm:"type:tinyint(4);comment:记录是否有效（0: 有效，1: 无效）"`
	// 是否删除
	IsDelete int32 `protobuf:"varint,9,opt,name=is_delete,json=isDelete,proto3" json:"is_delete" gorm:"type:tinyint(4);comment:记录是否删除（0: 未删除，1: 已删除）"`
	// 答题开始时间
	BeginTime string `protobuf:"bytes,10,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	// 答题结束时间
	EndTime string `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 额外信息
	Extra string `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra"`
	// 创建时间
	Ctime string `protobuf:"bytes,13,opt,name=ctime,proto3" json:"ctime,omitempty" gorm:"autoCreateTime"`
	// 答题时间
	Second string `protobuf:"bytes,14,opt,name=second,proto3" json:"second" gorm:"-"`
}

func (x *SurveyRecord) Reset() {
	*x = SurveyRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyRecord) ProtoMessage() {}

func (x *SurveyRecord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyRecord.ProtoReflect.Descriptor instead.
func (*SurveyRecord) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{7}
}

func (x *SurveyRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyRecord) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SurveyRecord) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SurveyRecord) GetRoleId() string {
	if x != nil {
		return x.RoleId
	}
	return ""
}

func (x *SurveyRecord) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SurveyRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SurveyRecord) GetIsValid() int32 {
	if x != nil {
		return x.IsValid
	}
	return 0
}

func (x *SurveyRecord) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *SurveyRecord) GetBeginTime() string {
	if x != nil {
		return x.BeginTime
	}
	return ""
}

func (x *SurveyRecord) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *SurveyRecord) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *SurveyRecord) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *SurveyRecord) GetSecond() string {
	if x != nil {
		return x.Second
	}
	return ""
}

type SurveyViewCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 答卷ID
	SurveyId int64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 过滤器名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 过滤期内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 视图类型，1：回收列表，2：交叉分析
	Kind int32 `protobuf:"varint,4,opt,name=kind,proto3" json:"kind,omitempty"`
}

func (x *SurveyViewCreateReq) Reset() {
	*x = SurveyViewCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyViewCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyViewCreateReq) ProtoMessage() {}

func (x *SurveyViewCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyViewCreateReq.ProtoReflect.Descriptor instead.
func (*SurveyViewCreateReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{8}
}

func (x *SurveyViewCreateReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyViewCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyViewCreateReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SurveyViewCreateReq) GetKind() int32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

type SurveyViewCreateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SurveyViewCreateRes) Reset() {
	*x = SurveyViewCreateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyViewCreateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyViewCreateRes) ProtoMessage() {}

func (x *SurveyViewCreateRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyViewCreateRes.ProtoReflect.Descriptor instead.
func (*SurveyViewCreateRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{9}
}

func (x *SurveyViewCreateRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SurveyView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID，自增主键，创建时此参数不需要传
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" gorm:"primaryKey;type:int(11) default 0;comment:过滤器ID"`
	// 问卷ID
	SurveyId int64 `protobuf:"varint,2,opt,name=survey_id,json=surveyId,proto3" json:"survey_id" gorm:"type:int(11);comment:问卷ID"`
	// 筛选器名
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name" gorm:"type:varchar(64);comment:筛选器名"`
	// 筛选器内容
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content" gorm:"type:string;comment:筛选器内容"`
	// 是否删除
	IsDelete int32 `protobuf:"varint,5,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty" gorm:"type:int(11);comment:是否删除，0：未删除，1：删除"`
	// 视图类型
	Kind int32 `protobuf:"varint,6,opt,name=kind,proto3" json:"kind" gorm:"type:int(11);comment:视图类型，1：回收列表，2：交叉分析"`
	// 创建时间
	Ctime string `protobuf:"bytes,7,opt,name=ctime,proto3" json:"ctime,omitempty" gorm:"ctime"`
	// 修改时间
	Mtime string `protobuf:"bytes,8,opt,name=mtime,proto3" json:"-" gorm:"mtime"`
	// 创建人
	Creator string `protobuf:"bytes,9,opt,name=creator,proto3" json:"-" gorm:"creator"`
	// 最近修改人
	Editor string `protobuf:"bytes,10,opt,name=editor,proto3" json:"-" gorm:"editor"`
}

func (x *SurveyView) Reset() {
	*x = SurveyView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyView) ProtoMessage() {}

func (x *SurveyView) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyView.ProtoReflect.Descriptor instead.
func (*SurveyView) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{10}
}

func (x *SurveyView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurveyView) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurveyView) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SurveyView) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *SurveyView) GetKind() int32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

func (x *SurveyView) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *SurveyView) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *SurveyView) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *SurveyView) GetEditor() string {
	if x != nil {
		return x.Editor
	}
	return ""
}

type SurveyViewListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 答卷ID
	SurveyId int64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id,omitempty"`
	// 视图类型，1：回收列表，2：交叉分析
	Kind int32 `protobuf:"varint,2,opt,name=kind,proto3" json:"kind,omitempty"`
}

func (x *SurveyViewListReq) Reset() {
	*x = SurveyViewListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyViewListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyViewListReq) ProtoMessage() {}

func (x *SurveyViewListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyViewListReq.ProtoReflect.Descriptor instead.
func (*SurveyViewListReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{11}
}

func (x *SurveyViewListReq) GetSurveyId() int64 {
	if x != nil {
		return x.SurveyId
	}
	return 0
}

func (x *SurveyViewListReq) GetKind() int32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

type SurveyViewListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 筛选器列表
	List []*SurveyView `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SurveyViewListRes) Reset() {
	*x = SurveyViewListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyViewListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyViewListRes) ProtoMessage() {}

func (x *SurveyViewListRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyViewListRes.ProtoReflect.Descriptor instead.
func (*SurveyViewListRes) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{12}
}

func (x *SurveyViewListRes) GetList() []*SurveyView {
	if x != nil {
		return x.List
	}
	return nil
}

type SurveyViewDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 要删除的筛选器ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SurveyViewDeleteReq) Reset() {
	*x = SurveyViewDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_survey_record_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyViewDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyViewDeleteReq) ProtoMessage() {}

func (x *SurveyViewDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_survey_record_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyViewDeleteReq.ProtoReflect.Descriptor instead.
func (*SurveyViewDeleteReq) Descriptor() ([]byte, []int) {
	return file_proto_survey_record_proto_rawDescGZIP(), []int{13}
}

func (x *SurveyViewDeleteReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_proto_survey_record_proto protoreflect.FileDescriptor

var file_proto_survey_record_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76,
	0x33, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x74, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x67,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x02, 0x0a, 0x17, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x73, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0xc1,
	0x01, 0x0a, 0x19, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x10, 0xe2, 0x41, 0x01, 0x02, 0xba, 0x47, 0x09, 0x69, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0,
	0x3f, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x04,
	0xe2, 0x41, 0x01, 0x02, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x30, 0x0a, 0x06, 0x43, 0x6c, 0x6f, 0x75, 0x6d, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x73, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72,
	0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x43, 0x0a, 0x14, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x22, 0x4c,
	0x0a, 0x1b, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x54, 0x0a, 0x1a,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65,
	0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x64, 0x65,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x49, 0x64, 0x22, 0x8a, 0x08, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x4c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x3c, 0xd2, 0xa7, 0x86, 0x07, 0x37, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31,
	0x31, 0x29, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x20, 0x30, 0x3b, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe4, 0xb8, 0xbb, 0xe9, 0x94, 0xae, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x52, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x40,
	0xba, 0x47, 0x03, 0x78, 0x80, 0x01, 0xd2, 0xa7, 0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a,
	0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x31, 0x32, 0x38,
	0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x55, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x08, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x75, 0x69, 0x64,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3e, 0xba, 0x47, 0x02, 0x78, 0x7d, 0xd2, 0xa7, 0x86, 0x07,
	0x24, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68,
	0x61, 0x72, 0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x55, 0x0a,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3c,
	0xd2, 0xa7, 0x86, 0x07, 0x26, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76,
	0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0xe8, 0xa7, 0x92, 0xe8, 0x89, 0xb2, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0c,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x06, 0x72, 0x6f,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3f, 0xd2, 0xa7, 0x86, 0x07, 0x27, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28,
	0x31, 0x32, 0x38, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xae, 0xbe,
	0xe5, 0xa4, 0x87, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x41, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31,
	0xd2, 0xa7, 0x86, 0x07, 0x20, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76,
	0x61, 0x72, 0x63, 0x68, 0x61, 0x72, 0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x3a, 0x69, 0x70, 0xd2, 0xa7, 0x86, 0x07, 0x07, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69,
	0x70, 0x52, 0x02, 0x69, 0x70, 0x12, 0x7c, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x61, 0xd2, 0xa7, 0x86, 0x07, 0x4a, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28,
	0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xae, 0xb0, 0xe5, 0xbd,
	0x95, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xef, 0xbc, 0x88,
	0x30, 0x3a, 0x20, 0xe6, 0x9c, 0x89, 0xe6, 0x95, 0x88, 0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x20, 0xe6,
	0x97, 0xa0, 0xe6, 0x95, 0x88, 0xef, 0xbc, 0x89, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x07, 0x69, 0x73, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x12, 0x85, 0x01, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x68, 0xd2, 0xa7, 0x86, 0x07, 0x50, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x74, 0x69, 0x6e, 0x79, 0x69, 0x6e, 0x74, 0x28,
	0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xae, 0xb0, 0xe5, 0xbd,
	0x95, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef, 0xbc, 0x88,
	0x30, 0x3a, 0x20, 0xe6, 0x9c, 0xaa, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef, 0xbc, 0x8c, 0x31,
	0x3a, 0x20, 0xe5, 0xb7, 0xb2, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef, 0xbc, 0x89, 0xd2, 0xa7,
	0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x62,
	0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0xd2, 0xa7, 0x86, 0x07, 0x0f, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x62, 0x65, 0x67, 0x69, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x12, 0xd2, 0xa7, 0x86, 0x07, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x25, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f,
	0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x4b, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x14,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0xd2, 0xa7, 0x86, 0x07, 0x13, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x61, 0x75,
	0x74, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x2d,
	0x52, 0x06, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22,
	0x74, 0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x25, 0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56,
	0x69, 0x65, 0x77, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd8, 0x06, 0x0a,
	0x0a, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x4f, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x3f, 0xd2, 0xa7, 0x86, 0x07, 0x3a, 0x67, 0x6f,
	0x72, 0x6d, 0x3a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x3b, 0x74, 0x79,
	0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x20, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x20, 0x30, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xbf, 0x87,
	0xe6, 0xbb, 0xa4, 0xe5, 0x99, 0xa8, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x57, 0x0a, 0x09,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x3a, 0xd2, 0xa7, 0x86, 0x07, 0x22, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a,
	0x69, 0x6e, 0x74, 0x28, 0x31, 0x31, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0xe9, 0x97, 0xae, 0xe5, 0x8d, 0xb7, 0x49, 0x44, 0xd2, 0xa7, 0x86, 0x07, 0x0e, 0x6a, 0x73, 0x6f,
	0x6e, 0x3a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x52, 0x08, 0x73, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x42, 0xba, 0x47, 0x02, 0x78, 0x32, 0xd2, 0xa7, 0x86, 0x07, 0x2a, 0x67,
	0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x76, 0x61, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x28, 0x36, 0x34, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe7, 0xad, 0x9b,
	0xe9, 0x80, 0x89, 0xe5, 0x99, 0xa8, 0xe5, 0x90, 0x8d, 0xd2, 0xa7, 0x86, 0x07, 0x09, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x6e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x58, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3e,
	0xd2, 0xa7, 0x86, 0x07, 0x28, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe7, 0xad,
	0x9b, 0xe9, 0x80, 0x89, 0xe5, 0x99, 0xa8, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xd2, 0xa7, 0x86,
	0x07, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x65, 0xd2, 0xa7, 0x86,
	0x07, 0x43, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28,
	0x31, 0x31, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef, 0xbc, 0x8c, 0x30, 0xef, 0xbc, 0x9a, 0xe6,
	0x9c, 0xaa, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xef, 0xbc, 0x8c, 0x31, 0xef, 0xbc, 0x9a, 0xe5,
	0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xd2, 0xa7, 0x86, 0x07, 0x18, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x73, 0x0a, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x5f, 0xd2, 0xa7, 0x86, 0x07,
	0x4c, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x69, 0x6e, 0x74, 0x28, 0x31,
	0x31, 0x29, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xa7, 0x86, 0xe5, 0x9b,
	0xbe, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x8c, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0x9b,
	0x9e, 0xe6, 0x94, 0xb6, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0xef, 0xbc, 0x8c, 0x32, 0xef, 0xbc,
	0x9a, 0xe4, 0xba, 0xa4, 0xe5, 0x8f, 0x89, 0xe5, 0x88, 0x86, 0xe6, 0x9e, 0x90, 0xd2, 0xa7, 0x86,
	0x07, 0x09, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x6b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x12, 0x42, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2c, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x14, 0x6a, 0x73, 0x6f, 0x6e, 0x3a,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0x2c, 0x6f, 0x6d, 0x69, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x79, 0xd2,
	0xa7, 0x86, 0x07, 0x0a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x05,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x6a,
	0x73, 0x6f, 0x6e, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x0a, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0xe2, 0x41,
	0x01, 0x03, 0xd2, 0xa7, 0x86, 0x07, 0x06, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x2d, 0xd2, 0xa7, 0x86,
	0x07, 0x0c, 0x67, 0x6f, 0x72, 0x6d, 0x3a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0xe2, 0x41, 0x01, 0x03, 0xd2, 0xa7, 0x86,
	0x07, 0x06, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x2d, 0xd2, 0xa7, 0x86, 0x07, 0x0b, 0x67, 0x6f, 0x72,
	0x6d, 0x3a, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72,
	0x3a, 0x05, 0xc8, 0xa7, 0x86, 0x07, 0x01, 0x22, 0x44, 0x0a, 0x11, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x4d, 0x0a,
	0x11, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61,
	0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x27, 0x0a, 0x13,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x69, 0x65, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x42, 0x41, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x70,
	0x65, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x2e, 0x73, 0x70, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x2e, 0x73,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x42, 0x0b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x12, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_survey_record_proto_rawDescOnce sync.Once
	file_proto_survey_record_proto_rawDescData = file_proto_survey_record_proto_rawDesc
)

func file_proto_survey_record_proto_rawDescGZIP() []byte {
	file_proto_survey_record_proto_rawDescOnce.Do(func() {
		file_proto_survey_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_survey_record_proto_rawDescData)
	})
	return file_proto_survey_record_proto_rawDescData
}

var file_proto_survey_record_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_proto_survey_record_proto_goTypes = []any{
	(*SurveyRecordListRequest)(nil),     // 0: papegames.sparrow.survey.SurveyRecordListRequest
	(*SurveyRecordListV2Request)(nil),   // 1: papegames.sparrow.survey.SurveyRecordListV2Request
	(*Cloumn)(nil),                      // 2: papegames.sparrow.survey.Cloumn
	(*SurveyRecordListResponse)(nil),    // 3: papegames.sparrow.survey.SurveyRecordListResponse
	(*SurveyRecordsRequest)(nil),        // 4: papegames.sparrow.survey.SurveyRecordsRequest
	(*SetValidSurveyRecordRequest)(nil), // 5: papegames.sparrow.survey.SetValidSurveyRecordRequest
	(*SurveyRecordDetailsRequest)(nil),  // 6: papegames.sparrow.survey.SurveyRecordDetailsRequest
	(*SurveyRecord)(nil),                // 7: papegames.sparrow.survey.SurveyRecord
	(*SurveyViewCreateReq)(nil),         // 8: papegames.sparrow.survey.SurveyViewCreateReq
	(*SurveyViewCreateRes)(nil),         // 9: papegames.sparrow.survey.SurveyViewCreateRes
	(*SurveyView)(nil),                  // 10: papegames.sparrow.survey.SurveyView
	(*SurveyViewListReq)(nil),           // 11: papegames.sparrow.survey.SurveyViewListReq
	(*SurveyViewListRes)(nil),           // 12: papegames.sparrow.survey.SurveyViewListRes
	(*SurveyViewDeleteReq)(nil),         // 13: papegames.sparrow.survey.SurveyViewDeleteReq
}
var file_proto_survey_record_proto_depIdxs = []int32{
	7,  // 0: papegames.sparrow.survey.SurveyRecordListResponse.list:type_name -> papegames.sparrow.survey.SurveyRecord
	10, // 1: papegames.sparrow.survey.SurveyViewListRes.list:type_name -> papegames.sparrow.survey.SurveyView
	2,  // [2:2] is the sub-list for method output_type
	2,  // [2:2] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_proto_survey_record_proto_init() }
func file_proto_survey_record_proto_init() {
	if File_proto_survey_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_survey_record_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordListV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Cloumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*SetValidSurveyRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecordDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyViewCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyViewCreateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyViewListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyViewListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_survey_record_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SurveyViewDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_survey_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_survey_record_proto_goTypes,
		DependencyIndexes: file_proto_survey_record_proto_depIdxs,
		MessageInfos:      file_proto_survey_record_proto_msgTypes,
	}.Build()
	File_proto_survey_record_proto = out.File
	file_proto_survey_record_proto_rawDesc = nil
	file_proto_survey_record_proto_goTypes = nil
	file_proto_survey_record_proto_depIdxs = nil
}
