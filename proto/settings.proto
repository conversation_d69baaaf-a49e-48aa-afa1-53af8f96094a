syntax = "proto3";

package papegames.sparrow.survey;

import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message ValidRedeemConfigRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 client_id = 3;
}

message ValidRedeemConfigResponse {
  repeated ValidRedeemConfig list = 1;
  int64 total = 2;
}

message ValidRedeemConfig {
  option (tagger.disable_omitempty) = true;

  int64 id = 1 [(tagger.tags) = "json:id"];
  string client_id = 2 [(tagger.tags) = "json:client_id"];
  int32 redeem_id = 3 [(tagger.tags) = "json:redeem_id"];
  string redeem_head = 4 [(tagger.tags) = "json:redeem_head"];
  int32 redeem_type = 5 [(tagger.tags) = "json:redeem_type"];
  int32 is_custom = 6 [(tagger.tags) = "json:is_custom"];
  int32 redeem_number = 7 [(tagger.tags) = "json:redeem_number"];
  int32 redeem_timefor1name = 8 [(tagger.tags) = "json:redeem_timefor1name"];
  int32 redeem_timefor1id = 9 [(tagger.tags) = "json:redeem_timefor1id"];
  string redeem_open_time = 10 [(tagger.tags) = "json:redeem_opentime"];
  string redeem_close_time = 11 [(tagger.tags) = "json:redeem_closetime"];
  string redeem_item = 12 [(tagger.tags) = "json:redeem_item"];
  string redeem_gift = 13 [(tagger.tags) = "json:redeem_gift"];
  string redeem_channel = 14 [(tagger.tags) = "json:redeem_channel"];
  string redeem_create_id = 15 [(tagger.tags) = "json:redeem_create_id"];
  string redeem_whitelist = 16 [(tagger.tags) = "json:redeem_whitelist"];
  string extra = 17 [(tagger.tags) = "json:extra"];
  string memo = 18 [(tagger.tags) = "json:memo"];
  string mtime = 19 [(tagger.tags) = "json:mtime"];
  string ctime = 20 [(tagger.tags) = "json:ctime"];
  int32 redeem_auto = 21 [(tagger.tags) = "json:redeem_auto"];
  int32 notify = 22 [(tagger.tags) = "json:notify"];
  string redeem_head2 = 23 [(tagger.tags) = "json:redeemHead"];
}


message PreAwardTemplateRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 client_id = 3;
}

message PreAwardTemplateResponse {
  repeated PreAwardTemplate list = 1;
  int64 total = 2;
}

message PreAwardTemplate {
  string id = 1 [(tagger.tags) = "json:id"];
  string name = 2 [(tagger.tags) = "json:name"];
  string client_id = 3 [(tagger.tags) = "json:client_id"];
  string sender = 4 [(tagger.tags) = "json:sender"];
  string title = 5 [(tagger.tags) = "json:title"];
  string body = 6 [(tagger.tags) = "json:body"];
  string content = 7 [(tagger.tags) = "json:content"];
  string memo = 8 [(tagger.tags) = "json:memo"];
  string editor = 9 [(tagger.tags) = "json:editor"];
  string stime = 10 [(tagger.tags) = "json:stime"];
  string etime = 11 [(tagger.tags) = "json:etime"];
  string mtime = 12 [(tagger.tags) = "json:mtime"];
  string ctime = 13 [(tagger.tags) = "json:ctime"];
  int32 permanent = 14 [(tagger.tags) = "json:permanent"];
  string creator = 15 [(tagger.tags) = "json:creator"];
  int32 key = 16 [(tagger.tags) = "json:key"];
}