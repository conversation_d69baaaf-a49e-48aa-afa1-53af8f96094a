// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.4.0
// protoc          v4.25.1
// source: survey
package proto

import (
	context "context"
	json "encoding/json"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	hooks "gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func RegisterSurveyServiceGinServer(s *xgin.Server, srv SurveyServiceServer) {
	eng := s.GetGinEngine()
	eng.Use(hooks.GetHandlerFunc()...)
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/health",
		_SurveyServiceGin_Health_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/health", "Health")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/statistics/user-answer-record/survey-export/list",
		_SurveyServiceGin_SurveyExportTaskList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/statistics/user-answer-record/survey-export/list", "SurveyExportTaskList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/survey-export/create",
		_SurveyServiceGin_CreateSurveyExportTask_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/survey-export/create", "CreateSurveyExportTask")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/survey-export/del",
		_SurveyServiceGin_DelSurveyExportTask_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/survey-export/del", "DelSurveyExportTask")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/survey-export/resetStatus",
		_SurveyServiceGin_ResetSurveyExportTaskStatus_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/survey-export/resetStatus", "ResetSurveyExportTaskStatus")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/list",
		_SurveyServiceGin_SurveyRecordList_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/list", "SurveyRecordList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/list-v2",
		_SurveyServiceGin_SurveyRecordListV2_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/list-v2", "SurveyRecordListV2")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/delete",
		_SurveyServiceGin_DelSurveyRecord_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/delete", "DelSurveyRecord")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/set-valid",
		_SurveyServiceGin_SetValidSurveyRecord_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/set-valid", "SetValidSurveyRecord")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/user-answer-record/set-invalid",
		_SurveyServiceGin_SetInvalidSurveyRecord_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/user-answer-record/set-invalid", "SetInvalidSurveyRecord")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/statistics/user-answer-record/invalid-list",
		_SurveyServiceGin_InValidSurveyRecordList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/statistics/user-answer-record/invalid-list", "InValidSurveyRecordList")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/preview",
		_SurveyServiceGin_SurveyPreview_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/preview", "SurveyPreview")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/statistics/user-answer-record/detail",
		_SurveyServiceGin_SurveyRecordDetail_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/statistics/user-answer-record/detail", "SurveyRecordDetail")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/list",
		_SurveyServiceGin_SurveyList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/list", "SurveyList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/create",
		_SurveyServiceGin_CreateSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/create", "CreateSurvey")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/rule/detail",
		_SurveyServiceGin_SurveyStatistics_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/rule/detail", "SurveyStatistics")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/statistics/list",
		_SurveyServiceGin_GetSurveyStatisticsList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/statistics/list", "GetSurveyStatisticsList")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/show",
		_SurveyServiceGin_SurveyShow_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/show", "SurveyShow")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/logical-destory",
		_SurveyServiceGin_DeleteSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/logical-destory", "DeleteSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/copy",
		_SurveyServiceGin_CopySurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/copy", "CopySurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/sync",
		_SurveyServiceGin_SyncSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/sync", "SyncSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/imp",
		_SurveyServiceGin_ImpSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/imp", "ImpSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/set-status",
		_SurveyServiceGin_SetStatusSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/set-status", "SetStatusSurvey")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/recycle-list",
		_SurveyServiceGin_SurveyRecycleList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/recycle-list", "SurveyRecycleList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/delete",
		_SurveyServiceGin_DeleteSurveyRecycle_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/delete", "DeleteSurveyRecycle")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/recover-logical-destory",
		_SurveyServiceGin_RecoverSurveyRecycle_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/recover-logical-destory", "RecoverSurveyRecycle")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/clear-logical-destory",
		_SurveyServiceGin_ClearAllSurveyRecycle_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/clear-logical-destory", "ClearAllSurveyRecycle")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/recover-all-logical-destory",
		_SurveyServiceGin_RecoverAllSurveyRecycle_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/recover-all-logical-destory", "RecoverAllSurveyRecycle")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/general/get-deliver-list",
		_SurveyServiceGin_GetDeliverList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/general/get-deliver-list", "GetDeliverList")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/general/get-zone-list",
		_SurveyServiceGin_GetZoneList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/general/get-zone-list", "GetZoneList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/update",
		_SurveyServiceGin_UpdateSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/update", "UpdateSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/publish",
		_SurveyServiceGin_PublishSurvey_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/publish", "PublishSurvey")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/detail/input-method-list",
		_SurveyServiceGin_GetInputMethodList_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/detail/input-method-list", "GetInputMethodList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/detail",
		_SurveyServiceGin_SurveyStatisticsDetail_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/detail", "SurveyStatisticsDetail")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/statistics/detail2",
		_SurveyServiceGin_SurveyStatisticsDetailOld_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/statistics/detail2", "SurveyStatisticsDetailOld")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/user/get-info",
		_SurveyServiceGin_GetUserInfo_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/user/get-info", "GetUserInfo")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/user/check",
		_SurveyServiceGin_UserCheck_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/user/check", "UserCheck")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/rule/update",
		_SurveyServiceGin_StatisticsUpdate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/rule/update", "StatisticsUpdate")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/get-latest-survey-by-survey-id",
		_SurveyServiceGin_GetLatestSurveyBySurveyId_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/get-latest-survey-by-survey-id", "GetLatestSurveyBySurveyId")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/settings/get-valid-redeem-config-list",
		_SurveyServiceGin_GetValidRedeemConfigList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/settings/get-valid-redeem-config-list", "GetValidRedeemConfigList")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/settings/get-pre-award-template-list",
		_SurveyServiceGin_GetPreAwardTemplateList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/settings/get-pre-award-template-list", "GetPreAwardTemplateList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/general/upload",
		_SurveyServiceGin_Upload_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/general/upload", "Upload")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/view/create",
		_SurveyServiceGin_SurveyViewCreate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/view/create", "SurveyViewCreate")
	xgin.RegisterHandler(eng,
		"GET", "/v1/survey/view/list",
		_SurveyServiceGin_SurveyViewList_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/survey/view/list", "SurveyViewList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/view/update",
		_SurveyServiceGin_SurveyViewUpdate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/view/update", "SurveyViewUpdate")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/view/delete",
		_SurveyServiceGin_SurveyViewDelete_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/view/delete", "SurveyViewDelete")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/create",
		_SurveyServiceGin_SurveyGroupCreate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/create", "SurveyGroupCreate")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/detail",
		_SurveyServiceGin_SurveyGroupDetail_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/detail", "SurveyGroupDetail")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/update",
		_SurveyServiceGin_SurveyGroupUpdate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/update", "SurveyGroupUpdate")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/list",
		_SurveyServiceGin_SurveyGroupList_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/list", "SurveyGroupList")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/sub_update",
		_SurveyServiceGin_SurveyGroupSubUpdate_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/sub_update", "SurveyGroupSubUpdate")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/overwrite/send",
		_SurveyServiceGin_SurveyOverwriteSend_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/overwrite/send", "SurveyOverwriteSend")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/overwrite/sync",
		_SurveyServiceGin_SurveyOverwriteSync_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/overwrite/sync", "SurveyOverwriteSync")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/overwrite/send",
		_SurveyServiceGin_SurveyGroupOverwriteSend_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/overwrite/send", "SurveyGroupOverwriteSend")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/group/overwrite/sync",
		_SurveyServiceGin_SurveyGroupOverwriteSync_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/group/overwrite/sync", "SurveyGroupOverwriteSync")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/export/user-cluster-submit",
		_SurveyServiceGin_SurveyExportUserClusterSubmit_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/export/user-cluster-submit", "SurveyExportUserClusterSubmit")
	xgin.RegisterHandler(eng,
		"POST", "/v1/survey/export/headers",
		_SurveyServiceGin_SurveyExportHeaders_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/survey/export/headers", "SurveyExportHeaders")
}

func _SurveyServiceGin_Health_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/health",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Health(ctx, in.(*HealthRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(HealthRequest)
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Health(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyExportTaskList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/statistics/user-answer-record/survey-export/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyExportTaskList(ctx, in.(*SurveyExportTaskListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyExportTaskListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyExportTaskList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_CreateSurveyExportTask_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/survey-export/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CreateSurveyExportTask(ctx, in.(*SurveyExportTask))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyExportTask)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CreateSurveyExportTask(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_DelSurveyExportTask_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/survey-export/del",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DelSurveyExportTask(ctx, in.(*DelSurveyExportTaskReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(DelSurveyExportTaskReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DelSurveyExportTask(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_ResetSurveyExportTaskStatus_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/survey-export/resetStatus",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ResetSurveyExportTaskStatus(ctx, in.(*SurveyExportTaskDetailsReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyExportTaskDetailsReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ResetSurveyExportTaskStatus(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyRecordList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyRecordList(ctx, in.(*SurveyRecordListV2Request))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordListV2Request)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyRecordList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyRecordListV2_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/list-v2",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyRecordListV2(ctx, in.(*SurveyRecordListV2Request))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordListV2Request)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyRecordListV2(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_DelSurveyRecord_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DelSurveyRecord(ctx, in.(*SurveyRecordDetailsRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordDetailsRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DelSurveyRecord(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SetValidSurveyRecord_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/set-valid",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SetValidSurveyRecord(ctx, in.(*SurveyRecordsRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordsRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SetValidSurveyRecord(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SetInvalidSurveyRecord_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/user-answer-record/set-invalid",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SetInvalidSurveyRecord(ctx, in.(*SetValidSurveyRecordRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SetValidSurveyRecordRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SetInvalidSurveyRecord(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_InValidSurveyRecordList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/statistics/user-answer-record/invalid-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.InValidSurveyRecordList(ctx, in.(*SurveyRecordListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.InValidSurveyRecordList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyPreview_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/preview",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyPreview(ctx, in.(*SurveyPreviewReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyPreviewReq)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyPreview(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyRecordDetail_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/statistics/user-answer-record/detail",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyRecordDetail(ctx, in.(*SurveyRecordConfDetailsReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRecordConfDetailsReq)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyRecordDetail(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyList(ctx, in.(*SurveyListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_CreateSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CreateSurvey(ctx, in.(*CreateSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(CreateSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CreateSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyStatistics_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/rule/detail",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyStatistics(ctx, in.(*SurveyStatisticsRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyStatisticsRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyStatistics(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetSurveyStatisticsList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/statistics/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetSurveyStatisticsList(ctx, in.(*SurveyDetailRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyDetailRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetSurveyStatisticsList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyShow_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/show",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyShow(ctx, in.(*ShowSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ShowSurveyRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyShow(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_DeleteSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/logical-destory",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteSurvey(ctx, in.(*SurveyDelRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyDelRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_CopySurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/copy",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CopySurvey(ctx, in.(*SurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CopySurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SyncSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/sync",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SyncSurvey(ctx, in.(*SyncSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SyncSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SyncSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_ImpSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/imp",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ImpSurvey(ctx, in.(*ImpSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ImpSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ImpSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SetStatusSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/set-status",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SetStatusSurvey(ctx, in.(*SurveySetStatusRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveySetStatusRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SetStatusSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyRecycleList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/recycle-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyRecycleList(ctx, in.(*SurveyListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyRecycleList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_DeleteSurveyRecycle_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.DeleteSurveyRecycle(ctx, in.(*SurveyDelRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyDelRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.DeleteSurveyRecycle(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_RecoverSurveyRecycle_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/recover-logical-destory",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.RecoverSurveyRecycle(ctx, in.(*RecoverSurveyRecycleReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(RecoverSurveyRecycleReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.RecoverSurveyRecycle(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_ClearAllSurveyRecycle_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/clear-logical-destory",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ClearAllSurveyRecycle(ctx, in.(*ClearAllSurveyRecycleRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ClearAllSurveyRecycleRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ClearAllSurveyRecycle(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_RecoverAllSurveyRecycle_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/recover-all-logical-destory",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.RecoverAllSurveyRecycle(ctx, in.(*Survey))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(Survey)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.RecoverAllSurveyRecycle(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetDeliverList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/general/get-deliver-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetDeliverList(ctx, in.(*GetDeliverListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(GetDeliverListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetDeliverList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetZoneList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/general/get-zone-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetZoneList(ctx, in.(*GetZoneListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(GetZoneListRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetZoneList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_UpdateSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.UpdateSurvey(ctx, in.(*UpdateSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(UpdateSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.UpdateSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_PublishSurvey_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/publish",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.PublishSurvey(ctx, in.(*PublishSurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(PublishSurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.PublishSurvey(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetInputMethodList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/detail/input-method-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetInputMethodList(ctx, in.(*SurveyInputMethodListRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyInputMethodListRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetInputMethodList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyStatisticsDetail_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/detail",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyStatisticsDetail(ctx, in.(*SurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyStatisticsDetail(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyStatisticsDetailOld_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/statistics/detail2",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyStatisticsDetailOld(ctx, in.(*SurveyRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyStatisticsDetailOld(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetUserInfo_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/user/get-info",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetUserInfo(ctx, in.(*GetUserInfoRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(GetUserInfoRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetUserInfo(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_UserCheck_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/user/check",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.UserCheck(ctx, in.(*UserCheckRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(UserCheckRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.UserCheck(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_StatisticsUpdate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/rule/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.StatisticsUpdate(ctx, in.(*StatisticsUpdateRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(StatisticsUpdateRequest)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.StatisticsUpdate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetLatestSurveyBySurveyId_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/get-latest-survey-by-survey-id",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetLatestSurveyBySurveyId(ctx, in.(*GetLatestSurveyBySurveyIdRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(GetLatestSurveyBySurveyIdRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetLatestSurveyBySurveyId(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetValidRedeemConfigList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/settings/get-valid-redeem-config-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetValidRedeemConfigList(ctx, in.(*ValidRedeemConfigRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(ValidRedeemConfigRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetValidRedeemConfigList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_GetPreAwardTemplateList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/settings/get-pre-award-template-list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.GetPreAwardTemplateList(ctx, in.(*PreAwardTemplateRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(PreAwardTemplateRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.GetPreAwardTemplateList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_Upload_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/general/upload",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.Upload(ctx, in.(*UploadRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(UploadRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.Upload(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyViewCreate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/view/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyViewCreate(ctx, in.(*SurveyViewCreateReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyViewCreateReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyViewCreate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyViewList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/survey/view/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyViewList(ctx, in.(*SurveyViewListReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyViewListReq)
		if err := xgin.DecodeValues(in, c); err != nil {
			logger.Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyViewList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyViewUpdate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/view/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyViewUpdate(ctx, in.(*SurveyView))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyView)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyViewUpdate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyViewDelete_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/view/delete",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyViewDelete(ctx, in.(*SurveyViewDeleteReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyViewDeleteReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyViewDelete(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupCreate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/create",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupCreate(ctx, in.(*SurveyGroupCreateReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupCreateReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupCreate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupDetail_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/detail",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupDetail(ctx, in.(*SurveyGroupDetailReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupDetailReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupDetail(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupUpdate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupUpdate(ctx, in.(*SurveyGroupUpdateReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupUpdateReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupUpdate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupList_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupList(ctx, in.(*SurveyGroupListReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupListReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupList(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupSubUpdate_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/sub_update",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupSubUpdate(ctx, in.(*SurveyGroupSubUpdateReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupSubUpdateReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupSubUpdate(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyOverwriteSend_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/overwrite/send",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyOverwriteSend(ctx, in.(*SurveyOverwriteSendReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyOverwriteSendReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyOverwriteSend(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyOverwriteSync_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/overwrite/sync",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyOverwriteSync(ctx, in.(*SurveyOverwriteSyncReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyOverwriteSyncReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyOverwriteSync(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupOverwriteSend_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/overwrite/send",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupOverwriteSend(ctx, in.(*SurveyGroupOverwriteSendReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupOverwriteSendReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupOverwriteSend(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyGroupOverwriteSync_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/group/overwrite/sync",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyGroupOverwriteSync(ctx, in.(*SurveyGroupOverwriteSyncReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyGroupOverwriteSyncReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyGroupOverwriteSync(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyExportUserClusterSubmit_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/export/user-cluster-submit",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyExportUserClusterSubmit(ctx, in.(*SurveyExportUserClusterSubmitReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyExportUserClusterSubmitReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyExportUserClusterSubmit(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _SurveyServiceGin_SurveyExportHeaders_Handler(srv SurveyServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/survey/export/headers",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.SurveyExportHeaders(ctx, in.(*SurveyExportHeadersReq))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		logger := xlog.FromContext(ctx)
		in := new(SurveyExportHeadersReq)
		if c.Request == nil || c.Request.Body == nil {
			logger.Error("empty request body")
			return nil, ecode.BadRequest
		}
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in); err != nil {
			logger.Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			logger.Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.SurveyExportHeaders(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}
