syntax = "proto3";

package papegames.sparrow.survey;

import "google/api/field_behavior.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto";

option go_package = "survey/proto;proto";
option java_multiple_files = true;
option java_outer_classname = "SurveyProto";
option java_package = "com.papegames.sparrow.survey";

message SurveyRecordListRequest {
	// 当前页码，默认1
	int32 page = 1;
	// 每页条数，默认10
	int32 page_size = 2;
	// 答卷id
	int64 id = 3;
	// 平台账号id
	string openid = 4;
	//    玩家角色id
	string role_id = 5;
	// IP
	string ip = 6;
	// 设备id
	string device_id = 7;
	// sort_id
	string sort_id = 8;
	// sort_end_time
	string sort_end_time = 9;
	// sort_second
	string sort_second = 10;
	int64 survey_id = 11;
}

message SurveyRecordListV2Request {
	// 问卷ID
	int64 survey_id = 1 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
	// 客户端ID
	int64 client_id = 2 [(google.api.field_behavior) = REQUIRED];
	// 筛选器内容
	string view_content = 3;
	// limit
//	int32 limit = 4 [(google.api.field_behavior) = REQUIRED, (openapi.v3.property).minimum = 1];
	// 当前页码，默认1
	int32 page = 5;
	// 每页条数，默认50
	int32 page_size = 6;
}

message Cloumn {
	string name =1;
	string type =2;
}

message SurveyRecordListResponse {
	option (tagger.disable_omitempty) = true;
	repeated SurveyRecord list = 1;
	int64 total = 2;
}

message SurveyRecordsRequest {
	int64 id = 1;
	int64 survey_id = 2;
}

message SetValidSurveyRecordRequest {
	int64 survey_id = 1;
	repeated int64 ids = 2;
}

message SurveyRecordDetailsRequest {
	repeated int64 del_list = 1;
	int64 survey_id = 2;
}

message SurveyRecord {
	option (tagger.disable_omitempty) = true;

	// ID，自增主键，创建时此参数不需要传
	int64 id = 1 [
		(tagger.tags) = "gorm:primaryKey;type:int(11) default 0;comment:主键ID"
	];

	// 用户UID
	string uid = 2 [
		(tagger.tags) = "gorm:type:varchar(128);comment:用户UID",
		(openapi.v3.property) = {
			max_length: 128,
		},
		(tagger.tags) = "json:uid"
	];

	// 用户roleid
	//  string roleid = 3 [
	//    (tagger.tags) = "gorm:type:varchar(128);comment:用户roleid",
	//    (tagger.tags) = "json:roleid"
	//  ];

	// openid
	string openid = 4 [
		(tagger.tags) = "gorm:type:varchar(64);comment:openid",
		(openapi.v3.property) = {
			max_length: 125,
		},
		(tagger.tags) = "json:openid"
	];

	// 角色ID
	string role_id = 5 [
		(tagger.tags) = "gorm:type:varchar(64);comment:角色ID",
		(tagger.tags) = "json:role_id"
	];

	// 设备ID
	string device_id = 6 [
		(tagger.tags) = "gorm:type:varchar(128);comment:设备ID",
		(tagger.tags) = "json:device_id"
	];

	// ip
	string ip = 7 [
		(tagger.tags) = "gorm:type:varchar(64);comment:ip",
		(tagger.tags) = "json:ip"
	];

	// 记录是否有效
	int32 is_valid = 8 [
		(tagger.tags) = "gorm:type:tinyint(4);comment:记录是否有效（0: 有效，1: 无效）",
		(tagger.tags) = "json:is_valid"
	];

	// 是否删除
	int32 is_delete = 9 [
		(tagger.tags) = "gorm:type:tinyint(4);comment:记录是否删除（0: 未删除，1: 已删除）",
		(tagger.tags) = "json:is_delete"
	];

	// 答题开始时间
	string begin_time = 10 [
		(tagger.tags) = "json:begin_time"
	];

	// 答题结束时间
	string end_time = 11 [
		(tagger.tags) = "json:end_time"
	];

	// 额外信息
	string extra = 12 [
		(tagger.tags) = "json:extra"
	];

	// 创建时间
	string ctime = 13 [
		(google.api.field_behavior) = OUTPUT_ONLY,
		(tagger.tags) = "json:ctime,omitempty",
		(tagger.tags) = "gorm:autoCreateTime"
	];

	// 答题时间
	string second = 14 [
		(tagger.tags) = "gorm:-"
	];
}

message SurveyViewCreateReq {
	 // 答卷ID
	 int64 survey_id = 1;
	 // 过滤器名称
	 string name = 2;
	 // 过滤期内容
	 string content = 3;
	 // 视图类型，1：回收列表，2：交叉分析
	 int32 kind = 4;
}

message SurveyViewCreateRes {
	int64 id = 1;
}

message SurveyView {
	option (tagger.disable_omitempty) = true;

	// ID，自增主键，创建时此参数不需要传
	int64 id = 1 [
		(tagger.tags) = "gorm:primaryKey;type:int(11) default 0;comment:过滤器ID"
	];

	// 问卷ID
	int64 survey_id = 2 [
		(tagger.tags) = "gorm:type:int(11);comment:问卷ID",
		(tagger.tags) = "json:survey_id"
	];

	// 筛选器名
	string name = 3 [
		(tagger.tags) = "gorm:type:varchar(64);comment:筛选器名",
		(openapi.v3.property) = {
			max_length: 50,
		},
		(tagger.tags) = "json:name"
	];

	// 筛选器内容
	string content = 4 [
		(tagger.tags) = "gorm:type:string;comment:筛选器内容",
		(tagger.tags) = "json:content"
	];

	// 是否删除
	int32 is_delete = 5 [
		(tagger.tags) = "gorm:type:int(11);comment:是否删除，0：未删除，1：删除",
		(tagger.tags) = "json:is_delete,omitempty"
	];

	// 视图类型
	int32 kind = 6 [
		(tagger.tags) = "gorm:type:int(11);comment:视图类型，1：回收列表，2：交叉分析",
		(tagger.tags) = "json:kind"
	];

	// 创建时间
	string ctime = 7 [
		(google.api.field_behavior) = OUTPUT_ONLY,
		(tagger.tags) = "json:ctime,omitempty",
		(tagger.tags) = "gorm:ctime"
	];

	// 修改时间
	string mtime = 8 [
		(google.api.field_behavior) = OUTPUT_ONLY,
		(tagger.tags) = "json:-",
		(tagger.tags) = "gorm:mtime"
	];

	// 创建人
	string creator = 9 [
		(google.api.field_behavior) = OUTPUT_ONLY,
		(tagger.tags) = "json:-",
		(tagger.tags) = "gorm:creator"
	];

	// 最近修改人
	string editor = 10 [
		(google.api.field_behavior) = OUTPUT_ONLY,
		(tagger.tags) = "json:-",
		(tagger.tags) = "gorm:editor"
	];

}

message SurveyViewListReq {
	// 答卷ID
	int64 survey_id = 1;
	// 视图类型，1：回收列表，2：交叉分析
	int32 kind  =  2;
}

message SurveyViewListRes {
	// 筛选器列表
	repeated SurveyView list = 1;
}

message SurveyViewDeleteReq {
	// 要删除的筛选器ID列表
	repeated int64 ids = 1;
}
