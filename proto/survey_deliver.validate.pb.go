// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: proto/survey_deliver.proto

package proto

func (x *GetDeliverListRequest) Validate() error {
	return nil
}

func (x *GetDeliverListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDeliverListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *Deliver) Validate() error {
	return nil
}

func (x *GetZoneListRequest) Validate() error {
	return nil
}

func (x *GetZoneListResponse) Validate() error {
	for _, item := range x.GetList() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetZoneListResponseValidationError{
					field:   "List",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *DataItem) Validate() error {
	return nil
}

type GetDeliverListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetDeliverListRequestValidationError) Field() string { return e.field }

func (e GetDeliverListRequestValidationError) Reason() string { return e.reason }

func (e GetDeliverListRequestValidationError) Message() string { return e.message }

func (e GetDeliverListRequestValidationError) Cause() error { return e.cause }

func (e GetDeliverListRequestValidationError) ErrorName() string {
	return "GetDeliverListRequestValidationError"
}

func (e GetDeliverListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetDeliverListRequest." + e.field + ": " + e.message + cause
}

type GetDeliverListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetDeliverListResponseValidationError) Field() string { return e.field }

func (e GetDeliverListResponseValidationError) Reason() string { return e.reason }

func (e GetDeliverListResponseValidationError) Message() string { return e.message }

func (e GetDeliverListResponseValidationError) Cause() error { return e.cause }

func (e GetDeliverListResponseValidationError) ErrorName() string {
	return "GetDeliverListResponseValidationError"
}

func (e GetDeliverListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetDeliverListResponse." + e.field + ": " + e.message + cause
}

type DeliverValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DeliverValidationError) Field() string { return e.field }

func (e DeliverValidationError) Reason() string { return e.reason }

func (e DeliverValidationError) Message() string { return e.message }

func (e DeliverValidationError) Cause() error { return e.cause }

func (e DeliverValidationError) ErrorName() string { return "DeliverValidationError" }

func (e DeliverValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Deliver." + e.field + ": " + e.message + cause
}

type GetZoneListRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetZoneListRequestValidationError) Field() string { return e.field }

func (e GetZoneListRequestValidationError) Reason() string { return e.reason }

func (e GetZoneListRequestValidationError) Message() string { return e.message }

func (e GetZoneListRequestValidationError) Cause() error { return e.cause }

func (e GetZoneListRequestValidationError) ErrorName() string {
	return "GetZoneListRequestValidationError"
}

func (e GetZoneListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetZoneListRequest." + e.field + ": " + e.message + cause
}

type GetZoneListResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e GetZoneListResponseValidationError) Field() string { return e.field }

func (e GetZoneListResponseValidationError) Reason() string { return e.reason }

func (e GetZoneListResponseValidationError) Message() string { return e.message }

func (e GetZoneListResponseValidationError) Cause() error { return e.cause }

func (e GetZoneListResponseValidationError) ErrorName() string {
	return "GetZoneListResponseValidationError"
}

func (e GetZoneListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid GetZoneListResponse." + e.field + ": " + e.message + cause
}

type DataItemValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e DataItemValidationError) Field() string { return e.field }

func (e DataItemValidationError) Reason() string { return e.reason }

func (e DataItemValidationError) Message() string { return e.message }

func (e DataItemValidationError) Cause() error { return e.cause }

func (e DataItemValidationError) ErrorName() string { return "DataItemValidationError" }

func (e DataItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid DataItem." + e.field + ": " + e.message + cause
}
